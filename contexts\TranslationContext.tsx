import { createContext, useContext } from 'react';
import { useLanguage } from './LanguageContext';
import { getTranslations } from '../utils/translations';

// Tipo per il contesto delle traduzioni
type TranslationContextType = {
  t: any; // Oggetto con tutte le traduzioni
  currentLanguage: {
    code: string;
    name: string;
    nativeName: string;
    flag: string;
  };
};

// Creazione del contesto
const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

// Hook per utilizzare il contesto delle traduzioni
export const useTranslations = () => {
  const context = useContext(TranslationContext);
  if (!context) {
    throw new Error('useTranslations must be used within a TranslationProvider');
  }
  return context;
};

export default TranslationContext;
