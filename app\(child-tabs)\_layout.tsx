import React from 'react';
import { Tabs } from 'expo-router';
import { FontAwesome5 } from '@expo/vector-icons';
import { useTranslations } from '../../contexts/TranslationContext';
import { useAuth } from '../../contexts/AuthContext';
import { startLocationTracking } from '../../utils/location';




// Componente che avvia i servizi dopo che i provider sono stati inizializzati
function ChildTabsWithServices() {
  const { user } = useAuth();
  const { t } = useTranslations();

  // Avvio del tracking della posizione quando l'app del bambino si carica
  React.useEffect(() => {
    if (user) {
      console.log('[ChildTabsLayout] Starting location tracking for child:', user.id);

      // Avvio del tracking della posizione
      startLocationTracking(user.id)
        .then(() => console.log('[ChildTabsLayout] Location tracking started successfully'))
        .catch(error => console.error('[ChildTabsLayout] Error starting location tracking:', error));
    }
  }, [user]);

  return (
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: '#4A90E2',
          tabBarInactiveTintColor: '#8E8E93',
          tabBarStyle: {
            backgroundColor: '#FFFFFF',
            borderTopWidth: 1,
            borderTopColor: '#E5E5E5',
            paddingBottom: 5,
            paddingTop: 5,
            height: 60,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: -2,
            },
            shadowOpacity: 0.1,
            shadowRadius: 3,
            elevation: 5,
          },
          tabBarLabelStyle: {
            fontSize: 12,
            fontWeight: '500',
          },
          headerShown: false,
        }}
      >
      <Tabs.Screen
        name="dashboard"
        options={{
          tabBarLabel: t.navigation?.dashboard || 'Home',
          tabBarIcon: ({ color, size }) => <FontAwesome5 name="home" size={size * 0.9} color={color} />,
        }}
      />
      <Tabs.Screen
        name="sos"
        options={{
          tabBarLabel: t.navigation?.sos || 'SOS',
          tabBarIcon: ({ color, size }) => <FontAwesome5 name="shield-alt" size={size * 0.9} color={color} />,
        }}
      />

      <Tabs.Screen
        name="homework-helper"
        options={{
          tabBarLabel: t.homework?.title || 'Compiti',
          tabBarIcon: ({ color, size }) => <FontAwesome5 name="graduation-cap" size={size * 0.9} color={color} />,
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          tabBarLabel: t.settings?.title || 'Impostazioni',
          tabBarIcon: ({ color, size }) => <FontAwesome5 name="user-cog" size={size * 0.9} color={color} />,
        }}
      />
      </Tabs>
  );
}

export default function ChildTabsLayout() {
  return <ChildTabsWithServices />;
}