import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import SystemPermissionHandler from '../common/SystemPermissionHandler';

interface NotificationAccessPermissionSlideProps {
  onPermissionGranted: () => void;
}

const NotificationAccessPermissionSlide: React.FC<NotificationAccessPermissionSlideProps> = ({ onPermissionGranted }) => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Accesso alle Notifiche</Text>
        <Text style={styles.subtitle}>
          Per ricevere notifiche importanti, è necessario concedere l'accesso alle notifiche.
        </Text>
      </View>

      <SystemPermissionHandler
        permissionType="notificationAccess"
        onPermissionGranted={onPermissionGranted}
        buttonText="Concedi Accesso"
        title="Accesso alle Notifiche"
        description="Per ricevere avvisi importanti quando entri o esci da una zona sicura, KidSafety ha bisogno dell'accesso alle notifiche."
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    lineHeight: 22,
  },
});

export default NotificationAccessPermissionSlide;
