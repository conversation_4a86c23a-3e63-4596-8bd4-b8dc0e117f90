import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import PermissionOnboardingScreen from '../../components/child/PermissionOnboardingScreen';
import { useAuth } from '../../contexts/AuthContext';
import LoadingIndicator from '../../components/shared/LoadingIndicator';

export default function ChildOnboardingScreen() {
  const router = useRouter();
  const { user, isLoading } = useAuth();

  // If no user is logged in, redirect to login
  if (!user && !isLoading) {
    router.replace('/auth/child-login');
    return null;
  }

  if (isLoading) {
    return <LoadingIndicator fullScreen text="Loading..." />;
  }

  return (
    <View style={styles.container}>
      <PermissionOnboardingScreen />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});
