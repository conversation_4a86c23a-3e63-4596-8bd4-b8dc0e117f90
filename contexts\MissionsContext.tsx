﻿import React, { createContext, useState, useEffect, useContext } from 'react';
import { useAuth } from './AuthContext';
import {
  createMission,
  assignMissionToChild,
  getChildMissions as getMissionsForChild,
  updateMissionStatus,
  supabase
} from '../utils/supabase';

// Define mission types
export type Mission = {
  id: string;
  title: string;
  description: string;
  reward?: string;
  due_date?: string;
  created_at: string;
  parent_id: string;
};

export type MissionStatus = 'pending' | 'in_progress' | 'completed' | 'verified';

export type MissionAssignment = {
  id: string;
  mission_id: string;
  child_id: string;
  status: MissionStatus;
  completion_date?: string;
  mission?: Mission;
};

type MissionsContextType = {
  missions: Mission[];
  childMissions: MissionAssignment[];
  isLoading: boolean;
  error: string | null;
  createNewMission: (missionData: Partial<Mission>) => Promise<Mission>;
  assignMission: (missionId: string, childId: string) => Promise<MissionAssignment>;
  updateStatus: (assignmentId: string, status: MissionAssignment['status']) => Promise<MissionAssignment>;
  refreshMissions: () => Promise<void>;
  clearError: () => void;
};

// Create the context
const MissionsContext = createContext<MissionsContextType>({
  missions: [],
  childMissions: [],
  isLoading: false,
  error: null,
  createNewMission: async () => ({ id: '', title: '', description: '', created_at: '', parent_id: '' }),
  assignMission: async () => ({ id: '', mission_id: '', child_id: '', status: 'pending' }),
  updateStatus: async () => ({ id: '', mission_id: '', child_id: '', status: 'pending' }),
  refreshMissions: async () => {},
  clearError: () => {},
});

// Provider component
export const MissionsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, userType } = useAuth();
  const [missions, setMissions] = useState<Mission[]>([]);
  const [childMissions, setChildMissions] = useState<MissionAssignment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load missions based on user type
  useEffect(() => {
    if (user) {
      refreshMissions();
    }
  }, [user, userType]);

  // Refresh missions
  const refreshMissions = async () => {
    if (!user) {
      console.log('No user found in refreshMissions');
      return;
    }

    console.log('Refreshing missions for user:', user.id, 'userType:', userType);
    console.log('User details:', JSON.stringify(user));

    try {
      setIsLoading(true);

      if (userType === 'child') {
        // Load child's missions
        console.log('Loading missions for child:', user.id);

        // Get missions directly from the database
        console.log('Getting missions directly from the database');
        try {
          // Use a direct SQL query to get missions with their details
          const { data: directData, error: directError } = await supabase.rpc(
            'get_child_missions_with_details',
            { p_child_id: user.id }
          );

          if (directError) {
            console.error('Error fetching missions directly:', directError);
          } else {
            console.log('Direct missions found:', directData?.length || 0);
            if (directData && directData.length > 0) {
              console.log('First direct mission:', JSON.stringify(directData[0]));

              // Transform the data to match the expected format
              const transformedMissions = directData.map(item => ({
                id: item.assignment_id,
                mission_id: item.mission_id,
                child_id: user.id,
                status: item.status,
                completion_date: item.completion_date,
                mission: {
                  id: item.mission_id,
                  title: item.title,
                  description: item.description,
                  reward: item.reward,
                  due_date: item.due_date,
                  created_at: item.created_at,
                  parent_id: item.parent_id
                }
              }));

              console.log('Transformed missions:', transformedMissions.length);
              console.log('First transformed mission:', JSON.stringify(transformedMissions[0]));

              // Set the missions directly
              setChildMissions(transformedMissions);
              console.log('Missions set directly in state');
              setIsLoading(false);
              return;
            }
          }
        } catch (directErr) {
          console.error('Error in direct mission fetch:', directErr);
        }

        // Fall back to the original method if direct fetch fails
        const missionAssignments = await getMissionsForChild(user.id);
        console.log('Child missions loaded count:', missionAssignments?.length || 0);
        if (missionAssignments && missionAssignments.length > 0) {
          console.log('First mission assignment:', missionAssignments[0]);
          console.log('Missions by status:', {
            pending: missionAssignments.filter(m => m.status === 'pending').length,
            in_progress: missionAssignments.filter(m => m.status === 'in_progress').length,
            completed: missionAssignments.filter(m => m.status === 'completed').length,
            verified: missionAssignments.filter(m => m.status === 'verified').length
          });
        }
        setChildMissions(missionAssignments);
      } else if (userType === 'parent') {
        // For parent, we'll need to implement a function to get all parent's missions
        // This would be implemented in supabase.ts
        console.log('Parent missions not implemented yet');
        /*
        const parentMissions = await getParentMissions(user.id);
        setMissions(parentMissions);
        */
      }
    } catch (err: any) {
      console.error('Error refreshing missions:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new mission (parent only)
  const createNewMission = async (missionData: Partial<Mission>): Promise<Mission> => {
    try {
      setIsLoading(true);

      if (!user || userType !== 'parent') {
        throw new Error('Only parents can create missions');
      }

      const mission = await createMission(user.id, missionData);

      // Add to local state
      setMissions(prev => [...prev, mission]);

      return mission;
    } catch (err: any) {
      console.error('Error creating mission:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Assign a mission to a child (parent only)
  const assignMission = async (missionId: string, childId: string): Promise<MissionAssignment> => {
    try {
      setIsLoading(true);

      if (!user || userType !== 'parent') {
        throw new Error('Only parents can assign missions');
      }

      const assignment = await assignMissionToChild(missionId, childId);
      return assignment;
    } catch (err: any) {
      console.error('Error assigning mission:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Update mission status (both parent and child)
  const updateStatus = async (
    assignmentId: string,
    status: MissionAssignment['status']
  ): Promise<MissionAssignment> => {
    try {
      setIsLoading(true);

      // Validate that a child can only update to 'in_progress' or 'completed'
      if (userType === 'child' && status === 'verified') {
        throw new Error('Children cannot verify missions');
      }

      // Validate that only a parent can verify a mission
      if (status === 'verified' && userType !== 'parent') {
        throw new Error('Only parents can verify missions');
      }

      const updatedAssignment = await updateMissionStatus(assignmentId, status);

      // Update local state
      if (userType === 'child') {
        setChildMissions(prev =>
          prev.map(item =>
            item.id === assignmentId ? updatedAssignment : item
          )
        );
      }

      return updatedAssignment;
    } catch (err: any) {
      console.error('Error updating mission status:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Clear error
  const clearError = () => setError(null);

  return (
    <MissionsContext.Provider
      value={{
        missions,
        childMissions,
        isLoading,
        error,
        createNewMission,
        assignMission,
        updateStatus,
        refreshMissions,
        clearError,
      }}
    >
      {children}
    </MissionsContext.Provider>
  );
};

// Custom hook to use the missions context
export const useMissions = () => {
  const context = useContext(MissionsContext);

  if (!context) {
    throw new Error('useMissions must be used within a MissionsProvider');
  }

  return context;
};
