/**
 * Jest Setup per KidSafety
 * Configurazione globale per i test - Versione Semplificata
 */

// Mock semplificato per funzioni di crittografia (senza expo-crypto)
global.mockCrypto = {
  getRandomBytes: jest.fn().mockReturnValue(new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8])),
  hash: jest.fn().mockReturnValue('mocked_hash_value'),
  encrypt: jest.fn().mockReturnValue('encrypted_data'),
  decrypt: jest.fn().mockReturnValue('decrypted_data')
};

// Mock per expo-secure-store
jest.mock('expo-secure-store', () => ({
  getItemAsync: jest.fn().mockResolvedValue('mocked_secure_key'),
  setItemAsync: jest.fn().mockResolvedValue(undefined),
  deleteItemAsync: jest.fn().mockResolvedValue(undefined),
  isAvailableAsync: jest.fn().mockResolvedValue(true)
}));

// Mock per react-native
jest.mock('react-native', () => {
  return {
    NativeModules: {
      AppControlModule: {
        blockApp: jest.fn().mockResolvedValue(true),
        unblockApp: jest.fn().mockResolvedValue(true),
        setTimeLimit: jest.fn().mockResolvedValue(true),
        getBlockedApps: jest.fn().mockResolvedValue([]),
        isAppBlocked: jest.fn().mockResolvedValue(false),
        checkPermissions: jest.fn().mockResolvedValue({
          usageStats: true,
          systemAlertWindow: true
        }),
        startMonitoring: jest.fn().mockResolvedValue(true),
        stopMonitoring: jest.fn().mockResolvedValue(true),
        requestPermissions: jest.fn().mockResolvedValue(true)
      },
      BillingModule: {
        initializeBilling: jest.fn().mockResolvedValue(true),
        purchaseProduct: jest.fn().mockResolvedValue({ success: true }),
        getAvailableProducts: jest.fn().mockResolvedValue([]),
        restorePurchases: jest.fn().mockResolvedValue([])
      }
    },
    NativeEventEmitter: jest.fn().mockImplementation(() => ({
      addListener: jest.fn(),
      removeListener: jest.fn(),
      removeAllListeners: jest.fn(),
      emit: jest.fn()
    })),
    DeviceEventEmitter: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
      removeAllListeners: jest.fn(),
      emit: jest.fn()
    },
    Platform: {
      OS: 'android',
      Version: 30,
      select: jest.fn((obj) => obj.android || obj.default)
    }
  };
});

// Mock per @react-native-async-storage/async-storage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn().mockResolvedValue(null),
  setItem: jest.fn().mockResolvedValue(undefined),
  removeItem: jest.fn().mockResolvedValue(undefined),
  clear: jest.fn().mockResolvedValue(undefined),
  getAllKeys: jest.fn().mockResolvedValue([]),
  multiGet: jest.fn().mockResolvedValue([]),
  multiSet: jest.fn().mockResolvedValue(undefined),
  multiRemove: jest.fn().mockResolvedValue(undefined)
}));

// Mock per Supabase
jest.mock('./utils/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({
      data: { id: '1', test: 'data' },
      error: null
    }),
    auth: {
      signUp: jest.fn().mockResolvedValue({ data: {}, error: null }),
      signIn: jest.fn().mockResolvedValue({ data: {}, error: null }),
      signOut: jest.fn().mockResolvedValue({ error: null }),
      getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null })
    }
  }
}));

// Mock per config
jest.mock('./config', () => ({
  default: {
    supabaseUrl: 'https://test.supabase.co',
    supabaseAnonKey: 'test-anon-key',
    anthropicApiKey: 'test-anthropic-key',
    googlePlayPublicKey: 'test-google-play-key'
  }
}));

// Mock per expo-location
jest.mock('expo-location', () => ({
  requestForegroundPermissionsAsync: jest.fn().mockResolvedValue({ status: 'granted' }),
  requestBackgroundPermissionsAsync: jest.fn().mockResolvedValue({ status: 'granted' }),
  getCurrentPositionAsync: jest.fn().mockResolvedValue({
    coords: {
      latitude: 45.4642,
      longitude: 9.1900,
      accuracy: 10
    }
  }),
  watchPositionAsync: jest.fn().mockResolvedValue({
    remove: jest.fn()
  })
}));

// Mock per expo-notifications
jest.mock('expo-notifications', () => ({
  requestPermissionsAsync: jest.fn().mockResolvedValue({ status: 'granted' }),
  scheduleNotificationAsync: jest.fn().mockResolvedValue('notification-id'),
  cancelNotificationAsync: jest.fn().mockResolvedValue(undefined),
  setNotificationHandler: jest.fn()
}));

// Configurazione globale per i test
global.console = {
  ...console,
  // Silenzia i log durante i test (opzionale)
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Timeout per i test asincroni
jest.setTimeout(10000);

// Mock per TextEncoder/TextDecoder (necessari per alcuni test di crittografia)
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock per fetch (se necessario)
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve('')
  })
);

console.log('🧪 Jest setup completato per KidSafety tests');
