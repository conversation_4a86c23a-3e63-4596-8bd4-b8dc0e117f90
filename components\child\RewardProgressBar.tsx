import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Easing,
  TouchableOpacity,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { supabase } from '../../utils/supabase';

interface RewardProgressBarProps {
  childId: string;
  onPress?: () => void;
}

interface RewardInfo {
  rewardId: string | null;
  rewardName: string;
  starsRequired: number;
  isAchieved: boolean;
  totalStars: number;
}

const RewardProgressBar: React.FC<RewardProgressBarProps> = ({ childId, onPress }) => {
  const [rewardInfo, setRewardInfo] = useState<RewardInfo>({
    rewardId: null,
    rewardName: 'Loading...',
    starsRequired: 100,
    isAchieved: false,
    totalStars: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const progressAnim = useState(new Animated.Value(0))[0];

  useEffect(() => {
    fetchRewardInfo();

    // Set up real-time subscription for mission verifications
    const subscription = supabase
      .channel('mission_verification_changes')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'mission_assignments',
        filter: `child_id=eq.${childId} AND status=eq.verified`,
      }, () => {
        // Refresh reward info when a mission is verified
        fetchRewardInfo();
      })
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [childId]);

  useEffect(() => {
    // Animate progress bar when data changes
    if (!loading) {
      const progress = Math.min(rewardInfo.totalStars / rewardInfo.starsRequired, 1);
      Animated.timing(progressAnim, {
        toValue: progress,
        duration: 1000,
        easing: Easing.elastic(1),
        useNativeDriver: false,
      }).start();
    }
  }, [rewardInfo, loading]);

  const fetchRewardInfo = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase.rpc('get_child_current_reward', {
        p_child_id: childId,
      });

      if (error) {
        console.error('Error fetching reward info:', error);
        setError('Failed to load reward information');
      } else if (data && data.length > 0) {
        const reward = data[0];
        setRewardInfo({
          rewardId: reward.reward_id,
          rewardName: reward.reward_name,
          starsRequired: reward.stars_required,
          isAchieved: reward.is_achieved,
          totalStars: reward.total_stars,
        });
        setError(null);
      }
    } catch (err) {
      console.error('Exception fetching reward info:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  const progressColor = progressAnim.interpolate({
    inputRange: [0, 0.3, 0.7, 1],
    outputRange: ['#FF9800', '#2196F3', '#4CAF50', '#8E44AD'],
  });

  const getProgressText = () => {
    if (rewardInfo.totalStars >= rewardInfo.starsRequired) {
      return 'Reward achieved! 🎉';
    }
    return `${rewardInfo.totalStars} / ${rewardInfo.starsRequired} stars`;
  };

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={onPress ? 0.7 : 1}
    >
      <View style={styles.headerContainer}>
        <View style={styles.titleContainer}>
          <FontAwesome5 name="star" size={16} color="#FFD700" />
          <Text style={styles.title}>Star Progress</Text>
        </View>
        <Text style={styles.progressText}>{getProgressText()}</Text>
      </View>

      <View style={styles.progressContainer}>
        <Animated.View
          style={[
            styles.progressBar,
            {
              width: progressWidth,
              backgroundColor: progressColor,
            },
          ]}
        />
        <View style={styles.rewardIconContainer}>
          <FontAwesome5 name="gift" size={20} color="#8E44AD" />
        </View>
      </View>

      <Text style={styles.rewardText}>
        Reward: <Text style={styles.rewardName}>{rewardInfo.rewardName}</Text>
      </Text>

      {error && <Text style={styles.errorText}>{error}</Text>}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    color: '#333',
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  progressContainer: {
    height: 24,
    backgroundColor: '#F0F0F0',
    borderRadius: 12,
    marginVertical: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  progressBar: {
    height: '100%',
    borderRadius: 12,
  },
  rewardIconContainer: {
    position: 'absolute',
    right: 4,
    top: 2,
    backgroundColor: 'white',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rewardText: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  rewardName: {
    fontWeight: 'bold',
    color: '#8E44AD',
  },
  errorText: {
    color: '#E74C3C',
    fontSize: 12,
    marginTop: 4,
  },
});

export default RewardProgressBar;
