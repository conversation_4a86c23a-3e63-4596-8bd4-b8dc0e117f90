import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const PermissionGuideScreen = () => {
  const navigation = useNavigation();

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Image
          source={require('../../assets/permissions-guide.png')}
          style={styles.image}
          defaultSource={require('../../assets/permissions-guide-placeholder.png')}
        />

        <Text style={styles.title}>Guida alle autorizzazioni</Text>

        <Text style={styles.subtitle}>
          Per garantire che KidSafety funzioni correttamente sul dispositivo di tuo figlio,
          segui questa guida per configurare le autorizzazioni di posizione.
        </Text>

        <View style={styles.card}>
          <Text style={styles.cardTitle}>Perché servono queste autorizzazioni?</Text>
          <Text style={styles.cardText}>
            KidSafety ha bisogno di accedere alla posizione del dispositivo di tuo figlio
            anche quando l'app è in background (non aperta) per garantire:
          </Text>

          <View style={styles.bulletPoint}>
            <Ionicons name="checkmark-circle" size={20} color="#4630EB" />
            <Text style={styles.bulletText}>Monitoraggio costante della posizione</Text>
          </View>

          <View style={styles.bulletPoint}>
            <Ionicons name="checkmark-circle" size={20} color="#4630EB" />
            <Text style={styles.bulletText}>Funzionamento delle zone sicure (geofence)</Text>
          </View>

          <View style={styles.bulletPoint}>
            <Ionicons name="checkmark-circle" size={20} color="#4630EB" />
            <Text style={styles.bulletText}>Notifiche quando tuo figlio entra o esce da una zona sicura</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Configurazione su {Platform.OS === 'ios' ? 'iOS' : 'Android'}</Text>

        {Platform.OS === 'ios' ? (
          <View style={styles.steps}>
            <View style={styles.step}>
              <View style={styles.stepNumberContainer}>
                <Text style={styles.stepNumber}>1</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Apri le Impostazioni</Text>
                <Text style={styles.stepText}>
                  Sul dispositivo di tuo figlio, vai su Impostazioni → Privacy e sicurezza → Servizi di localizzazione
                </Text>
              </View>
            </View>

            <View style={styles.step}>
              <View style={styles.stepNumberContainer}>
                <Text style={styles.stepNumber}>2</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Trova KidSafety</Text>
                <Text style={styles.stepText}>
                  Scorri l'elenco delle app e seleziona "KidSafety"
                </Text>
              </View>
            </View>

            <View style={styles.step}>
              <View style={styles.stepNumberContainer}>
                <Text style={styles.stepNumber}>3</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Imposta "Sempre"</Text>
                <Text style={styles.stepText}>
                  Seleziona l'opzione "Sempre" per consentire all'app di utilizzare la posizione anche in background
                </Text>
              </View>
            </View>
          </View>
        ) : (
          <View style={styles.steps}>
            <View style={styles.step}>
              <View style={styles.stepNumberContainer}>
                <Text style={styles.stepNumber}>1</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Apri le Impostazioni</Text>
                <Text style={styles.stepText}>
                  Sul dispositivo di tuo figlio, vai su Impostazioni → App → KidSafety
                </Text>
              </View>
            </View>

            <View style={styles.step}>
              <View style={styles.stepNumberContainer}>
                <Text style={styles.stepNumber}>2</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Seleziona Autorizzazioni</Text>
                <Text style={styles.stepText}>
                  Tocca "Autorizzazioni" e poi "Posizione"
                </Text>
              </View>
            </View>

            <View style={styles.step}>
              <View style={styles.stepNumberContainer}>
                <Text style={styles.stepNumber}>3</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Consenti sempre</Text>
                <Text style={styles.stepText}>
                  Seleziona "Consenti sempre" o "Consenti tutto il tempo"
                </Text>
              </View>
            </View>

            <View style={styles.step}>
              <View style={styles.stepNumberContainer}>
                <Text style={styles.stepNumber}>4</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Disattiva l'ottimizzazione della batteria</Text>
                <Text style={styles.stepText}>
                  In Impostazioni → App → KidSafety → Batteria, seleziona "Nessuna restrizione"
                </Text>
              </View>
            </View>
          </View>
        )}

        <View style={styles.tipBox}>
          <Ionicons name="bulb-outline" size={24} color="#FF9500" />
          <View style={styles.tipContent}>
            <Text style={styles.tipTitle}>Suggerimento</Text>
            <Text style={styles.tipText}>
              Su alcuni dispositivi Android, potresti dover disattivare anche altre ottimizzazioni
              della batteria nelle impostazioni del dispositivo per garantire che KidSafety
              funzioni correttamente in background.
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={styles.button}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.buttonText}>Ho capito</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  content: {
    padding: 20,
  },
  image: {
    width: '100%',
    height: 180,
    resizeMode: 'contain',
    marginBottom: 24,
    alignSelf: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#4630EB',
  },
  cardText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 12,
    lineHeight: 22,
  },
  bulletPoint: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bulletText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  steps: {
    marginBottom: 24,
  },
  step: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  stepNumberContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#4630EB',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumber: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  stepText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  tipBox: {
    backgroundColor: '#FFF9E6',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderLeftWidth: 4,
    borderLeftColor: '#FF9500',
  },
  tipContent: {
    flex: 1,
    marginLeft: 12,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#FF9500',
  },
  tipText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  button: {
    backgroundColor: '#4630EB',
    paddingVertical: 14,
    borderRadius: 30,
    alignItems: 'center',
    marginTop: 8,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default PermissionGuideScreen;