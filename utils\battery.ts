import { Platform } from 'react-native';
import { isBatteryOptimizationDisabledNative } from './systemSettingsNative';

/**
 * Verifica se l'ottimizzazione della batteria è disattivata per l'app
 * @returns Promise<boolean> - true se l'ottimizzazione della batteria è disattivata
 */
export const isBatteryOptimizationDisabled = async (): Promise<boolean> => {
  try {
    // Su iOS, l'ottimizzazione della batteria non è un problema
    if (Platform.OS !== 'android') {
      return true;
    }

    // Utilizziamo il modulo nativo per verificare lo stato
    const isDisabled = await isBatteryOptimizationDisabledNative();
    console.log('[Battery] Battery optimization disabled:', isDisabled);
    return isDisabled;
  } catch (error) {
    console.error('[Battery] Error checking battery optimization status:', error);
    // In caso di errore, assumiamo che l'ottimizzazione sia attiva
    return false;
  }
};

/**
 * <PERSON><PERSON>ene lo stato corrente della batteria
 * @returns Promise<number> - percentuale di batteria (0-100)
 */
export const getBatteryLevel = async (): Promise<number> => {
  try {
    // Questa è solo un'implementazione di esempio
    // In una implementazione reale, utilizzare expo-battery o un modulo nativo
    return 100;
  } catch (error) {
    console.error('[Battery] Error getting battery level:', error);
    return 0;
  }
};

/**
 * Verifica se il dispositivo è in carica
 * @returns Promise<boolean> - true se il dispositivo è in carica
 */
export const isCharging = async (): Promise<boolean> => {
  try {
    // Questa è solo un'implementazione di esempio
    // In una implementazione reale, utilizzare expo-battery o un modulo nativo
    return false;
  } catch (error) {
    console.error('[Battery] Error checking charging status:', error);
    return false;
  }
}; 