import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import AuthForm from '../../components/auth/AuthForm';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import { useTranslations } from '../../contexts/TranslationContext';
import { Ionicons } from '@expo/vector-icons';

export default function ParentLoginScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { signIn, error, isLoading, subscriptionStatus, refreshSubscriptionStatus } = useAuth();
  const { t } = useTranslations();
  const [isCheckingSubscription, setIsCheckingSubscription] = useState(false);

  // Handle reset parameter to prevent going back after logout
  useEffect(() => {
    if (params.reset === 'true') {
      console.log('Reset parameter detected in parent login, preventing back navigation');
    }
  }, [params]);

  // Handle form submission
  const handleLogin = async (values: Record<string, string>) => {
    try {
      setIsCheckingSubscription(true);

      // Sign in the user
      await signIn(values.email, values.password);

      // Refresh subscription status
      await refreshSubscriptionStatus();

      // Check if user has an active subscription
      if (!subscriptionStatus || !subscriptionStatus.hasSubscription) {
        // If no subscription, show trial info screen
        console.log('No active subscription found, showing trial info');
        router.replace('/auth/trial-info');
      } else {
        // If subscription exists, go to home
        console.log('Active subscription found, going to home');
        router.replace('/(parent-tabs)/home');
      }
    } catch (err) {
      // Error is handled by the AuthContext
      console.log('Login error');
    } finally {
      setIsCheckingSubscription(false);
    }
  };



  // Navigate to child login
  const navigateToChildLogin = () => {
    router.push('/auth/child-login');
  };

  // Navigate to signup
  const navigateToSignup = () => {
    router.push('/auth/parent-signup');
  };

  if (isLoading || isCheckingSubscription) {
    return <LoadingIndicator fullScreen text={isCheckingSubscription ? (t.subscription?.checking || "Checking subscription...") : "Logging in..."} />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.logoContainer}>
        <View style={styles.logoIcon}>
          <Ionicons name="shield-checkmark" size={64} color="#4630EB" />
        </View>
        <Text style={styles.appName}>KidSafety</Text>
        <Text style={styles.tagline}>Keeping your children safe</Text>
      </View>

      <View style={styles.formContainer}>
        <AuthForm
          formType="login"
          onSubmit={handleLogin}
          isLoading={isLoading}
          error={error}
          onToggleForm={navigateToSignup}
        />
      </View>

      <TouchableOpacity
        style={styles.childLoginButton}
        onPress={navigateToChildLogin}
      >
        <Text style={styles.childLoginText}>Child? Login here</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 60,
    marginBottom: 40,
  },
  logoIcon: {
    width: 120,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: '#F0F0F0',
    borderRadius: 60,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#4630EB',
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
    color: '#666666',
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  childLoginButton: {
    padding: 16,
    alignItems: 'center',
  },
  childLoginText: {
    fontSize: 16,
    color: '#4630EB',
  },
});