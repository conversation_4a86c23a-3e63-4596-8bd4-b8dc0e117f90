import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { FontAwesome5 } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Button from '../components/shared/Button';
import Header from '../components/shared/Header';

export default function OnboardingScreen() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: 'Benvenuto in KidSafety',
      description: 'Grazie per aver scelto KidSafety per aiutare a mantenere i tuoi bambini al sicuro. La sicurezza della famiglia è la nostra priorità.',
      icon: 'shield-alt',
    },
    {
      title: 'Monitoraggio Posizione',
      description: 'KidSafety utilizza il tracciamento della posizione per monitorare dove si trovano i tuoi figli e inviarti avvisi quando entrano o escono dalle zone sicure.',
      icon: 'map-marker-alt',
    },
    {
      title: 'Missioni e Ricompense',
      description: 'Crea missioni per i tuoi figli da completare e premiali per i loro risultati. Rendi l\'apprendimento divertente e coinvolgente.',
      icon: 'trophy',
    },
    {
      title: 'Assistente Compiti',
      description: 'I tuoi figli possono utilizzare l\'assistente compiti alimentato da AI per ricevere aiuto con i loro compiti scolastici.',
      icon: 'book',
    },
    {
      title: 'Pronto per Iniziare',
      description: 'Ora sei pronto per iniziare a utilizzare KidSafety. Crea il tuo account per accedere a tutte le funzionalità.',
      icon: 'check-circle',
    },
  ];

  const handleNextStep = async () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      await completeOnboarding();
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const completeOnboarding = async () => {
    try {
      await AsyncStorage.setItem('hasSeenOnboarding', 'true');
      console.log('OnboardingScreen: Onboarding completed, navigating to parent login');
      router.replace('/auth/parent-login');
    } catch (error) {
      console.error('Error completing onboarding:', error);
      router.replace('/auth/parent-login');
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="Iniziamo"
        showBackButton={currentStep > 0}
        onBackPress={handlePreviousStep}
      />

      <ScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        <View style={styles.stepIndicatorContainer}>
          {steps.map((_, index) => (
            <View
              key={index}
              style={[
                styles.stepIndicator,
                currentStep === index && styles.activeStepIndicator
              ]}
            />
          ))}
        </View>

        <View style={styles.stepContent}>
          <FontAwesome5
            name={steps[currentStep].icon}
            size={64}
            color="#4630EB"
            style={styles.icon}
          />

          <Text style={styles.stepTitle}>{steps[currentStep].title}</Text>

          <Text style={styles.stepDescription}>
            {steps[currentStep].description}
          </Text>
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <Button
          title={currentStep === steps.length - 1 ? "Iniziamo" : "Avanti"}
          onPress={handleNextStep}
          fullWidth
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  stepIndicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 40,
  },
  stepIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#D8D8D8',
    marginHorizontal: 5,
  },
  activeStepIndicator: {
    backgroundColor: '#4630EB',
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  stepContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    marginBottom: 24,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
    color: '#333333',
  },
  stepDescription: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    color: '#666666',
    lineHeight: 24,
  },
  buttonContainer: {
    padding: 24,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
});