import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Share,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import * as Clipboard from 'expo-clipboard';
import { useTranslations } from '../../contexts/TranslationContext';

interface AIResponseProps {
  question: string;
  answer: string;
  timestamp?: Date;
  isLoading?: boolean;
}

const AIResponse: React.FC<AIResponseProps> = ({
  question,
  answer,
  timestamp,
  isLoading = false,
}) => {
  const { t } = useTranslations();
  const copyToClipboard = async () => {
    await Clipboard.setStringAsync(answer);
  };

  const shareResponse = async () => {
    try {
      await Share.share({
        message: `${t.homework?.yourQuestion || 'Domanda:'} ${question}\n\n${t.homework?.assistantAnswer || 'Risposta:'} ${answer}`,
        title: t.homework?.title || 'Assistente compiti',
      });
    } catch (error) {
      console.error('Error sharing response:', error);
    }
  };

  const formatTimestamp = (date?: Date) => {
    if (!date) return '';
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <View style={styles.container}>
      <View style={styles.questionContainer}>
        <Text style={styles.questionLabel}>{t.homework?.yourQuestion || 'La tua domanda:'}</Text>
        <Text style={styles.questionText}>{question}</Text>
        {timestamp && (
          <Text style={styles.timestamp}>{formatTimestamp(timestamp)}</Text>
        )}
      </View>

      <View style={styles.answerContainer}>
        <View style={styles.answerHeader}>
          <View style={styles.aiIconContainer}>
            <FontAwesome5 name="robot" size={16} color="#FFFFFF" />
          </View>
          <Text style={styles.answerLabel}>{t.homework?.assistantAnswer || 'Risposta dell\'assistente:'}</Text>
        </View>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Sto pensando...</Text>
            <View style={styles.typingIndicator}>
              <View style={[styles.dot, styles.dot1]} />
              <View style={[styles.dot, styles.dot2]} />
              <View style={[styles.dot, styles.dot3]} />
            </View>
          </View>
        ) : (
          <>
            <ScrollView
              style={styles.answerScrollView}
              showsVerticalScrollIndicator={true}
              persistentScrollbar={true}
              nestedScrollEnabled={true}
            >
              <Text style={styles.answerText}>{answer}</Text>
              {answer.length > 500 && (
                <Text style={styles.scrollHint}>Scorri per vedere il resto della risposta</Text>
              )}
            </ScrollView>

            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={copyToClipboard}
              >
                <FontAwesome5 name="copy" size={16} color="#4630EB" />
                <Text style={styles.actionButtonText}>Copia</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={shareResponse}
              >
                <FontAwesome5 name="share-alt" size={16} color="#4630EB" />
                <Text style={styles.actionButtonText}>Condividi</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginBottom: 20,
  },
  questionContainer: {
    backgroundColor: '#F0F0F0',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  questionLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666666',
    marginBottom: 8,
  },
  questionText: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 22,
  },
  timestamp: {
    fontSize: 12,
    color: '#999999',
    marginTop: 8,
    textAlign: 'right',
  },
  answerContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  answerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  aiIconContainer: {
    backgroundColor: '#4630EB',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  answerLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4630EB',
  },
  answerScrollView: {
    maxHeight: 400, // Aumentato da 300 a 400
  },
  scrollHint: {
    fontSize: 12,
    color: '#666666',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
    paddingBottom: 8,
  },
  answerText: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginRight: 8,
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4630EB',
    marginHorizontal: 2,
    opacity: 0.6,
  },
  dot1: {
    opacity: 0.6,
  },
  dot2: {
    opacity: 0.8,
  },
  dot3: {
    opacity: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    marginLeft: 16,
  },
  actionButtonText: {
    fontSize: 14,
    color: '#4630EB',
    marginLeft: 4,
  },
});

export default AIResponse;
