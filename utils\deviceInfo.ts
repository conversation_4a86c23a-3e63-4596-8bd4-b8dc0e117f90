import * as Device from 'expo-device';
import { Platform } from 'react-native';

export interface DeviceInfo {
  brand: string;
  manufacturer: string;
  modelName: string;
  modelId: string;
  deviceType: string;
  osVersion: string;
}

/**
 * Ottiene informazioni dettagliate sul dispositivo corrente
 * @returns Informazioni sul dispositivo
 */
export const getDeviceInfo = (): DeviceInfo => {
  try {
    const brand = Device.brand || 'unknown';
    const manufacturer = Device.manufacturer || 'unknown';
    const modelName = Device.modelName || 'unknown';
    const modelId = Device.modelId || 'unknown';
    const deviceType = Device.deviceType !== undefined ? Device.DeviceType[Device.deviceType] : 'unknown';
    const osVersion = Device.osVersion || 'unknown';

    return {
      brand,
      manufacturer,
      modelName,
      modelId,
      deviceType,
      osVersion
    };
  } catch (error) {
    console.error('Error getting device info:', error);
    return {
      brand: 'unknown',
      manufacturer: 'unknown',
      modelName: 'unknown',
      modelId: 'unknown',
      deviceType: 'unknown',
      osVersion: 'unknown'
    };
  }
};

/**
 * Identifica il tipo di UI del dispositivo in base al produttore
 * @returns Il tipo di UI del dispositivo
 */
export const getDeviceUIType = (): 'samsung' | 'xiaomi' | 'huawei' | 'oppo' | 'oneplus' | 'google' | 'motorola' | 'generic' => {
  try {
    // Se non siamo su Android, restituisci 'generic'
    if (Platform.OS !== 'android') {
      return 'generic';
    }

    const manufacturer = (Device.manufacturer || '').toLowerCase();

    if (manufacturer.includes('samsung')) return 'samsung';
    if (manufacturer.includes('xiaomi') || manufacturer.includes('redmi') || manufacturer.includes('poco')) return 'xiaomi';
    if (manufacturer.includes('huawei') || manufacturer.includes('honor')) return 'huawei';
    if (manufacturer.includes('oppo') || manufacturer.includes('realme') || manufacturer.includes('vivo')) return 'oppo';
    if (manufacturer.includes('oneplus')) return 'oneplus';
    if (manufacturer.includes('google')) return 'google';
    if (manufacturer.includes('motorola') || manufacturer.includes('moto')) return 'motorola';

    return 'generic';
  } catch (error) {
    console.error('Error identifying device UI type:', error);
    return 'generic';
  }
};
