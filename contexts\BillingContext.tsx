import React, { createContext, useContext, ReactNode } from 'react';
import { useBilling, BillingState } from '../hooks/useBilling';

// Define the context shape extending the BillingState
interface BillingContextType extends BillingState {
  initialize: () => Promise<boolean>;
  loadProducts: (productIds?: string[]) => Promise<any[]>;
  loadPurchases: () => Promise<any[]>;
  purchaseSubscription: (productId: string) => Promise<{ success: boolean; message: string }>;
  restorePurchases: () => Promise<{ success: boolean; message: string }>;
  checkHasActiveSubscription: () => boolean;
  getActiveSubscription: () => any | undefined;
  isProductActive: (productId: string) => boolean;
  hasFreeTrial: (productId: string) => boolean;
  getFreeTrialPeriod: (productId: string) => string | null;
  isMonthlyActive: boolean;
  isYearlyActive: boolean;
  monthlyProduct: any | undefined;
  yearlyProduct: any | undefined;
}

// Create the context with a default value
const BillingContext = createContext<BillingContextType | undefined>(undefined);

// Provider component
export const BillingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const billingState = useBilling();

  return (
    <BillingContext.Provider value={billingState}>
      {children}
    </BillingContext.Provider>
  );
};

// Custom hook to use the billing context
export const useBillingContext = () => {
  const context = useContext(BillingContext);
  if (context === undefined) {
    throw new Error('useBillingContext must be used within a BillingProvider');
  }
  return context;
};

export default BillingContext; 