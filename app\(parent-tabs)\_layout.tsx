import React from 'react';
import { Tabs } from 'expo-router';
import { View, Text, ActivityIndicator } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useTranslations } from '../../contexts/TranslationContext';

// Componente wrapper per le tab che utilizza le traduzioni
function TabsWithTranslation() {
  let t;

  try {
    const translations = useTranslations();
    t = translations.t;

    // Controllo di sicurezza per le traduzioni
    if (!t) {
      console.log('⚠️ Translations not ready in ParentTabsLayout');
      // Usa traduzioni di fallback
      t = {
        dashboard: { home: 'Home', title: 'Dashboard', missions: 'Missions', routes: 'Routes' },
        settings: { title: 'Settings' }
      };
    } else {
      console.log('✅ ParentTabsLayout: Translations ready, rendering tabs');
    }
  } catch (error) {
    console.log('❌ ParentTabsLayout: Error accessing translations:', error.message);
    // Usa traduzioni di fallback invece di mostrare loading
    t = {
      dashboard: { home: 'Home', title: 'Dashboard', missions: 'Missions', routes: 'Routes' },
      settings: { title: 'Settings' }
    };
  }

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#4630EB',
        tabBarInactiveTintColor: '#888888',
        tabBarStyle: {
          height: 60,
          paddingBottom: 10,
          paddingTop: 6,
        },
        headerShown: false,
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: t.dashboard?.home || 'Home',
          tabBarIcon: ({ color, size }) => (
            <FontAwesome5 name="home" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="index"
        options={{
          href: null, // Nascondi questa scheda dalla barra delle schede
        }}
      />
      <Tabs.Screen
        name="dashboard"
        options={{
          title: t.dashboard?.title || 'Dashboard',
          tabBarIcon: ({ color, size }) => (
            <FontAwesome5 name="chart-bar" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="children/index"
        options={{
          href: null, // Nascondi questa scheda dalla barra delle schede
        }}
      />
      <Tabs.Screen
        name="missions"
        options={{
          title: t.dashboard?.missions || 'Missions',
          tabBarIcon: ({ color, size }) => (
            <FontAwesome5 name="tasks" size={size} color={color} />
          ),
        }}
      />


      <Tabs.Screen
        name="routes"
        options={{
          title: t.dashboard?.routes || 'Routes',
          tabBarIcon: ({ color, size }) => (
            <FontAwesome5 name="route" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="map"
        options={{
          href: null, // Nascondi questa scheda dalla barra delle schede
        }}
      />
      <Tabs.Screen
        name="safe-zones"
        options={{
          href: null, // Nascondi questa scheda dalla barra delle schede
        }}
      />
      <Tabs.Screen
        name="children/[id]"
        options={{
          href: null, // Nascondi questa scheda dalla barra delle schede
        }}
      />
      <Tabs.Screen
        name="sos-alert/[id]"
        options={{
          href: null, // Nascondi questa scheda dalla barra delle schede
        }}
      />
      <Tabs.Screen
        name="subscription"
        options={{
          href: null, // Nascondi questa scheda dalla barra delle schede
        }}
      />
      <Tabs.Screen
        name="subscription-management"
        options={{
          href: null, // Nascondi questa scheda dalla barra delle schede
        }}
      />
      <Tabs.Screen
        name="app-monitoring"
        options={{
          href: null, // Nascondi questa scheda dalla barra delle schede
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: t.settings?.title || 'Settings',
          tabBarIcon: ({ color, size }) => (
            <FontAwesome5 name="cog" size={size} color={color} />
          ),
        }}
      />
    </Tabs>
  );
}

export default function ParentTabsLayout() {
  return <TabsWithTranslation />;
}