import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, TextInput, Modal } from 'react-native';
import * as Clipboard from 'expo-clipboard';
import { useRouter } from 'expo-router';
import { FontAwesome5 } from '@expo/vector-icons';
import { useAuth } from '../../../contexts/AuthContext';
import { getChildrenForParent, registerChild, deleteChild, verifyAndRepairChildRelationships } from '../../../utils/supabase';
import Header from '../../../components/shared/Header';
import LoadingIndicator from '../../../components/shared/LoadingIndicator';
// Token generation utilities (moved inline to fix build issues)
const TOKEN_CHARSET = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
const TOKEN_LENGTH = 6;

const generateToken = (): string => {
  let token = '';
  for (let i = 0; i < TOKEN_LENGTH; i++) {
    const randomIndex = Math.floor(Math.random() * TOKEN_CHARSET.length);
    token += TOKEN_CHARSET[randomIndex];
  }
  return token;
};

const formatToken = (token: string): string => {
  const cleanToken = token.replace(/[^A-Z0-9]/g, '').toUpperCase();
  if (cleanToken.length === TOKEN_LENGTH) {
    return `${cleanToken.slice(0, 2)}-${cleanToken.slice(2, 4)}-${cleanToken.slice(4, 6)}`;
  }
  return cleanToken;
};
import { useTranslations } from '../../../contexts/TranslationContext';

interface Child {
  child_id: string;
  token: string;
  users: {
    id: string;
    name: string;
    email: string;
    user_type: string;
    age?: number;
    created_at: string;
  };
}

export default function ChildrenScreen() {
  const { user } = useAuth();
  const router = useRouter();
  const { t } = useTranslations();
  const [children, setChildren] = useState<Child[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [newChild, setNewChild] = useState({
    name: '',
    age: '',
  });

  useEffect(() => {
    fetchChildren();
  }, []);

  const fetchChildren = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Prima verifica e ripara eventuali relazioni mancanti
      console.log('Verifying child relationships...');
      await verifyAndRepairChildRelationships(user.id);

      // Poi carica i dati dei bambini
      const childrenData = await getChildrenForParent(user.id);
      console.log('Fetched children data:', childrenData);
      setChildren(childrenData || []);

      if (childrenData && childrenData.length > 0) {
        console.log('Children loaded successfully:', childrenData.map(child => ({
          id: child.child_id,
          name: child.users?.name,
          token: child.token
        })));
      } else {
        console.log('No children found for parent:', user.id);
      }
    } catch (error) {
      console.error('Error fetching children:', error);
      Alert.alert(t.common.error, t.child.errorLoading || 'Failed to load children data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteChild = (child: Child) => {
    Alert.alert(
      t.child.deleteChild,
      `${t.child.confirmDelete.message.replace('{name}', child.users?.name || t.child.title)}`,
      [
        {
          text: t.child.confirmDelete.cancel,
          style: 'cancel',
        },
        {
          text: t.child.confirmDelete.delete,
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);

              // Aggiorna immediatamente l'interfaccia utente rimuovendo il bambino dalla lista
              // Facciamo questo prima della chiamata al database per un'esperienza utente più reattiva
              setChildren(prevChildren => prevChildren.filter(c => c.child_id !== child.child_id));

              // Elimina il bambino dal database
              const result = await deleteChild(child.child_id);
              console.log('Delete child result:', result);

              Alert.alert(t.common.success, t.child.deleteSuccess || 'Child deleted successfully');

              // Aggiorna anche i dati dal server per sicurezza, ma con un ritardo
              // per dare tempo al database di completare l'operazione
              setTimeout(() => {
                fetchChildren();
              }, 1000);
            } catch (error) {
              console.error('Error deleting child:', error);
              Alert.alert(t.common.error, t.child.deleteError || 'Failed to delete child. Please try again.');

              // In caso di errore, ripristina la lista originale
              fetchChildren();
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  const showAddChildModal = () => {
    setNewChild({
      name: '',
      age: '',
    });
    setModalVisible(true);
  };

  const addNewChild = async () => {
    if (!user) {
      Alert.alert(t.common.error, t.child.loginRequired || 'You must be logged in to add a child');
      return;
    }

    // Validate form
    if (!newChild.name.trim()) {
      Alert.alert(t.common.error, t.child.nameRequired || 'Please enter a name for the child');
      return;
    }

    try {
      setIsLoading(true);

      // Generate a random token using the tokenGenerator utility
      const token = generateToken();
      const formattedToken = formatToken(token);

      // Register the child with the provided data
      const childData = {
        name: newChild.name.trim(),
        age: newChild.age ? parseInt(newChild.age) : undefined,
        user_type: 'child',
      };

      const result = await registerChild(user.id, childData, token);

      if (result) {
        setModalVisible(false);

        // Mostra il token e chiedi conferma prima di aggiungere il bambino
        Alert.alert(
          t.child.addSuccess || 'Child Added Successfully',
          `Token: ${formattedToken}\n\n${t.child.tokenInstructions || "Use this token to log in on your child's device."}`,
          [
            {
              text: t.child.copyToken || 'Copy Token',
              onPress: async () => {
                // Copia il token negli appunti
                await Clipboard.setStringAsync(formattedToken);

                // Mostra un messaggio di conferma
                Alert.alert(
                  t.child.tokenCopied || 'Token Copied',
                  t.child.tokenCopiedMessage || 'The token has been copied to clipboard. Please provide this token to your child to log in.',
                  [
                    {
                      text: t.common.ok,
                      onPress: () => {
                        // Chiedi conferma per completare l'aggiunta del bambino
                        Alert.alert(
                          t.child.confirmAddition || 'Confirm Addition',
                          t.child.confirmAdditionMessage || 'Do you want to complete adding this child to your account?',
                          [
                            {
                              text: t.common.cancel,
                              style: 'cancel',
                              onPress: () => {
                                // Se l'utente annulla, aggiorniamo comunque la lista
                                // perché il bambino è già stato creato nel database
                                fetchChildren();
                              }
                            },
                            {
                              text: t.common.confirm || 'Confirm',
                              onPress: async () => {
                                // Aggiorna la lista dei bambini e verifica che sia stato salvato
                                await fetchChildren();

                                // Verifica che il bambino sia effettivamente nella lista
                                const updatedChildren = await getChildrenForParent(user.id);
                                const childExists = updatedChildren?.some(child => child.users?.name === newChild.name.trim());

                                if (childExists) {
                                  Alert.alert(t.common.success, t.child.addedToAccount || 'Child has been added to your account');
                                } else {
                                  Alert.alert(t.common.error, 'Errore nel salvare il bambino. Riprova.');
                                }
                              }
                            }
                          ]
                        );
                      }
                    }
                  ]
                );
              },
            },
            {
              text: t.common.ok,
              onPress: () => {
                // Chiedi conferma per completare l'aggiunta del bambino
                Alert.alert(
                  t.child.confirmAddition || 'Confirm Addition',
                  t.child.confirmAdditionMessage || 'Do you want to complete adding this child to your account?',
                  [
                    {
                      text: t.common.cancel,
                      style: 'cancel',
                      onPress: () => {
                        // Se l'utente annulla, aggiorniamo comunque la lista
                        // perché il bambino è già stato creato nel database
                        fetchChildren();
                      }
                    },
                    {
                      text: t.common.confirm || 'Confirm',
                      onPress: async () => {
                        // Aggiorna la lista dei bambini e verifica che sia stato salvato
                        await fetchChildren();

                        // Verifica che il bambino sia effettivamente nella lista
                        const updatedChildren = await getChildrenForParent(user.id);
                        const childExists = updatedChildren?.some(child => child.users?.name === newChild.name.trim());

                        if (childExists) {
                          Alert.alert(t.common.success, t.child.addedToAccount || 'Child has been added to your account');
                        } else {
                          Alert.alert(t.common.error, 'Errore nel salvare il bambino. Riprova.');
                        }
                      }
                    }
                  ]
                );
              },
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error adding child:', error);
      Alert.alert(t.common.error, t.child.addError || 'Failed to add child. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <View style={styles.container}>
      <Header titleKey="child.title" />

      <ScrollView style={styles.content}>
        {children.length === 0 ? (
          <View style={styles.emptyState}>
            <FontAwesome5 name="child" size={50} color="#ccc" />
            <Text style={styles.emptyText}>{t.dashboard.noChildrenAdded}</Text>
            <Text style={styles.emptySubtext}>{t.child.addFirstChild || 'Add your first child to start monitoring'}</Text>
          </View>
        ) : (
          children.map((child) => (
            <TouchableOpacity
              key={child.child_id}
              style={styles.childCard}
              onPress={() => router.push(`/children/${child.child_id}`)}
            >
              <View style={styles.childIcon}>
                <FontAwesome5 name="child" size={24} color="#4630EB" />
              </View>
              <View style={styles.childInfo}>
                <Text style={styles.childName}>{child.users?.name || t.child.title}</Text>
                <Text style={styles.childDetails}>
                  {child.users?.age ? `${t.child.age}: ${child.users.age}` : `${t.child.age}: ${t.child.notSpecified || 'Not specified'}`}
                </Text>
              </View>
              <View style={styles.actionButtons}>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => handleDeleteChild(child)}
                >
                  <FontAwesome5 name="trash" size={16} color="#FF3B30" />
                </TouchableOpacity>
                <FontAwesome5 name="chevron-right" size={16} color="#ccc" style={{marginLeft: 12}} />
              </View>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>

      <TouchableOpacity style={styles.addButton} onPress={showAddChildModal}>
        <FontAwesome5 name="plus" size={20} color="#fff" />
        <Text style={styles.addButtonText}>{t.child.addChild}</Text>
      </TouchableOpacity>

      {/* Add Child Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t.child.addChild}</Text>
              <TouchableOpacity
                onPress={() => setModalVisible(false)}
                style={styles.closeButton}
              >
                <FontAwesome5 name="times" size={20} color="#333" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              <Text style={styles.inputLabel}>{t.child.name}</Text>
              <TextInput
                style={styles.input}
                placeholder={t.child.enterName || "Enter name"}
                value={newChild.name}
                onChangeText={(text) => setNewChild({...newChild, name: text})}
              />

              <Text style={styles.inputLabel}>{t.child.age}</Text>
              <TextInput
                style={styles.input}
                placeholder={t.child.enterAge || "Enter age"}
                keyboardType="numeric"
                value={newChild.age}
                onChangeText={(text) => {
                  // Allow only numbers
                  const numericText = text.replace(/[^0-9]/g, '');
                  setNewChild({...newChild, age: numericText});
                }}
              />

              <TouchableOpacity
                style={styles.submitButton}
                onPress={addNewChild}
              >
                <Text style={styles.submitButtonText}>{t.child.addChild}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 100,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
    color: '#333',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  childCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  childIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f0ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  childInfo: {
    flex: 1,
  },
  childName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  childDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4630EB',
    borderRadius: 30,
    padding: 16,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
  },
  submitButton: {
    backgroundColor: '#4630EB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButton: {
    padding: 8,
  },
});
