import React, { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import * as SplashScreen from 'expo-splash-screen';
import { LanguageProvider } from '../contexts/LanguageContext';
import TranslationProvider from '../contexts/TranslationProvider';
import { AuthProvider } from '../contexts/AuthContext';
import { LocationProvider } from '../contexts/LocationContext';
import { MissionsProvider } from '../contexts/MissionsContext';
import { BillingProvider } from '../contexts/BillingContext';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  console.log('🔥 Root Layout: SIMPLIFIED VERSION WITHOUT LOADING SCREEN');

  useEffect(() => {
    const hideSplash = async () => {
      try {
        await SplashScreen.hideAsync();
      } catch (error) {
        console.error('❌ Error hiding splash screen:', error);
      }
    };

    hideSplash();
  }, []);

  return (
    <BillingProvider>
      <LanguageProvider>
        <TranslationProvider>
          <AuthProvider>
            <LocationProvider>
              <MissionsProvider>
                <StatusBar style="auto" />
                <Stack screenOptions={{ headerShown: false }}>
                  <Stack.Screen name="index" />
                  <Stack.Screen name="auth" />
                  <Stack.Screen name="(parent-tabs)" />
                  <Stack.Screen name="(child-tabs)" />
                </Stack>
              </MissionsProvider>
            </LocationProvider>
          </AuthProvider>
        </TranslationProvider>
      </LanguageProvider>
    </BillingProvider>
  );
}
