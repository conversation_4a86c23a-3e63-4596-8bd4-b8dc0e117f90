import React from 'react';
import { View, StyleSheet, Text, Dimensions, TouchableOpacity } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import DeviceSpecificPermissionHelp from './DeviceSpecificPermissionHelp';
import { openBackgroundLocationSettings, openBatteryOptimizationSettings, openAccessibilitySettings } from '../../utils/systemSettings';

const { width } = Dimensions.get('window');

interface PermissionHelpImageProps {
  permissionType: 'location' | 'backgroundLocation' | 'appMonitoring' | 'notifications' | 'batteryOptimization' | 'notificationAccess' | 'accessibility';
}

const PermissionHelpImage: React.FC<PermissionHelpImageProps> = ({ permissionType }) => {
  // Funzione per aprire le impostazioni di sistema appropriate
  const openSystemSettings = async () => {
    console.log('[PermissionHelpImage] Opening system settings for', permissionType);

    switch (permissionType) {
      case 'location':
      case 'backgroundLocation':
        // Utilizziamo sempre questa funzione per le impostazioni di posizione
        console.log('[PermissionHelpImage] Opening background location settings');
        await openBackgroundLocationSettings();
        break;
      case 'appMonitoring':
      case 'accessibility':
        console.log('[PermissionHelpImage] Opening accessibility settings');
        await openAccessibilitySettings();
        break;
      case 'batteryOptimization':
        console.log('[PermissionHelpImage] Opening battery optimization settings');
        await openBatteryOptimizationSettings();
        break;
      case 'notifications':
      case 'notificationAccess':
        console.log('[PermissionHelpImage] Opening location settings as fallback');
        // Utilizziamo le impostazioni di posizione come fallback
        await openBackgroundLocationSettings();
        break;
      default:
        // Per altre autorizzazioni, apriamo le impostazioni generali
        console.log('[PermissionHelpImage] Opening location settings as default');
        await openBackgroundLocationSettings();
    }
  };

  // Ottieni il contenuto appropriato in base al tipo di autorizzazione
  const getImageContent = () => {
    // Per il monitoraggio delle app, usa le istruzioni specifiche per il dispositivo
    if (permissionType === 'appMonitoring') {
      return (
        <>
          <DeviceSpecificPermissionHelp permissionType="usageStats" />
          <TouchableOpacity style={styles.openSettingsButton} onPress={openSystemSettings}>
            <FontAwesome5 name="cog" size={16} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.openSettingsButtonText}>Apri Impostazioni</Text>
          </TouchableOpacity>
        </>
      );
    }

    // Per l'accessibilità, usa le istruzioni specifiche per il dispositivo
    if (permissionType === 'accessibility') {
      return (
        <>
          <DeviceSpecificPermissionHelp permissionType="accessibility" />
          <TouchableOpacity style={styles.openSettingsButton} onPress={openSystemSettings}>
            <FontAwesome5 name="cog" size={16} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.openSettingsButtonText}>Apri Impostazioni</Text>
          </TouchableOpacity>
        </>
      );
    }

    // Per le altre autorizzazioni, usa le istruzioni generiche
    switch (permissionType) {
      case 'location':
        return (
          <>
            <View style={styles.mockImage}>
              <View style={styles.mockDialog}>
                <Text style={styles.mockDialogTitle}>Consenti a "KidSafety" di accedere alla posizione del dispositivo?</Text>
                <View style={styles.mockButtonContainer}>
                  <View style={styles.mockButton}>
                    <Text style={styles.mockButtonText}>Non consentire</Text>
                  </View>
                  <View style={[styles.mockButton, styles.mockButtonHighlight]}>
                    <Text style={[styles.mockButtonText, styles.mockButtonTextHighlight]}>Consenti</Text>
                  </View>
                </View>
                <View style={styles.mockArrow}>
                  <Text style={styles.mockArrowText}>👆 Tocca "Consenti"</Text>
                </View>
              </View>
            </View>
            <TouchableOpacity style={styles.openSettingsButton} onPress={openSystemSettings}>
              <FontAwesome5 name="cog" size={16} color="#FFFFFF" style={styles.buttonIcon} />
              <Text style={styles.openSettingsButtonText}>Apri Impostazioni</Text>
            </TouchableOpacity>
          </>
        );
      case 'backgroundLocation':
        return (
          <>
            <View style={styles.mockImage}>
              <View style={styles.mockDialog}>
                <Text style={styles.mockDialogTitle}>Consenti a "KidSafety" di accedere alla tua posizione anche quando l'app non è in uso?</Text>
                <View style={styles.mockButtonContainer}>
                  <View style={styles.mockButton}>
                    <Text style={styles.mockButtonText}>Solo mentre usi l'app</Text>
                  </View>
                  <View style={[styles.mockButton, styles.mockButtonHighlight]}>
                    <Text style={[styles.mockButtonText, styles.mockButtonTextHighlight]}>Consenti sempre</Text>
                  </View>
                </View>
                <View style={styles.mockArrow}>
                  <Text style={styles.mockArrowText}>👆 Tocca "Consenti sempre"</Text>
                </View>
              </View>
            </View>
            <TouchableOpacity style={styles.openSettingsButton} onPress={openSystemSettings}>
              <FontAwesome5 name="cog" size={16} color="#FFFFFF" style={styles.buttonIcon} />
              <Text style={styles.openSettingsButtonText}>Apri Impostazioni</Text>
            </TouchableOpacity>
          </>
        );
      case 'notifications':
        return (
          <>
            <View style={styles.mockImage}>
              <View style={styles.mockDialog}>
                <Text style={styles.mockDialogTitle}>"KidSafety" vorrebbe inviarti notifiche</Text>
                <View style={styles.mockButtonContainer}>
                  <View style={styles.mockButton}>
                    <Text style={styles.mockButtonText}>Non consentire</Text>
                  </View>
                  <View style={[styles.mockButton, styles.mockButtonHighlight]}>
                    <Text style={[styles.mockButtonText, styles.mockButtonTextHighlight]}>Consenti</Text>
                  </View>
                </View>
                <View style={styles.mockArrow}>
                  <Text style={styles.mockArrowText}>👆 Tocca "Consenti"</Text>
                </View>
              </View>
            </View>
            <TouchableOpacity style={styles.openSettingsButton} onPress={openSystemSettings}>
              <FontAwesome5 name="cog" size={16} color="#FFFFFF" style={styles.buttonIcon} />
              <Text style={styles.openSettingsButtonText}>Apri Impostazioni</Text>
            </TouchableOpacity>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {getImageContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
    width: width - 40,
  },
  openSettingsButton: {
    backgroundColor: '#4630EB',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    width: '80%',
  },
  openSettingsButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  buttonIcon: {
    marginRight: 4,
  },
  mockImage: {
    width: '100%',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  mockHeader: {
    backgroundColor: '#4630EB',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  mockHeaderText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  mockSection: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  mockSectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  mockMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  mockMenuText: {
    fontSize: 16,
    color: '#333',
  },
  mockAppItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 8,
  },
  mockAppIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: '#4630EB',
    marginRight: 12,
  },
  mockAppText: {
    flex: 1,
    fontSize: 16,
  },
  mockSwitch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#ddd',
    padding: 2,
  },
  mockSwitchKnob: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: 'white',
  },
  mockSwitchOn: {
    backgroundColor: '#4CAF50',
    marginLeft: 20,
  },
  mockDialog: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  mockDialogTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 20,
    textAlign: 'center',
  },
  mockButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  mockButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    marginHorizontal: 4,
    alignItems: 'center',
  },
  mockButtonHighlight: {
    backgroundColor: '#4630EB',
  },
  mockButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  mockButtonTextHighlight: {
    color: 'white',
  },
  mockArrow: {
    alignItems: 'center',
    marginTop: 16,
  },
  mockArrowText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF9800',
  },
});

export default PermissionHelpImage;
