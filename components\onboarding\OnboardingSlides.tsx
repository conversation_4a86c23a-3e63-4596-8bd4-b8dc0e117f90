import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

interface Slide {
  id: number;
  title: string;
  description: string;
  icon: string;
  color: string;
  backgroundColor: string;
}

const slides: Slide[] = [
  {
    id: 1,
    title: "Benvenuto in KidSafety",
    description: "L'app che ti aiuta a proteggere i tuoi figli e a rimanere sempre connesso con loro in modo sicuro e divertente.",
    icon: "shield-alt",
    color: "#4630EB",
    backgroundColor: "#F0F8FF"
  },
  {
    id: 2,
    title: "Aggiungi i tuoi Figli",
    description: "Inizia aggiungendo un figlio al tuo account. Riceverai un codice univoco da inserire nell'app del bambino per collegare i dispositivi.",
    icon: "user-plus",
    color: "#4CAF50",
    backgroundColor: "#E8F5E9"
  },
  {
    id: 3,
    title: "Zone Sicure e Notifiche",
    description: "Crea zone sicure come casa, scuola o casa dei nonni. Riceverai notifiche automatiche quando tuo figlio entra o esce da questi luoghi.",
    icon: "map-marker-alt",
    color: "#FF9800",
    backgroundColor: "#FFF3E0"
  },
  {
    id: 4,
    title: "Missioni e Ricompense",
    description: "Assegna missioni divertenti ai tuoi figli e offri ricompense per motivarli. Un modo positivo per incoraggiare buoni comportamenti.",
    icon: "trophy",
    color: "#9C27B0",
    backgroundColor: "#F3E5F5"
  },
  {
    id: 5,
    title: "Supporto allo Studio con IA",
    description: "I tuoi figli possono ricevere aiuto per i compiti grazie all'intelligenza artificiale. Possono fare domande, scattare foto dei compiti e ricevere spiegazioni dettagliate.",
    icon: "brain",
    color: "#673AB7",
    backgroundColor: "#EDE7F6"
  },

  {
    id: 7,
    title: "Account Famiglia",
    description: "Condividi l'abbonamento con il tuo partner! Un genitore può invitare l'altro tramite email per avere accesso completo a tutte le funzionalità premium.",
    icon: "users",
    color: "#00BCD4",
    backgroundColor: "#E0F2F1"
  },
  {
    id: 8,
    title: "Traccia i Percorsi",
    description: "Visualizza i tragitti percorsi dai tuoi figli durante il giorno per assicurarti che siano sempre al sicuro.",
    icon: "route",
    color: "#F44336",
    backgroundColor: "#FFEBEE"
  }
];



export default function OnboardingSlides() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const router = useRouter();

  const handleNext = () => {
    console.log('handleNext called, currentSlide:', currentSlide, 'totalSlides:', slides.length);

    if (currentSlide < slides.length - 1) {
      const nextSlide = currentSlide + 1;
      console.log('Moving to next slide:', nextSlide);
      setCurrentSlide(nextSlide);
      scrollViewRef.current?.scrollTo({
        x: nextSlide * width,
        animated: true,
      });
    } else {
      console.log('Last slide reached, calling handleFinish');
      handleFinish();
    }
  };

  const handlePrevious = () => {
    if (currentSlide > 0) {
      const prevSlide = currentSlide - 1;
      setCurrentSlide(prevSlide);
      scrollViewRef.current?.scrollTo({
        x: prevSlide * width,
        animated: true,
      });
    }
  };

  const handleSkip = () => {
    handleFinish();
  };

  const handleFinish = async () => {
    try {
      console.log('🚀 OnboardingSlides: Finishing onboarding');

      // Salva lo stato dell'onboarding
      await AsyncStorage.setItem('hasSeenOnboarding', 'true');
      console.log('✅ OnboardingSlides: Onboarding status saved');

      // Navigazione diretta al login dei genitori
      console.log('🔄 OnboardingSlides: Navigating to parent login');
      router.replace('/auth/parent-login');

    } catch (error) {
      console.error('❌ OnboardingSlides: Error in handleFinish:', error);
      
      // Fallback: prova comunque a navigare
      router.replace('/auth/parent-login');
    }
  };



  const handleScroll = (event: any) => {
    const slideIndex = Math.round(event.nativeEvent.contentOffset.x / width);
    setCurrentSlide(slideIndex);
  };

  const renderSlide = (slide: Slide) => (
    <View key={slide.id} style={[styles.slide, { backgroundColor: slide.backgroundColor }]}>
      <View style={styles.slideContent}>
        <View style={[styles.iconContainer, { backgroundColor: slide.color }]}>
          <FontAwesome5 name={slide.icon} size={40} color="#FFFFFF" />
        </View>

        <Text style={styles.title}>{slide.title}</Text>
        <Text style={styles.description}>{slide.description}</Text>

        {slide.id === 2 && (
          <View style={styles.codeExample}>
            <Text style={styles.codeLabel}>Esempio di codice:</Text>
            <View style={styles.codeContainer}>
              <Text style={styles.codeText}>ABC123</Text>
            </View>
          </View>
        )}

        {slide.id === 5 && (
          <View style={styles.aiExample}>
            <Text style={styles.aiExampleLabel}>Esempi di utilizzo:</Text>
            <View style={styles.aiFeaturesList}>
              <View style={styles.aiFeatureItem}>
                <FontAwesome5 name="camera" size={16} color="#673AB7" />
                <Text style={styles.aiFeatureText}>Scatta foto dei compiti</Text>
              </View>
              <View style={styles.aiFeatureItem}>
                <FontAwesome5 name="question-circle" size={16} color="#673AB7" />
                <Text style={styles.aiFeatureText}>Fai domande di matematica</Text>
              </View>
              <View style={styles.aiFeatureItem}>
                <FontAwesome5 name="book" size={16} color="#673AB7" />
                <Text style={styles.aiFeatureText}>Aiuto con le materie</Text>
              </View>
            </View>
          </View>
        )}

        {slide.id === 6 && (
          <View style={styles.appControlExample}>
            <Text style={styles.appControlLabel}>Funzionalità:</Text>
            <View style={styles.appControlFeaturesList}>
              <View style={styles.appControlFeatureItem}>
                <FontAwesome5 name="ban" size={16} color="#E91E63" />
                <Text style={styles.appControlFeatureText}>Blocca app specifiche</Text>
              </View>
              <View style={styles.appControlFeatureItem}>
                <FontAwesome5 name="clock" size={16} color="#E91E63" />
                <Text style={styles.appControlFeatureText}>Orari di restrizione</Text>
              </View>
              <View style={styles.appControlFeatureItem}>
                <FontAwesome5 name="eye" size={16} color="#E91E63" />
                <Text style={styles.appControlFeatureText}>Monitora utilizzo</Text>
              </View>
            </View>
          </View>
        )}

        {slide.id === 7 && (
          <View style={styles.familyExample}>
            <Text style={styles.familyLabel}>Come funziona:</Text>
            <View style={styles.familyStepsList}>
              <View style={styles.familyStepItem}>
                <View style={styles.stepNumber}>
                  <Text style={styles.stepNumberText}>1</Text>
                </View>
                <Text style={styles.familyStepText}>Inserisci l'email del partner</Text>
              </View>
              <View style={styles.familyStepItem}>
                <View style={styles.stepNumber}>
                  <Text style={styles.stepNumberText}>2</Text>
                </View>
                <Text style={styles.familyStepText}>Condividi il codice di invito</Text>
              </View>
              <View style={styles.familyStepItem}>
                <View style={styles.stepNumber}>
                  <Text style={styles.stepNumberText}>3</Text>
                </View>
                <Text style={styles.familyStepText}>Entrambi avete accesso premium!</Text>
              </View>
            </View>
          </View>
        )}
      </View>
    </View>
  );

  const renderPagination = () => (
    <View style={styles.pagination}>
      {slides.map((_, index) => (
        <View
          key={index}
          style={[
            styles.paginationDot,
            index === currentSlide ? styles.paginationDotActive : null,
          ]}
        />
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => {
            console.log('⏭️ SKIP BUTTON PRESSED');
            handleSkip();
          }}
          style={styles.skipButton}
        >
          <Text style={styles.skipText}>Salta</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        style={styles.scrollView}
      >
        {slides.map((slide) => renderSlide(slide))}
      </ScrollView>

      {renderPagination()}

      <View style={styles.footer}>
        <TouchableOpacity
          onPress={handlePrevious}
          style={[styles.button, styles.previousButton]}
          disabled={currentSlide === 0}
        >
          <FontAwesome5 name="chevron-left" size={16} color={currentSlide === 0 ? "#CCCCCC" : "#666666"} />
          <Text style={[styles.buttonText, { color: currentSlide === 0 ? "#CCCCCC" : "#666666" }]}>
            Indietro
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            console.log('🔘 NEXT/START BUTTON PRESSED - Current slide:', currentSlide, 'Is last:', currentSlide === slides.length - 1);
            handleNext();
          }}
          style={[styles.button, styles.nextButton]}
        >
          <Text style={styles.nextButtonText}>
            {currentSlide === slides.length - 1 ? 'Inizia' : 'Avanti'}
          </Text>
          <FontAwesome5 name="chevron-right" size={16} color="#FFFFFF" style={styles.buttonIcon} />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 10,
  },
  skipButton: {
    padding: 10,
  },
  skipText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  slide: {
    width,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  slideContent: {
    alignItems: 'center',
    maxWidth: 320,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 28,
  },
  description: {
    fontSize: 15,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 20,
  },
  codeExample: {
    alignItems: 'center',
    marginTop: 20,
  },
  codeLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 10,
  },
  codeContainer: {
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#4CAF50',
  },
  codeText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
    letterSpacing: 2,
  },
  aiExample: {
    alignItems: 'center',
    marginTop: 20,
    width: '100%',
  },
  aiExampleLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 15,
    fontWeight: '600',
  },
  aiFeaturesList: {
    width: '100%',
  },
  aiFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F5FF',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#673AB7',
  },
  aiFeatureText: {
    fontSize: 14,
    color: '#673AB7',
    marginLeft: 12,
    fontWeight: '500',
  },
  appControlExample: {
    alignItems: 'center',
    marginTop: 20,
    width: '100%',
  },
  appControlLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 15,
    fontWeight: '600',
  },
  appControlFeaturesList: {
    width: '100%',
  },
  appControlFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FDF2F8',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#E91E63',
  },
  appControlFeatureText: {
    fontSize: 14,
    color: '#E91E63',
    marginLeft: 12,
    fontWeight: '500',
  },
  familyExample: {
    alignItems: 'center',
    marginTop: 20,
    width: '100%',
  },
  familyLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 15,
    fontWeight: '600',
  },
  familyStepsList: {
    width: '100%',
  },
  familyStepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0FDFF',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#00BCD4',
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#00BCD4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumberText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  familyStepText: {
    fontSize: 14,
    color: '#00BCD4',
    fontWeight: '500',
    flex: 1,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#CCCCCC',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: '#4630EB',
    width: 20,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  previousButton: {
    backgroundColor: 'transparent',
  },
  nextButton: {
    backgroundColor: '#4630EB',
    shadowColor: '#4630EB',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  buttonIcon: {
    marginLeft: 8,
  },
});

