import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ScrollView,
  Linking,
  Platform,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { useBilling } from '../../hooks/useBilling';
import { useTranslations } from '../../contexts/TranslationContext';

export default function SubscriptionManagementScreen() {
  const { user, subscriptionStatus, isSubscriptionLoading, refreshSubscriptionStatus } = useAuth();
  const { initialize, loadProducts } = useBilling();
  const [isRestoring, setIsRestoring] = React.useState(false);
  const [isTesting, setIsTesting] = React.useState(false);
  const { t } = useTranslations();

  const handleRestorePurchases = async () => {
    if (!user) return;

    try {
      setIsRestoring(true);
      // Using native billing service - restore functionality would need to be implemented
      Alert.alert(
        '<PERSON><PERSON><PERSON><PERSON>',
        'Funzionalità di ripristino non ancora implementata con il nuovo servizio nativo.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error restoring purchases:', error);
      Alert.alert(t.common.error, t.subscription.restoreError);
    } finally {
      setIsRestoring(false);
    }
  };

  const handleTestBillingConnection = async () => {
    if (!user) return;

    try {
      setIsTesting(true);

      // Test initialization and product loading
      const initResult = await initialize();
      if (initResult) {
        await loadProducts();
        Alert.alert(
          'Test Connessione Riuscito',
          'Connessione al Google Play Billing stabilita con successo!',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Test Connessione Fallito',
          'Impossibile connettersi al Google Play Billing.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error testing billing connection:', error);
      Alert.alert('Errore', 'Si è verificato un errore durante il test della connessione.');
    } finally {
      setIsTesting(false);
    }
  };

  const openSubscriptionSettings = () => {
    if (Platform.OS === 'ios') {
      Linking.openURL('https://apps.apple.com/account/subscriptions');
    } else {
      Linking.openURL('https://play.google.com/store/account/subscriptions');
    }
  };

  const renderSubscriptionDetails = () => {
    if (isSubscriptionLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4630EB" />
          <Text style={styles.loadingText}>{t.subscription.loadingDetails}</Text>
        </View>
      );
    }

    if (!subscriptionStatus || !subscriptionStatus.hasSubscription) {
      return (
        <View style={styles.noSubscriptionContainer}>
          <FontAwesome5 name="exclamation-circle" size={50} color="#F5A623" style={styles.noSubscriptionIcon} />
          <Text style={styles.noSubscriptionTitle}>{t.subscription.noSubscriptionTitle}</Text>
          <Text style={styles.noSubscriptionText}>{t.subscription.noSubscriptionMessage}</Text>
        </View>
      );
    }

    return (
      <View style={styles.detailsContainer}>
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Text style={styles.statusTitle}>{t.subscription.subscriptionStatus}</Text>
            <View style={[
              styles.statusBadge,
              subscriptionStatus.isActive ? styles.activeBadge : styles.inactiveBadge
            ]}>
              <Text style={styles.statusBadgeText}>
                {subscriptionStatus.isActive ? t.subscription.active : t.subscription.inactive}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>{t.subscription.plan}</Text>
            <Text style={styles.detailValue}>
              {subscriptionStatus.subscription?.subscription_plan ||
               (subscriptionStatus.status === 'trial' ? t.subscription.freeTrial : t.subscription.unknown)}
            </Text>
          </View>

          {subscriptionStatus.status === 'trial' && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>{t.subscription.daysRemaining}</Text>
              <Text style={styles.detailValue}>
                {subscriptionStatus.daysRemaining} {subscriptionStatus.daysRemaining === 1 ? t.subscription.day : t.subscription.days}
              </Text>
            </View>
          )}

          {subscriptionStatus.subscription?.trial_start_date && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>{t.subscription.trialStarted}</Text>
              <Text style={styles.detailValue}>
                {new Date(subscriptionStatus.subscription.trial_start_date).toLocaleDateString()}
              </Text>
            </View>
          )}

          {subscriptionStatus.subscription?.trial_end_date && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>{t.subscription.trialEnds}</Text>
              <Text style={styles.detailValue}>
                {new Date(subscriptionStatus.subscription.trial_end_date).toLocaleDateString()}
              </Text>
            </View>
          )}

          {subscriptionStatus.subscription?.subscription_start_date && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>{t.subscription.subscriptionStarted}</Text>
              <Text style={styles.detailValue}>
                {new Date(subscriptionStatus.subscription.subscription_start_date).toLocaleDateString()}
              </Text>
            </View>
          )}

          {subscriptionStatus.subscription?.subscription_end_date && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>{t.subscription.subscriptionEnds}</Text>
              <Text style={styles.detailValue}>
                {new Date(subscriptionStatus.subscription.subscription_end_date).toLocaleDateString()}
              </Text>
            </View>
          )}
        </View>

        {subscriptionStatus.status === 'active' && (
          <TouchableOpacity
            style={styles.manageButton}
            onPress={openSubscriptionSettings}
          >
            <FontAwesome5 name="cog" size={16} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.buttonText}>{t.subscription.manageSubscription}</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>{t.subscription.managementTitle}</Text>
        <Text style={styles.subtitle}>{t.subscription.managementSubtitle}</Text>
      </View>

      {renderSubscriptionDetails()}

      <TouchableOpacity
        style={styles.restoreButton}
        onPress={handleRestorePurchases}
        disabled={isRestoring}
      >
        {isRestoring ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : (
          <>
            <FontAwesome5 name="sync" size={16} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.buttonText}>{t.subscription.restorePurchases}</Text>
          </>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.testButton}
        onPress={handleTestBillingConnection}
        disabled={isTesting}
      >
        {isTesting ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : (
          <>
            <FontAwesome5 name="tools" size={16} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.buttonText}>Testa Connessione Google Play</Text>
          </>
        )}
      </TouchableOpacity>

      <View style={styles.infoContainer}>
        <FontAwesome5 name="info-circle" size={16} color="#757575" style={styles.infoIcon} />
        <Text style={styles.infoText}>{t.subscription.managementInfo}</Text>
      </View>

      {/* Subscription Plans Information */}
      <View style={styles.plansContainer}>
        <Text style={styles.plansTitle}>{t.subscription.availablePlans}</Text>

        <View style={[styles.planCard, styles.monthlyPlanCard]}>
          <View style={styles.planHeader}>
            <Text style={styles.planName}>{t.subscription.monthlyPlan}</Text>
            <View style={styles.trialBadge}>
              <Text style={styles.badgeText}>tre giorni di prova gratuita</Text>
            </View>
          </View>
          <Text style={styles.planPrice}>€3.99</Text>
          <Text style={styles.planBilling}>{t.subscription.billedMonthly}</Text>
          <Text style={styles.planDescription}>{t.subscription.monthlyDescription}</Text>
        </View>

        <View style={[styles.planCard, styles.yearlyPlanCard]}>
          <View style={styles.planHeader}>
            <Text style={styles.planName}>{t.subscription.yearlyPlan}</Text>
            <View style={styles.saveBadge}>
              <Text style={styles.saveBadgeText}>{t.subscription.savePercent}</Text>
            </View>
          </View>
          <Text style={styles.planPrice}>€15.99</Text>
          <Text style={styles.planBilling}>{t.subscription.billedAnnually}</Text>
          <Text style={styles.planDescription}>{t.subscription.yearlyDescription}</Text>
        </View>
      </View>

      {/* Premium Features */}
      <View style={styles.featuresContainer}>
        <Text style={styles.featuresTitle}>Funzionalità Premium Incluse</Text>
        <View style={styles.featureRow}>
          <FontAwesome5 name="map-marker-alt" size={18} color="#4630EB" style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>Tracciamento Posizione</Text>
            <Text style={styles.featureDescription}>Monitora la posizione dei tuoi figli in tempo reale</Text>
          </View>
        </View>
        <View style={styles.featureRow}>
          <FontAwesome5 name="map" size={18} color="#4630EB" style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>Zone Sicure</Text>
            <Text style={styles.featureDescription}>Crea zone sicure illimitate con notifiche</Text>
          </View>
        </View>
        <View style={styles.featureRow}>
          <FontAwesome5 name="exclamation-triangle" size={18} color="#4630EB" style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>Notifiche SOS</Text>
            <Text style={styles.featureDescription}>Ricevi notifiche immediate in caso di emergenza</Text>
          </View>
        </View>
        <View style={styles.featureRow}>
          <FontAwesome5 name="users" size={18} color="#4630EB" style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>Account Famiglia</Text>
            <Text style={styles.featureDescription}>Condividi l'abbonamento con il tuo partner</Text>
          </View>
        </View>
      </View>

      {/* Subscription Terms */}
      <View style={styles.termsContainer}>
        <Text style={styles.termsTitle}>{t.subscription.termsTitle}</Text>

        <View style={styles.trialHighlightContainer}>
          <FontAwesome5 name="exclamation-circle" size={16} color="#FF9800" style={styles.trialHighlightIcon} />
          <Text style={styles.trialHighlightText}>
            <Text style={styles.trialHighlightBold}>Importante:</Text> L'abbonamento mensile include 3 giorni di prova gratuita.
          </Text>
        </View>

        <View style={[styles.trialHighlightContainer, {backgroundColor: '#E8F5E9', borderLeftColor: '#4CAF50'}]}>
          <FontAwesome5 name="piggy-bank" size={16} color="#4CAF50" style={styles.trialHighlightIcon} />
          <Text style={styles.trialHighlightText}>
            <Text style={{fontWeight: 'bold', color: '#4CAF50'}}>L'abbonamento annuale offre un risparmio del 67%</Text> rispetto al pagamento mensile.
          </Text>
        </View>

        <View style={styles.termItem}>
          <FontAwesome5 name="check-circle" size={16} color="#4CAF50" style={styles.termIcon} />
          <Text style={styles.termText}>{t.subscription.subscriptionRequired}</Text>
        </View>

        <View style={styles.termItem}>
          <FontAwesome5 name="sync" size={16} color="#4630EB" style={styles.termIcon} />
          <Text style={styles.termText}>{t.subscription.autoRenewalNotice}</Text>
        </View>

        <View style={styles.termItem}>
          <FontAwesome5 name="credit-card" size={16} color="#F44336" style={styles.termIcon} />
          <Text style={styles.termText}>{t.subscription.paymentInfo}</Text>
        </View>

        <View style={styles.termItem}>
          <FontAwesome5 name="calendar-alt" size={16} color="#FF9800" style={styles.termIcon} />
          <Text style={styles.termText}>{t.subscription.cancellationInfo}</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 22,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 50,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666666',
  },
  noSubscriptionContainer: {
    alignItems: 'center',
    padding: 30,
    backgroundColor: '#F9F9F9',
    borderRadius: 12,
    marginBottom: 20,
  },
  noSubscriptionIcon: {
    marginBottom: 15,
  },
  noSubscriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 10,
    textAlign: 'center',
  },
  noSubscriptionText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  detailsContainer: {
    marginBottom: 20,
  },
  statusCard: {
    backgroundColor: '#F9F9F9',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
  },
  activeBadge: {
    backgroundColor: '#4CAF50',
  },
  inactiveBadge: {
    backgroundColor: '#F44336',
  },
  statusBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  detailLabel: {
    fontSize: 16,
    color: '#666666',
  },
  detailValue: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
  },
  manageButton: {
    backgroundColor: '#4630EB',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  restoreButton: {
    backgroundColor: '#757575',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  testButton: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  buttonIcon: {
    marginRight: 10,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F5F5F5',
    padding: 15,
    borderRadius: 8,
  },
  infoIcon: {
    marginRight: 10,
    marginTop: 2,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  plansContainer: {
    marginTop: 20,
    marginBottom: 20,
  },
  plansTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
  },
  planCard: {
    backgroundColor: '#F9F9F9',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    position: 'relative',
  },
  monthlyPlanCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  yearlyPlanCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  planName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 5,
  },
  planPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4630EB',
    marginBottom: 5,
  },
  planBilling: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  planDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  saveBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  trialBadge: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  saveBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  planHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  featuresContainer: {
    backgroundColor: '#F9F9F9',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  featureIcon: {
    marginRight: 15,
    marginTop: 2,
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  termsContainer: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 15,
    marginBottom: 20,
  },
  termsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
  },
  termItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  termIcon: {
    marginRight: 10,
    marginTop: 2,
  },
  termText: {
    flex: 1,
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
  trialHighlightContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFF3E0',
    padding: 10,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#FF9800',
    marginBottom: 15,
  },
  trialHighlightIcon: {
    marginRight: 10,
    marginTop: 2,
  },
  trialHighlightText: {
    flex: 1,
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
  trialHighlightBold: {
    fontWeight: 'bold',
    color: '#FF9800',
  },
});
