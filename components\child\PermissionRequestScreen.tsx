import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  Platform,
  Linking,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import * as Location from 'expo-location';
import { checkPermission, requestPermission } from '../../utils/permissions';

const PermissionRequestScreen = () => {
  const navigation = useNavigation();
  const [locationStatus, setLocationStatus] = useState('undetermined');
  const [backgroundLocationStatus, setBackgroundLocationStatus] = useState('undetermined');

  useEffect(() => {
    // Check current permission status
    const checkPermissions = async () => {
      const foregroundStatus = await checkPermission('location');
      const backgroundStatus = await checkPermission('backgroundLocation');

      setLocationStatus(foregroundStatus);
      setBackgroundLocationStatus(backgroundStatus);
    };

    checkPermissions();
  }, []);

  const handleRequestLocation = async () => {
    const status = await requestPermission('location');
    setLocationStatus(status);

    if (status === 'granted') {
      // If location is granted, then we can request background location
      handleRequestBackgroundLocation();
    }
  };

  const handleRequestBackgroundLocation = async () => {
    const status = await requestPermission('backgroundLocation');
    setBackgroundLocationStatus(status);
  };

  const openSettings = () => {
    if (Platform.OS === 'ios') {
      Linking.openURL('app-settings:');
    } else {
      Linking.openSettings();
    }
  };

  const allPermissionsGranted = locationStatus === 'granted' && backgroundLocationStatus === 'granted';

  const handleContinue = () => {
    if (allPermissionsGranted) {
      // Navigate to the next screen
      navigation.navigate('Main' as never);
    } else {
      Alert.alert(
        'Permessi necessari',
        'Per utilizzare l\'app è necessario concedere i permessi di localizzazione anche in background',
        [
          { text: 'Apri Impostazioni', onPress: openSettings },
          { text: 'Annulla', style: 'cancel' }
        ]
      );
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Image
          source={require('../../assets/location-permission.png')}
          style={styles.image}
          defaultSource={require('../../assets/location-permission-placeholder.png')}
        />

        <Text style={styles.title}>Ciao!</Text>

        <Text style={styles.description}>
          Per funzionare correttamente, KidSafety ha bisogno del tuo permesso per
          accedere alla tua posizione anche quando l'app non è aperta.
        </Text>

        <Text style={styles.description}>
          Questo permette ai tuoi genitori di sapere sempre dove sei, anche se il
          telefono è in tasca o nello zaino.
        </Text>

        <View style={styles.permissionCard}>
          <View style={styles.permissionHeader}>
            <Ionicons name="location" size={24} color="#4630EB" />
            <Text style={styles.permissionTitle}>Posizione</Text>
            {locationStatus === 'granted' ? (
              <Ionicons name="checkmark-circle" size={24} color="#34C759" />
            ) : (
              <TouchableOpacity onPress={handleRequestLocation}>
                <View style={styles.permissionButton}>
                  <Text style={styles.permissionButtonText}>Consenti</Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
          <Text style={styles.permissionDescription}>
            Permette all'app di vedere dove sei quando stai usando l'app
          </Text>
        </View>

        <View style={styles.permissionCard}>
          <View style={styles.permissionHeader}>
            <Ionicons name="locate" size={24} color="#4630EB" />
            <Text style={styles.permissionTitle}>Posizione in background</Text>
            {locationStatus !== 'granted' ? (
              <Text style={styles.permissionDisabled}>Prima attiva posizione</Text>
            ) : backgroundLocationStatus === 'granted' ? (
              <Ionicons name="checkmark-circle" size={24} color="#34C759" />
            ) : (
              <TouchableOpacity onPress={handleRequestBackgroundLocation}>
                <View style={styles.permissionButton}>
                  <Text style={styles.permissionButtonText}>Consenti</Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
          <Text style={styles.permissionDescription}>
            Permette all'app di vedere dove sei anche quando non stai usando l'app
          </Text>
        </View>

        <View style={styles.infoBox}>
          <Ionicons name="information-circle-outline" size={24} color="#4630EB" />
          <Text style={styles.infoText}>
            Su alcuni telefoni, dovrai confermare i permessi nelle impostazioni del sistema.
            Se vedi una notifica, seguila per completare la configurazione.
          </Text>
        </View>

        <TouchableOpacity
          style={[
            styles.continueButton,
            !allPermissionsGranted && styles.continueButtonDisabled
          ]}
          onPress={handleContinue}
        >
          <Text style={styles.continueButtonText}>
            {allPermissionsGranted ? 'Continua' : 'Attiva tutti i permessi'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    padding: 20,
    alignItems: 'center',
  },
  image: {
    width: 200,
    height: 200,
    resizeMode: 'contain',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#4630EB',
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 24,
  },
  permissionCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  permissionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    flex: 1,
  },
  permissionButton: {
    backgroundColor: '#4630EB',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  permissionButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  permissionDescription: {
    fontSize: 14,
    color: '#666',
  },
  permissionDisabled: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
  infoBox: {
    backgroundColor: '#E8EFFD',
    borderRadius: 12,
    padding: 16,
    marginVertical: 16,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
    marginLeft: 8,
    lineHeight: 20,
  },
  continueButton: {
    backgroundColor: '#4630EB',
    paddingHorizontal: 24,
    paddingVertical: 14,
    borderRadius: 30,
    width: '100%',
    alignItems: 'center',
    marginTop: 20,
  },
  continueButtonDisabled: {
    backgroundColor: '#A9A9A9',
  },
  continueButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  }
});

export default PermissionRequestScreen;