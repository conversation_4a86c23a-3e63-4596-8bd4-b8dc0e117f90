// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Configurazione ottimizzata per Expo SDK 53
config.resolver.sourceExts = [...config.resolver.sourceExts];
config.transformer.minifierPath = 'metro-minify-terser';
config.transformer.minifierConfig = {
  compress: { drop_console: false } // true in produzione
};

// SOLUZIONE UFFICIALE per SDK 53: Disabilita package.json exports
// Questo risolve i problemi di compatibilità con Supabase e altre librerie
// Fonte: https://github.com/expo/expo/discussions/36551
config.resolver.unstable_enablePackageExports = false;

// Polyfill per moduli Node.js richiesti da Supabase
config.resolver.alias = {
  ...config.resolver.alias,
  'crypto': require.resolve('expo-crypto'),
  'stream': require.resolve('readable-stream'),
  'buffer': require.resolve('buffer'),
};

module.exports = config;

