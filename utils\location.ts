﻿import * as Location from 'expo-location';
import * as TaskManager from 'expo-task-manager';
import * as Battery from 'expo-battery';
import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { saveLocationUpdate, addRoutePoint } from './supabase';
import { saveChildLocationSecure, logSecurityEvent } from './secureDatabase';

// Declare global variables for TypeScript
declare global {
  var userId: string;
}

// Task name for background location updates
const LOCATION_TRACKING_TASK = 'location-tracking';

// Distance in meters for significant location change (10 meters)
const SIGNIFICANT_DISTANCE = 10;

// Time interval for location updates in ms (30 seconds)
const UPDATE_INTERVAL = 30 * 1000;

// Earth radius in meters for distance calculations
const EARTH_RADIUS = 6371000;

// Define location task
TaskManager.defineTask(LOCATION_TRACKING_TASK, async ({ data, error }) => {
  if (error) {
    console.error('Location task error:', error);
    return;
  }

  if (!data) {
    console.warn('Location task received no data');
    return;
  }

  try {
    // Extract location data
    const { locations } = data as { locations: Location.LocationObject[] };
    const location = locations[0];

    if (!location) {
      console.warn('No location received');
      return;
    }

    // Get battery level
    const batteryLevel = await Battery.getBatteryLevelAsync();

    // Get child ID from SecureStore
    const childId = await getChildId();

    // Fallback to global variable if SecureStore fails
    const finalChildId = childId || global.userId;

    if (!finalChildId) {
      console.warn('No child ID found for location tracking');
      return;
    }

    console.log('Using child ID for location tracking:', finalChildId);

    // Save location to database
    await saveLocationUpdate(finalChildId, {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      accuracy: location.coords.accuracy ?? undefined,
      speed: location.coords.speed ?? undefined,
      battery: batteryLevel,
      timestamp: new Date(),
    });

    // Aggiungi il punto al tragitto
    try {
      await addRoutePoint(
        finalChildId,
        location.coords.latitude,
        location.coords.longitude,
        new Date(),
        location.coords.accuracy ?? undefined,
        batteryLevel
      );
      console.log('Route point added successfully');
    } catch (routeErr) {
      console.error('Error adding route point:', routeErr);
    }

    console.log('Location updated successfully in real-time');

    // Forza un nuovo aggiornamento dopo un breve intervallo per garantire aggiornamenti frequenti
    // anche quando l'app è in background
    setTimeout(async () => {
      try {
        // Ottieni una nuova posizione
        const newLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High
        });

        // Salva la nuova posizione
        const batteryLevel = await Battery.getBatteryLevelAsync();
        await saveLocationUpdate(finalChildId, {
          latitude: newLocation.coords.latitude,
          longitude: newLocation.coords.longitude,
          accuracy: newLocation.coords.accuracy ?? undefined,
          speed: newLocation.coords.speed ?? undefined,
          battery: batteryLevel,
          timestamp: new Date(),
        });

        // Aggiungi il punto al tragitto
        try {
          await addRoutePoint(
            finalChildId,
            newLocation.coords.latitude,
            newLocation.coords.longitude,
            new Date(),
            newLocation.coords.accuracy ?? undefined,
            batteryLevel
          );
          console.log('Route point added successfully (forced update)');
        } catch (routeErr) {
          console.error('Error adding route point (forced update):', routeErr);
        }

        console.log('Additional location update sent in real-time');
      } catch (updateErr) {
        console.error('Error sending additional location update:', updateErr);
      }
    }, 15000); // Invia un aggiornamento aggiuntivo dopo 15 secondi
  } catch (err) {
    console.error('Error in location tracking task:', err);
  }
});

// Chiave per memorizzare l'ID del bambino
const CHILD_ID_KEY = 'kidguard_location_tracking_child_id';

// Funzione per salvare l'ID del bambino in AsyncStorage e SecureStore
async function saveChildId(childId: string) {
  try {
    // Salva in AsyncStorage per i task di background
    await AsyncStorage.setItem(CHILD_ID_KEY, childId);

    // Salva anche in SecureStore per maggiore sicurezza nell'app principale
    await SecureStore.setItemAsync(CHILD_ID_KEY, childId);

    console.log('Child ID saved for location tracking:', childId);
    return true;
  } catch (error) {
    console.error('Error saving child ID:', error);
    return false;
  }
}

// Funzione per recuperare l'ID del bambino
async function getChildId(): Promise<string | null> {
  try {
    // Prima prova a recuperare da AsyncStorage
    let childId = await AsyncStorage.getItem(CHILD_ID_KEY);

    // Se non trovato in AsyncStorage, prova con SecureStore
    if (!childId) {
      childId = await SecureStore.getItemAsync(CHILD_ID_KEY);
    }

    console.log('Retrieved child ID for location tracking:', childId);
    return childId;
  } catch (error) {
    console.error('Error retrieving child ID:', error);
    return null;
  }
}

// Start location tracking in background
export async function startLocationTracking(userId: string) {
  try {
    // Store the user ID in global state for the background task
    global.userId = userId;

    // Store the user ID in SecureStore for persistence
    const saved = await saveChildId(userId);
    if (!saved) {
      console.warn('Failed to save child ID to SecureStore');
    }

    // Request permissions
    const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();

    if (foregroundStatus !== 'granted') {
      throw new Error('Foreground location permission denied');
    }

    const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();

    if (backgroundStatus !== 'granted') {
      throw new Error('Background location permission denied');
    }

    // Start location tracking
    await Location.startLocationUpdatesAsync(LOCATION_TRACKING_TASK, {
      accuracy: Location.Accuracy.High,
      timeInterval: UPDATE_INTERVAL,
      distanceInterval: SIGNIFICANT_DISTANCE,
      showsBackgroundLocationIndicator: true,
      foregroundService: {
        notificationTitle: 'Location Tracking Active',
        notificationBody: 'KidSafety is monitoring your location for your safety',
        notificationColor: '#4630EB',
      },
      // Ensure the task continues even when the app is in the background
      deferredUpdatesInterval: UPDATE_INTERVAL,
      deferredUpdatesDistance: SIGNIFICANT_DISTANCE,
    });

    return true;
  } catch (err) {
    console.error('Error starting location tracking:', err);
    throw err;
  }
}

// Stop location tracking
export async function stopLocationTracking() {
  try {
    if (await TaskManager.isTaskRegisteredAsync(LOCATION_TRACKING_TASK)) {
      await Location.stopLocationUpdatesAsync(LOCATION_TRACKING_TASK);
    }

    // Clear the child ID from storage
    try {
      // Rimuovi da AsyncStorage
      await AsyncStorage.removeItem(CHILD_ID_KEY);

      // Rimuovi anche da SecureStore
      await SecureStore.deleteItemAsync(CHILD_ID_KEY);

      console.log('Child ID removed from location tracking');
    } catch (clearErr) {
      console.error('Error clearing child ID:', clearErr);
    }

    // Clear the global variable
    global.userId = '';

    return true;
  } catch (err) {
    console.error('Error stopping location tracking:', err);
    throw err;
  }
}

// Get current location
export async function getCurrentLocation() {
  try {
    const { status } = await Location.requestForegroundPermissionsAsync();

    if (status !== 'granted') {
      throw new Error('Location permission denied');
    }

    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.High,
    });

    return {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      accuracy: location.coords.accuracy,
      speed: location.coords.speed,
    };
  } catch (err) {
    console.error('Error getting current location:', err);
    throw err;
  }
}

// Calculate distance between two points in meters
export function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number) {
  // Convert to radians
  const toRad = (value: number) => (value * Math.PI) / 180;

  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = EARTH_RADIUS * c;

  return distance;
}

// Check if a location is within a safe zone
export function isLocationInSafeZone(
  location: { latitude: number; longitude: number },
  safeZone: { latitude: number; longitude: number; radius: number }
) {
  const distance = calculateDistance(
    location.latitude,
    location.longitude,
    safeZone.latitude,
    safeZone.longitude
  );

  return distance <= safeZone.radius;
}

// Get geocoded address from coordinates
export async function getAddressFromCoordinates(latitude: number, longitude: number) {
  try {
    const addressResponse = await Location.reverseGeocodeAsync({
      latitude,
      longitude,
    });

    if (addressResponse?.length > 0) {
      const address = addressResponse[0];
      return {
        street: address.street,
        city: address.city,
        region: address.region,
        country: address.country,
        postalCode: address.postalCode,
        formattedAddress: [
          address.street,
          address.city,
          address.region,
          address.country,
        ]
          .filter(Boolean)
          .join(', '),
      };
    }

    return null;
  } catch (err) {
    console.error('Error getting address from coordinates:', err);
    return null;
  }
}
