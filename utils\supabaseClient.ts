import { createClient } from '@supabase/supabase-js';
import * as SecureStore from 'expo-secure-store';
import 'react-native-url-polyfill/auto';

// Load environment variables using Expo SDK 53 native support
const EXPO_PUBLIC_SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL || '';
const EXPO_PUBLIC_SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || '';

// SecureStore adapter for Supabase storage
const ExpoSecureStoreAdapter = {
  getItem: (key: string) => {
    return SecureStore.getItemAsync(key);
  },
  setItem: (key: string, value: string) => {
    SecureStore.setItemAsync(key, value);
  },
  removeItem: (key: string) => {
    SecureStore.deleteItemAsync(key);
  },
};

// Log environment variables for debugging
console.log('🔧 Caricamento variabili d\'ambiente dal file .env (SecureStore):', {
  SUPABASE_URL: EXPO_PUBLIC_SUPABASE_URL ? EXPO_PUBLIC_SUPABASE_URL.substring(0, 30) + '...' : '❌ NON TROVATA',
  SUPABASE_ANON_KEY: EXPO_PUBLIC_SUPABASE_ANON_KEY ? EXPO_PUBLIC_SUPABASE_ANON_KEY.substring(0, 30) + '...' : '❌ NON TROVATA',
});

// Check if we have valid environment variables
if (!EXPO_PUBLIC_SUPABASE_URL || !EXPO_PUBLIC_SUPABASE_ANON_KEY) {
  console.error('❌ Variabili d\'ambiente Supabase mancanti nel file .env');
}

// Create Supabase client
export const supabase = createClient(EXPO_PUBLIC_SUPABASE_URL, EXPO_PUBLIC_SUPABASE_ANON_KEY, {
  auth: {
    storage: ExpoSecureStoreAdapter,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
