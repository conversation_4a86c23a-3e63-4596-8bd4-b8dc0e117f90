import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Easing,
  Dimensions,
  TouchableOpacity,
  Modal
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import LottieView from 'lottie-react-native';
import * as Haptics from 'expo-haptics';

const { width, height } = Dimensions.get('window');

interface MissionRewardAnimationProps {
  visible: boolean;
  onClose: () => void;
  missionTitle: string;
  rewardPoints: number;
}

const MissionRewardAnimation: React.FC<MissionRewardAnimationProps> = ({
  visible,
  onClose,
  missionTitle,
  rewardPoints
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.5)).current;
  const starAnim = useRef(new Animated.Value(0)).current;
  const [stars, setStars] = useState<{ id: number; left: number; top: number; size: number; delay: number; rotation: number }[]>([]);
  const lottieRef = useRef<LottieView>(null);

  useEffect(() => {
    if (visible) {
      // Generate random stars
      const newStars = Array.from({ length: Math.min(rewardPoints, 50) }, (_, i) => ({
        id: i,
        left: Math.random() * width * 0.8,
        top: Math.random() * height * 0.6 + height * 0.2,
        size: Math.random() * 20 + 10,
        delay: Math.random() * 1000,
        rotation: Math.random() * 360
      }));
      setStars(newStars);

      // Play haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Start animations
      Animated.sequence([
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 500,
            easing: Easing.elastic(1.2),
            useNativeDriver: true,
          })
        ]),
        Animated.timing(starAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      ]).start();

      // Play lottie animation
      if (lottieRef.current) {
        lottieRef.current.play();
      }
    } else {
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.5);
      starAnim.setValue(0);
    }
  }, [visible, fadeAnim, scaleAnim, starAnim, rewardPoints]);

  if (!visible) return null;

  return (
    <Modal
      transparent={true}
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <Animated.View
          style={[
            styles.contentContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }]
            }
          ]}
        >
          <View style={styles.confettiContainer}>
            <LottieView
              ref={lottieRef}
              source={require('../../assets/animations/confetti.json')}
              style={styles.lottie}
              autoPlay
              loop={false}
            />
          </View>

          <Text style={styles.congratsText}>Mission Verified!</Text>
          <Text style={styles.missionTitle}>{missionTitle}</Text>

          <View style={styles.rewardContainer}>
            <Text style={styles.rewardText}>You earned</Text>
            <View style={styles.pointsContainer}>
              <FontAwesome5 name="star" size={24} color="#FFD700" />
              <Text style={styles.pointsText}>{rewardPoints}</Text>
            </View>
            <Text style={styles.rewardText}>stars!</Text>
          </View>

          {/* Animated stars */}
          {stars.map((star) => (
            <Animated.View
              key={star.id}
              style={[
                styles.star,
                {
                  left: star.left,
                  top: star.top,
                  width: star.size,
                  height: star.size,
                  opacity: starAnim.interpolate({
                    inputRange: [0, 0.2, 0.8, 1],
                    outputRange: [0, 1, 1, 0]
                  }),
                  transform: [
                    {
                      scale: starAnim.interpolate({
                        inputRange: [0, 0.5, 1],
                        outputRange: [0, 1, 0]
                      })
                    },
                    {
                      rotate: `${star.rotation}deg`
                    }
                  ]
                }
              ]}
            >
              <FontAwesome5 name="star" size={star.size} color="#FFD700" />
            </Animated.View>
          ))}

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Continue</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  contentContainer: {
    width: width * 0.85,
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  confettiContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  lottie: {
    width: width,
    height: height,
    position: 'absolute',
    top: -height * 0.3,
    left: -width * 0.1,
  },
  congratsText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4630EB',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  missionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
    paddingHorizontal: 10,
  },
  rewardContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  rewardText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 10,
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  pointsText: {
    fontSize: 40,
    fontWeight: 'bold',
    color: '#FFD700',
    marginLeft: 10,
  },
  star: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    backgroundColor: '#4630EB',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 30,
    marginTop: 20,
    marginBottom: 10,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default MissionRewardAnimation;
