import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import { checkAppTimeLimit, getBlockedApps } from './supabase';
import { sendPushNotification } from '../lib/notifications';

// Check if an app is blocked or has exceeded its time limit
export const checkAppRestrictions = async (
  childId: string,
  appName: string,
  parentId: string,
  childName: string
): Promise<{ isRestricted: boolean; reason: string }> => {
  try {
    // Check if the app is blocked
    const blockedApps = await getBlockedApps(childId);
    if (blockedApps[appName]) {
      // Send notification to parent
      await sendAppBlockedNotification(parentId, childId, childName, appName, 'blocked');
      return { isRestricted: true, reason: 'blocked' };
    }

    // Check if the app has exceeded its time limit
    const timeLimitInfo = await checkAppTimeLimit(childId, appName);
    if (timeLimitInfo.hasLimit && timeLimitInfo.limitExceeded) {
      // Send notification to parent
      await sendAppBlockedNotification(parentId, childId, childName, appName, 'time_limit');
      return { isRestricted: true, reason: 'time_limit' };
    }

    return { isRestricted: false, reason: '' };
  } catch (error) {
    console.error('Error checking app restrictions:', error);
    return { isRestricted: false, reason: '' };
  }
};

// Send a local notification to the child
export const sendLocalNotification = async (
  title: string,
  body: string,
  data: any = {}
): Promise<void> => {
  try {
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('app-restrictions', {
        name: 'App Restrictions',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: true,
      },
      trigger: null, // Send immediately
    });
  } catch (error) {
    console.error('Error sending local notification:', error);
  }
};

// Send a push notification to the parent
export const sendAppBlockedNotification = async (
  parentId: string,
  childId: string,
  childName: string,
  appName: string,
  reason: 'blocked' | 'time_limit'
): Promise<void> => {
  try {
    const title = reason === 'blocked'
      ? `${childName} tried to use blocked app`
      : `${childName} exceeded time limit`;
      
    const body = reason === 'blocked'
      ? `${childName} attempted to use ${appName}, which is blocked.`
      : `${childName} has exceeded the daily time limit for ${appName}.`;

    await sendPushNotification(
      parentId,
      title,
      body,
      {
        type: 'app_restriction',
        childId,
        childName,
        appName,
        reason,
        timestamp: new Date().toISOString()
      }
    );
  } catch (error) {
    console.error('Error sending app blocked notification:', error);
  }
};
