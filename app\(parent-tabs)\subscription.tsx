import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import Header from '../../components/shared/Header';
import SubscriptionScreen from '../../components/subscription/SubscriptionScreen';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import { useTranslations } from '../../contexts/TranslationContext';

export default function SubscriptionPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const { t } = useTranslations();

  // If no user is logged in, redirect to login
  if (!user && !isLoading) {
    router.replace('/auth/parent-login');
    return null;
  }

  if (isLoading) {
    return <LoadingIndicator fullScreen text={t.common.loading} />;
  }

  return (
    <View style={styles.container}>
      <Header title={t.subscription.title} showBackButton />
      <SubscriptionScreen />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});
