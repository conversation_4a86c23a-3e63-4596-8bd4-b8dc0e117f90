import React, { useState, useEffect, useRef } from 'react';

// Dichiarazione per TypeScript
declare global {
  var lastSelectedLocation: { latitude: number; longitude: number } | null;
}
import { View, Text, StyleSheet, TouchableOpacity, Alert, Modal, TextInput, ScrollView, ActivityIndicator } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { Marker, Circle } from 'react-native-maps';
import GoogleMapView from '../../components/shared/GoogleMapView';
import { useAuth } from '../../contexts/AuthContext';
import { getChildrenForParent, getSafeZones, createSafeZone, assignSafeZoneToChild, deleteSafeZone } from '../../utils/supabase';
// Import sendZoneNotification from the correct path
import * as Notifications from 'expo-notifications';
import Header from '../../components/shared/Header';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import { useTranslations } from '../../contexts/TranslationContext';

interface Child {
  child_id: string;
  users: {
    name: string;
  };
}

interface SafeZone {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  radius: number;
  address?: string;
  children?: Child[];
}

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

// Function to send zone notification
async function sendZoneNotification(childName: string, zoneName: string, action: 'entered' | 'exited', translations: any) {
  try {
    const title = `${childName} ${action === 'entered' ? translations.safeZone.entered : translations.safeZone.exited} ${zoneName}`;
    const body = action === 'entered'
      ? translations.safeZone.enteredMessage.replace('{childName}', childName).replace('{zoneName}', zoneName)
      : translations.safeZone.exitedMessage.replace('{childName}', childName).replace('{zoneName}', zoneName);

    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: {
          type: 'zone',
          childName,
          zoneName,
          action,
          timestamp: new Date().toISOString(),
        },
      },
      trigger: null, // null means show immediately
    });

    console.log(`Notification sent: ${childName} ${action} ${zoneName}`);
  } catch (error) {
    console.error('Error sending notification:', error);
  }
}


function SafeZonesScreen() {
  const { user } = useAuth();
  const { t } = useTranslations();
  const [children, setChildren] = useState<Child[]>([]);
  const [safeZones, setSafeZones] = useState<SafeZone[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [region, setRegion] = useState({
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  // Modal states
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedChildren, setSelectedChildren] = useState<Child[]>([]);
  const [childSelectVisible, setChildSelectVisible] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<{latitude: number, longitude: number} | null>(null);

  // New safe zone form state
  const [newSafeZone, setNewSafeZone] = useState({
    name: '',
    radius: '100',
    address: '',
  });

  const mapRef = useRef<any>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);

      // Get children data
      const childrenData = await getChildrenForParent(user.id);
      setChildren(childrenData);

      // Get safe zones from the database
      try {
        const zones = await getSafeZones(user.id);
        console.log('Safe zones loaded:', zones);

        // Verifica se i dati sono già nel formato corretto (array di oggetti)
        if (Array.isArray(zones)) {
          // Prepara le zone sicure con i dati dei bambini
          const safeZonesWithChildren = zones.map(zone => {
            // Se la zona ha già i bambini assegnati, usali
            if (zone.children) {
              return zone;
            }

            // Altrimenti, assegna bambini casuali
            const randomChildren = childrenData.length > 0
              ? childrenData.slice(0, Math.floor(Math.random() * childrenData.length) + 1)
              : [];

            return {
              ...zone,
              children: randomChildren
            };
          });

          setSafeZones(safeZonesWithChildren);
        } else {
          // Se i dati non sono un array, potrebbe essere un oggetto JSON
          console.log('Zones data is not an array, trying to parse:', zones);

          try {
            // Se è una stringa JSON, prova a convertirla
            const parsedZones = typeof zones === 'string' ? JSON.parse(zones) : zones;

            if (Array.isArray(parsedZones)) {
              setSafeZones(parsedZones);
            } else {
              throw new Error('Invalid data format');
            }
          } catch (parseError) {
            console.error('Error parsing zones data:', parseError);
            throw parseError;
          }
        }
      } catch (error) {
        console.error('Error loading safe zones:', error);

        // Fallback to mock data if there's an error
        const mockSafeZones: SafeZone[] = [
          {
            id: '1',
            name: 'Home',
            latitude: region.latitude,
            longitude: region.longitude,
            radius: 100,
            address: '123 Main St',
            children: childrenData.slice(0, 1)
          },
          {
            id: '2',
            name: 'School',
            latitude: region.latitude + 0.01,
            longitude: region.longitude + 0.01,
            radius: 200,
            address: '456 School Ave',
            children: childrenData
          }
        ];

        setSafeZones(mockSafeZones);
      }
    } catch (err) {
      console.error('Error loading data:', err);
      Alert.alert(t.common.error, t.safeZone.errorLoading || 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMapPress = (event: any) => {
    try {
      console.log('Map pressed');
      // Get the coordinate from the event
      const { coordinate } = event.nativeEvent;
      console.log('Coordinate:', coordinate);

      // Always set the selected location, regardless of modal state
      setSelectedLocation(coordinate);

      // Get address from coordinates (reverse geocoding)
      // In a real app, you would use a geocoding service
      setNewSafeZone({
        ...newSafeZone,
        address: 'Address will be shown here'
      });

      // Store the selected location in a global variable for debugging
      global.lastSelectedLocation = coordinate;
      console.log('Location stored in global variable:', global.lastSelectedLocation);

      // Provide feedback to the user
      Alert.alert(
        t.safeZone.locationSelected || 'Location Selected',
        t.safeZone.locationSelectedMessage || 'You have selected a location for your safe zone. Now tap the "Add Safe Zone" button to continue.',
        [{ text: t.common.ok }]
      );
    } catch (error) {
      console.error('Error handling map press:', error);
      Alert.alert(t.common.error, t.safeZone.locationError || 'Failed to select location. Please try again.');
    }
  };

  const handleCreateSafeZone = async () => {
    console.log('handleCreateSafeZone called');
    console.log('selectedLocation:', selectedLocation);
    console.log('newSafeZone:', newSafeZone);

    // Validazione dei dati
    if (!selectedLocation) {
      console.log('No location selected');
      Alert.alert(t.common.error, t.safeZone.noLocationSelected || 'Please select a location on the map');
      return;
    }

    if (!newSafeZone.name.trim()) {
      console.log('No name entered');
      Alert.alert(t.common.error, t.safeZone.noNameEntered || 'Please enter a name for the safe zone');
      return;
    }

    try {
      console.log('Creating safe zone...');
      setIsLoading(true);

      // Create the safe zone in the database
      const zoneData = {
        name: newSafeZone.name,
        latitude: selectedLocation.latitude,
        longitude: selectedLocation.longitude,
        radius: parseInt(newSafeZone.radius) || 100, // Default to 100 if parsing fails
        address: newSafeZone.address || 'Address not available'
      };

      console.log('Zone data:', zoneData);

      // Create the safe zone
      let createdZoneData;
      try {
        console.log('Calling createSafeZone API with user ID:', user.id);
        const createdZone = await createSafeZone(user.id, zoneData);
        console.log('Safe zone created successfully:', createdZone);
        createdZoneData = createdZone;

        // Assign children to the safe zone
        console.log('Assigning children to safe zone:', selectedChildren.length);
        for (const child of selectedChildren) {
          try {
            await assignSafeZoneToChild(createdZone.id, child.child_id);
            console.log('Child assigned to safe zone:', child.child_id);
          } catch (assignError) {
            console.error('Error assigning child to safe zone:', assignError);
          }
        }
      } catch (createError) {
        console.error('Error creating safe zone in database:', createError);

        // Fallback to mock data if there's an error
        console.log('Using fallback mock data');
        createdZoneData = {
          id: Date.now().toString(),
          name: zoneData.name,
          latitude: zoneData.latitude,
          longitude: zoneData.longitude,
          radius: zoneData.radius,
          address: zoneData.address
        };
      }

      // Add the new zone to the state
      const newZone: SafeZone = {
        ...createdZoneData,
        children: selectedChildren
      };

      console.log('Adding new zone to state:', newZone);
      setSafeZones(prevZones => [...prevZones, newZone]);

      // Send notification for each selected child
      console.log('Sending notifications');
      for (const child of selectedChildren) {
        try {
          await sendZoneNotification(child.users.name, newSafeZone.name, 'entered', t);
          console.log('Notification sent for child:', child.users.name);
        } catch (error) {
          console.error('Error sending notification:', error);
        }
      }

      // Reset form and close modal
      console.log('Resetting form and closing modal');
      setNewSafeZone({
        name: '',
        radius: '100',
        address: '',
      });
      setSelectedLocation(null);
      setSelectedChildren([]);
      setModalVisible(false);

      Alert.alert(t.common.success, t.safeZone.createSuccess || 'Safe zone created successfully!');
    } catch (err) {
      console.error('Error creating safe zone:', err);
      Alert.alert(t.common.error, (t.safeZone.createError || 'Failed to create safe zone: ') + (err instanceof Error ? err.message : String(err)));
    } finally {
      setIsLoading(false);
    }
  };



  const toggleChildSelection = (child: Child) => {
    if (selectedChildren.some(c => c.child_id === child.child_id)) {
      setSelectedChildren(selectedChildren.filter(c => c.child_id !== child.child_id));
    } else {
      setSelectedChildren([...selectedChildren, child]);
    }
  };

  const handleDeleteSafeZone = (zone: SafeZone) => {
    Alert.alert(
      t.safeZone.deleteSafeZone,
      (t.safeZone.confirmDelete.message || `Are you sure you want to delete the "{name}" safe zone? This action cannot be undone.`).replace('{name}', zone.name),
      [
        {
          text: t.safeZone.confirmDelete.cancel,
          style: 'cancel',
        },
        {
          text: t.safeZone.confirmDelete.delete,
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              await deleteSafeZone(zone.id);
              setSafeZones(prevZones => prevZones.filter(z => z.id !== zone.id));
              Alert.alert(t.common.success, t.safeZone.deleteSuccess || 'Safe zone deleted successfully');
            } catch (error) {
              console.error('Error deleting safe zone:', error);
              Alert.alert(t.common.error, t.safeZone.deleteError || 'Failed to delete safe zone. Please try again.');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <View style={styles.container}>
      <Header titleKey="safeZone.title" />

      <View style={styles.mapContainer}>
        <GoogleMapView
          ref={mapRef}
          style={styles.map}
          initialRegion={region}
          onPress={handleMapPress}
        >
          {safeZones.map((zone) => (
            <React.Fragment key={zone.id}>
              <Circle
                center={{
                  latitude: zone.latitude,
                  longitude: zone.longitude,
                }}
                radius={zone.radius}
                fillColor="rgba(70, 48, 235, 0.1)"
                strokeColor="rgba(70, 48, 235, 0.5)"
              />
              <Marker
                coordinate={{
                  latitude: zone.latitude,
                  longitude: zone.longitude,
                }}
                title={zone.name}
                description={`${zone.children?.length || 0} ${t.safeZone.childrenAssigned || 'children assigned'}`}
              >
                <View style={styles.markerContainer}>
                  <FontAwesome5 name="shield-alt" size={20} color="#4630EB" />
                </View>
              </Marker>
            </React.Fragment>
          ))}

          {/* Istruzioni rimosse dalla mappa per evitare interferenze con gli eventi di tocco */}

          {selectedLocation && modalVisible && (
            <React.Fragment>
              <Circle
                center={selectedLocation}
                radius={parseInt(newSafeZone.radius)}
                fillColor="rgba(70, 48, 235, 0.1)"
                strokeColor="rgba(70, 48, 235, 0.5)"
              />
              <Marker
                coordinate={selectedLocation}
                title={t.safeZone.newSafeZone || "New Safe Zone"}
              >
                <View style={styles.markerContainer}>
                  <FontAwesome5 name="shield-alt" size={20} color="#4630EB" />
                </View>
              </Marker>
            </React.Fragment>
          )}
        </GoogleMapView>

        <View style={styles.zoneListContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {safeZones.map((zone) => (
              <TouchableOpacity
                key={zone.id}
                style={styles.zoneCard}
                onPress={() => {
                  mapRef.current?.animateToRegion({
                    latitude: zone.latitude,
                    longitude: zone.longitude,
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                  });
                }}
              >
                <View style={styles.zoneIconContainer}>
                  <FontAwesome5 name="shield-alt" size={16} color="#4630EB" />
                </View>
                <View style={styles.zoneInfo}>
                  <Text style={styles.zoneName}>{zone.name}</Text>
                  <Text style={styles.zoneDetails}>
                    {zone.children?.length || 0} {t.child.title === 'Child' ? 'children' : 'bambini'}
                  </Text>
                </View>
                <View style={styles.zoneActions}>
                  <TouchableOpacity
                    style={styles.viewDetailsButton}
                    onPress={() => {
                      // Mostra i dettagli della zona sicura
                      Alert.alert(
                        `${zone.name} Details`,
                        `Radius: ${zone.radius}m\nChildren: ${zone.children?.length || 0}\n\nReal-time notifications will be sent automatically when a child enters or leaves this zone.`,
                        [{ text: 'OK' }]
                      );
                    }}
                  >
                    <Text style={styles.viewDetailsButtonText}>{t.common.details || 'Details'}</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={() => handleDeleteSafeZone(zone)}
                  >
                    <FontAwesome5 name="trash" size={16} color="#FF3B30" />
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <TouchableOpacity
          style={styles.addButton}
          onPress={() => {
            try {
              console.log('Opening safe zone modal');
              console.log('Current selectedLocation:', selectedLocation);
              console.log('Global lastSelectedLocation:', global.lastSelectedLocation);

              // Mantieni la posizione selezionata se esiste
              const currentLocation = selectedLocation || global.lastSelectedLocation;

              // Reset form state before opening modal
              setNewSafeZone({
                name: '',
                radius: '100',
                address: '',
              });

              // Usa la posizione memorizzata se disponibile
              if (currentLocation) {
                console.log('Using stored location:', currentLocation);
                setSelectedLocation(currentLocation);
              }

              setSelectedChildren([]);
              setModalVisible(true);
            } catch (error) {
              console.error('Error opening safe zone modal:', error);
              Alert.alert('Error', 'Failed to open safe zone creation screen. Please try again.');
            }
          }}
        >
          <FontAwesome5 name="plus" size={20} color="#fff" />
          <Text style={styles.addButtonText}>{t.safeZone.addSafeZone}</Text>
        </TouchableOpacity>
      </View>

      {/* Create Safe Zone Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t.safeZone.createSafeZone || 'Create Safe Zone'}</Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <FontAwesome5 name="times" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={styles.instructionContainer}>
                <Text style={styles.instructionTitle}>{t.safeZone.howToCreate || 'How to create a Safe Zone:'}</Text>
                <View style={styles.instructionStep}>
                  <Text style={styles.instructionNumber}>1</Text>
                  <Text style={styles.instructionText}>{t.safeZone.instruction1 || 'Close this popup by tapping "Cancel"'}</Text>
                </View>
                <View style={styles.instructionStep}>
                  <Text style={styles.instructionNumber}>2</Text>
                  <Text style={styles.instructionText}>{t.safeZone.instruction2 || 'Tap on the map to select a location'}</Text>
                </View>
                <View style={styles.instructionStep}>
                  <Text style={styles.instructionNumber}>3</Text>
                  <Text style={styles.instructionText}>{t.safeZone.instruction3 || 'Tap the "Add Safe Zone" button again'}</Text>
                </View>
                <View style={styles.instructionStep}>
                  <Text style={styles.instructionNumber}>4</Text>
                  <Text style={styles.instructionText}>{t.safeZone.instruction4 || 'Fill in the details and create your zone'}</Text>
                </View>
              </View>

              {selectedLocation ? (
                <View style={styles.locationSelected}>
                  <FontAwesome5 name="check-circle" size={16} color="#4CAF50" />
                  <Text style={styles.locationSelectedText}>
                    {t.safeZone.locationSelectedConfirm || 'Location selected! You can adjust it by tapping elsewhere on the map.'}
                  </Text>
                </View>
              ) : (
                <View style={styles.locationWarning}>
                  <FontAwesome5 name="exclamation-triangle" size={16} color="#FF9800" />
                  <Text style={styles.locationWarningText}>
                    {t.safeZone.noLocationSelectedWarning || 'No location selected yet. Please close this popup and tap on the map first.'}
                  </Text>
                </View>
              )}

              <Text style={styles.inputLabel}>{t.safeZone.zoneName || 'Zone Name'}</Text>
              <TextInput
                style={styles.input}
                value={newSafeZone.name}
                onChangeText={(text) => setNewSafeZone({ ...newSafeZone, name: text })}
                placeholder={t.safeZone.enterZoneName || "Enter zone name (e.g. Home, School)"}
              />

              <Text style={styles.inputLabel}>{t.safeZone.radius || 'Radius (meters)'}</Text>
              <TextInput
                style={styles.input}
                value={newSafeZone.radius}
                onChangeText={(text) => setNewSafeZone({ ...newSafeZone, radius: text.replace(/[^0-9]/g, '') })}
                placeholder={t.safeZone.enterRadius || "Enter radius in meters"}
                keyboardType="numeric"
              />

              {selectedLocation && (
                <View style={styles.locationInfo}>
                  <Text style={styles.locationTitle}>Selected Location</Text>
                  <Text style={styles.locationText}>
                    Latitude: {selectedLocation.latitude.toFixed(6)}
                  </Text>
                  <Text style={styles.locationText}>
                    Longitude: {selectedLocation.longitude.toFixed(6)}
                  </Text>
                  <Text style={styles.locationText}>
                    Address: {newSafeZone.address || 'Not available'}
                  </Text>
                </View>
              )}

              <Text style={styles.inputLabel}>Assign Children</Text>
              <TouchableOpacity
                style={styles.childPickerButton}
                onPress={() => setChildSelectVisible(true)}
              >
                <Text style={styles.childPickerText}>
                  {selectedChildren.length > 0
                    ? `${selectedChildren.length} children selected`
                    : 'Select children'}
                </Text>
                <FontAwesome5 name="chevron-down" size={16} color="#4630EB" />
              </TouchableOpacity>

              {selectedChildren.length > 0 && (
                <View style={styles.selectedChildrenContainer}>
                  {selectedChildren.map((child) => (
                    <View key={child.child_id} style={styles.selectedChildChip}>
                      <Text style={styles.selectedChildName}>{child.users.name}</Text>
                      <TouchableOpacity
                        onPress={() => toggleChildSelection(child)}
                        style={styles.removeChildButton}
                      >
                        <FontAwesome5 name="times" size={12} color="#fff" />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.createButton, (!selectedLocation || isLoading) && styles.disabledButton]}
                disabled={!selectedLocation || isLoading}
                onPress={() => {
                  console.log('Create Zone button pressed');
                  console.log('selectedLocation at press time:', selectedLocation);
                  console.log('global.lastSelectedLocation at press time:', global.lastSelectedLocation);

                  // Usa la posizione memorizzata se disponibile
                  if (!selectedLocation && global.lastSelectedLocation) {
                    console.log('Using global location');
                    setSelectedLocation(global.lastSelectedLocation);
                    setTimeout(() => handleCreateSafeZone(), 100);
                  } else if (selectedLocation && !isLoading) {
                    console.log('Using current location');
                    handleCreateSafeZone();
                  } else {
                    Alert.alert(
                      'No Location Selected',
                      'Please close this popup, tap on the map to select a location, then open this popup again.',
                      [{ text: 'OK' }]
                    );
                  }
                }}
              >
                {isLoading ? (
                  <View style={styles.buttonContentWithLoader}>
                    <ActivityIndicator size="small" color="#fff" />
                    <Text style={styles.createButtonText}>Creating...</Text>
                  </View>
                ) : (
                  <Text style={styles.createButtonText}>
                    {selectedLocation || global.lastSelectedLocation ? 'Create Zone' : 'Need Location'}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Child Selection Modal */}
      <Modal
        visible={childSelectVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setChildSelectVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, styles.childSelectModal]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Children</Text>
              <TouchableOpacity onPress={() => setChildSelectVisible(false)}>
                <FontAwesome5 name="times" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              {children.length === 0 ? (
                <Text style={styles.noChildrenText}>No children added yet</Text>
              ) : (
                children.map((child) => (
                  <TouchableOpacity
                    key={child.child_id}
                    style={styles.childOption}
                    onPress={() => toggleChildSelection(child)}
                  >
                    <View style={styles.childIconContainer}>
                      <FontAwesome5 name="child" size={20} color="#4630EB" />
                    </View>
                    <Text style={styles.childOptionText}>{child.users.name}</Text>
                    {selectedChildren.some(c => c.child_id === child.child_id) && (
                      <FontAwesome5 name="check" size={16} color="#4630EB" />
                    )}
                  </TouchableOpacity>
                ))
              )}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setChildSelectVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.createButton]}
                onPress={() => setChildSelectVisible(false)}
              >
                <Text style={styles.createButtonText}>Confirm Selection</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  markerContainer: {
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#4630EB',
  },
  zoneListContainer: {
    position: 'absolute',
    top: 16,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
  },
  zoneCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  zoneIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  zoneInfo: {
    flex: 1,
  },
  zoneName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  zoneDetails: {
    fontSize: 12,
    color: '#666',
  },
  addButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 30,
    backgroundColor: '#4630EB',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  childSelectModal: {
    maxHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  modalContent: {
    padding: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
  },
  locationInfo: {
    backgroundColor: '#f0f0ff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  locationTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  childPickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  childPickerText: {
    fontSize: 16,
    color: '#333',
  },
  selectedChildrenContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  selectedChildChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4630EB',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedChildName: {
    fontSize: 14,
    color: '#fff',
    marginRight: 8,
  },
  removeChildButton: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#666',
    fontWeight: 'bold',
  },
  createButton: {
    backgroundColor: '#4630EB',
    marginLeft: 8,
  },
  createButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: '#B0BEC5',
    opacity: 0.7,
  },
  childOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  childIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  childOptionText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  noChildrenText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
  simulateButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginLeft: 8,
  },
  simulateButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  viewDetailsButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginLeft: 8,
  },
  viewDetailsButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  locationWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  locationWarningText: {
    fontSize: 14,
    color: '#FF9800',
    marginLeft: 8,
    flex: 1,
  },
  locationSelected: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  locationSelectedText: {
    fontSize: 14,
    color: '#4CAF50',
    marginLeft: 8,
    flex: 1,
  },
  mapInstructionContainer: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
    pointerEvents: 'none',
  },
  mapInstruction: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  mapInstructionIcon: {
    marginRight: 8,
  },
  mapInstructionText: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
  },
  instructionContainer: {
    backgroundColor: '#F0F8FF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  instructionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  instructionStep: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  instructionNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4630EB',
    color: '#fff',
    textAlign: 'center',
    lineHeight: 24,
    fontWeight: 'bold',
    marginRight: 8,
  },
  instructionText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  buttonContentWithLoader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  zoneActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButton: {
    padding: 8,
    marginLeft: 8,
  },
});

export default SafeZonesScreen;
