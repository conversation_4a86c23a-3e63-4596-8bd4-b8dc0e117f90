import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { FontAwesome5 } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslations } from '../../contexts/TranslationContext';
import { useBilling } from '../../hooks/useBilling';

export default function TrialInfoScreen() {
  const { user, isSubscriptionLoading, refreshSubscriptionStatus } = useAuth();
  const { purchaseSubscription } = useBilling();
  const [isStarting, setIsStarting] = React.useState(false);
  const router = useRouter();
  const { t } = useTranslations();

  const handleStartTrial = async () => {
    try {
      setIsStarting(true);
      console.log('Starting trial with real Google Play Billing...');

      // Use direct purchase of monthly subscription to ensure trial is activated
      if (user) {
        console.log('Attempting to purchase monthly subscription with trial...');
        const result = await purchaseSubscription('com.evotech.kidsafety.monthly');

        console.log('Purchase result:', result);

        if (result === true || (typeof result === 'object' && result.success)) {
          console.log('Purchase successful, refreshing subscription status...');
          await refreshSubscriptionStatus();
          // Navigate to home screen after starting trial
          router.replace('/(parent-tabs)/home');
        } else {
          const errorMessage = typeof result === 'object' && result.message
            ? result.message
            : 'Failed to start trial. Please try again.';
          console.error('Purchase failed:', errorMessage);
          Alert.alert(
            'Errore',
            errorMessage,
            [{ text: 'OK' }]
          );
        }
      } else {
        console.error('No user available for purchase');
        Alert.alert(
          'Errore',
          'User not found. Please log in again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error starting trial:', error);
      Alert.alert(
        'Errore',
        `Failed to start trial: ${error instanceof Error ? error.message : 'Unknown error'}`,
        [{ text: 'OK' }]
      );
    } finally {
      setIsStarting(false);
    }
  };

  const handleStartAnnualSubscription = async () => {
    try {
      setIsStarting(true);
      console.log('Starting annual subscription with real Google Play Billing...');

      if (user) {
        console.log('Attempting to purchase annual subscription...');
        const result = await purchaseSubscription('com.evotech.kidsafety.annual');

        console.log('Annual purchase result:', result);

        if (result === true || (typeof result === 'object' && result.success)) {
          console.log('Annual purchase successful, refreshing subscription status...');
          await refreshSubscriptionStatus();
          // Navigate to home screen after purchase
          router.replace('/(parent-tabs)/home');
        } else {
          const errorMessage = typeof result === 'object' && result.message
            ? result.message
            : 'Failed to purchase annual subscription. Please try again.';
          console.error('Annual purchase failed:', errorMessage);
          Alert.alert(
            'Errore',
            errorMessage,
            [{ text: 'OK' }]
          );
        }
      } else {
        console.error('No user available for annual purchase');
        Alert.alert(
          'Errore',
          'User not found. Please log in again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error purchasing annual subscription:', error);
      Alert.alert(
        'Errore',
        `Failed to purchase annual subscription: ${error instanceof Error ? error.message : 'Unknown error'}`,
        [{ text: 'OK' }]
      );
    } finally {
      setIsStarting(false);
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Header con gradiente */}
      <View style={styles.headerGradient}>
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <FontAwesome5 name="shield-alt" size={60} color="#FFFFFF" />
          </View>
          <Text style={styles.title}>KidSafety Premium</Text>
          <Text style={styles.subtitle}>Protezione completa per la tua famiglia</Text>
        </View>
      </View>

      {/* Sezione piani */}
      <View style={styles.plansContainer}>
        <Text style={styles.plansTitle}>Scegli il tuo piano</Text>

        {/* Piano Mensile */}
        <TouchableOpacity
          style={[styles.planCard, styles.monthlyPlan]}
          onPress={handleStartTrial}
          disabled={isStarting || isSubscriptionLoading}
        >
          <View style={styles.planHeader}>
            <View style={styles.planTitleContainer}>
              <Text style={styles.planTitle}>Piano Mensile</Text>
              <View style={styles.trialBadge}>
                <Text style={styles.trialBadgeText}>TRE GIORNI DI PROVA GRATUITA</Text>
              </View>
            </View>
            <Text style={styles.planPrice}>€3.99</Text>
          </View>
          <Text style={styles.planDescription}>Perfetto per iniziare</Text>

          {isStarting || isSubscriptionLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#4630EB" />
              <Text style={styles.loadingText}>Avvio in corso...</Text>
            </View>
          ) : (
            <View style={styles.startTrialButton}>
              <FontAwesome5 name="rocket" size={16} color="#FFFFFF" />
              <Text style={styles.startTrialText}>Inizia Prova Gratuita</Text>
            </View>
          )}
        </TouchableOpacity>

        {/* Piano Annuale */}
        <TouchableOpacity
          style={[styles.planCard, styles.annualPlan]}
          onPress={handleStartAnnualSubscription}
          disabled={isStarting || isSubscriptionLoading}
        >
          <View style={styles.planHeader}>
            <View style={styles.planTitleContainer}>
              <Text style={styles.planTitle}>Piano Annuale</Text>
              <View style={styles.saveBadge}>
                <Text style={styles.saveBadgeText}>RISPARMIA 67%</Text>
              </View>
            </View>
            <Text style={styles.planPrice}>€15.99</Text>
          </View>
          <Text style={styles.planBilling}>all'anno</Text>
          <Text style={styles.planDescription}>Il miglior valore</Text>

          {isStarting || isSubscriptionLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#4CAF50" />
              <Text style={styles.loadingText}>Acquisto in corso...</Text>
            </View>
          ) : (
            <View style={styles.startButton}>
              <FontAwesome5 name="crown" size={16} color="#FFFFFF" />
              <Text style={styles.startButtonText}>Abbonati Ora</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* Features */}
      <View style={styles.featuresContainer}>
        <Text style={styles.featuresTitle}>Cosa include:</Text>

        <View style={styles.featureItem}>
          <View style={styles.featureIconContainer}>
            <FontAwesome5 name="map-marker-alt" size={18} color="#4630EB" />
          </View>
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>Localizzazione in tempo reale</Text>
            <Text style={styles.featureDescription}>Monitora la posizione dei tuoi figli 24/7</Text>
          </View>
        </View>



        <View style={styles.featureItem}>
          <View style={styles.featureIconContainer}>
            <FontAwesome5 name="tasks" size={18} color="#4630EB" />
          </View>
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>Missioni e ricompense</Text>
            <Text style={styles.featureDescription}>Motiva i tuoi figli con obiettivi e premi</Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <View style={styles.featureIconContainer}>
            <FontAwesome5 name="brain" size={18} color="#4630EB" />
          </View>
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>Assistente AI per i compiti</Text>
            <Text style={styles.featureDescription}>Aiuto intelligente per lo studio</Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <View style={styles.featureIconContainer}>
            <FontAwesome5 name="users" size={18} color="#4630EB" />
          </View>
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>Account Famiglia</Text>
            <Text style={styles.featureDescription}>Condividi l'abbonamento con il tuo partner</Text>
          </View>
        </View>
      </View>

      {/* Info legali */}
      <View style={styles.legalContainer}>
        <View style={styles.infoItem}>
          <FontAwesome5 name="info-circle" size={16} color="#888" />
          <Text style={styles.infoText}>
            Iniziando la prova gratuita, accetti che dopo il periodo di prova verrai addebitato per l'abbonamento a meno che non annulli prima della fine del periodo di prova.
          </Text>
        </View>

        <View style={styles.infoItem}>
          <FontAwesome5 name="credit-card" size={16} color="#888" />
          <Text style={styles.infoText}>
            <Text style={styles.boldText}>L'ABBONAMENTO SI RINNOVERÀ AUTOMATICAMENTE.</Text> Il tuo abbonamento si rinnoverà automaticamente alla fine di ogni periodo a meno che non venga annullato almeno 24 ore prima della fine del periodo corrente.
          </Text>
        </View>

        <View style={styles.infoItem}>
          <FontAwesome5 name="shield-alt" size={16} color="#888" />
          <Text style={styles.infoText}>
            È richiesto un abbonamento per utilizzare tutte le funzionalità premium dell'app. Senza abbonamento, saranno disponibili solo le funzionalità di base.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    paddingBottom: 40,
  },
  headerGradient: {
    backgroundColor: '#4630EB',
    paddingTop: 60,
    paddingBottom: 40,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  header: {
    alignItems: 'center',
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  plansContainer: {
    padding: 20,
  },
  plansTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1A1A1A',
    textAlign: 'center',
    marginBottom: 20,
  },
  planCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  monthlyPlan: {
    borderColor: '#4630EB',
    transform: [{ scale: 1.02 }],
  },
  annualPlan: {
    borderColor: '#4CAF50',
    opacity: 1,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  planTitleContainer: {
    flex: 1,
  },
  planTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1A1A1A',
    marginBottom: 8,
  },
  planPrice: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#4630EB',
  },
  planBilling: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  planDescription: {
    fontSize: 14,
    color: '#888',
    marginBottom: 16,
  },
  trialBadge: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  trialBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  saveBadge: {
    backgroundColor: '#00C851',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  saveBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  startTrialButton: {
    backgroundColor: '#4630EB',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  startTrialText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  startButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
    opacity: 0.6,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  comingSoonButton: {
    backgroundColor: '#F0F0F0',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  comingSoonText: {
    color: '#888',
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  loadingText: {
    color: '#4630EB',
    fontSize: 14,
    fontWeight: '500',
  },
  featuresContainer: {
    backgroundColor: '#FFFFFF',
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1A1A1A',
    marginBottom: 20,
    textAlign: 'center',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  featureIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1A1A1A',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  legalContainer: {
    backgroundColor: '#FFFFFF',
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    gap: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 12,
    color: '#666',
    lineHeight: 18,
  },
  boldText: {
    fontWeight: 'bold',
    color: '#E53935',
  },
});
