import React, { forwardRef, useEffect, useState, useRef } from 'react';
import MapView, { MapViewProps, PROVIDER_GOOGLE } from 'react-native-maps';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';

/**
 * Un componente wrapper per MapView che utilizza Google Maps
 * Questo componente accetta tutte le stesse props di MapView
 */
const GoogleMapView = forwardRef<MapView, MapViewProps>((props, ref) => {
  const [isMapReady, setIsMapReady] = useState(false);
  const [mapError, setMapError] = useState<string | null>(null);
  const mapRef = useRef<MapView | null>(null);

  // Gestisce il caricamento della mappa
  const handleMapReady = () => {
    console.log('[GoogleMapView] Map is ready');
    setIsMapReady(true);

    // Impostiamo il ref interno se non è stato passato un ref esterno
    if (!ref && mapRef.current) {
      console.log('[GoogleMapView] Using internal ref');
    }
  };

  // Monitora gli errori
  useEffect(() => {
    if (mapError) {
      console.error('[GoogleMapView] Map error state:', mapError);
    }
  }, [mapError]);

  // Utilizziamo il ref interno o quello passato dall'esterno
  const mapRefToUse = ref || mapRef;

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRefToUse}
        {...props}
        provider={PROVIDER_GOOGLE}
        mapType="standard"
        zoomEnabled={true}
        rotateEnabled={true}
        scrollEnabled={true}
        loadingEnabled={true}
        onMapReady={handleMapReady}
        style={[styles.map, props.style]}
      >
        {props.children}
      </MapView>

      {!isMapReady && !mapError && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4630EB" />
          <Text style={styles.loadingText}>Caricamento mappa...</Text>
        </View>
      )}

      {mapError && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Errore nel caricamento della mappa</Text>
          <Text style={styles.errorDetail}>{mapError}</Text>
        </View>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#4630EB',
  },
  errorContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF3B30',
    marginBottom: 10,
  },
  errorDetail: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});

export default GoogleMapView;
