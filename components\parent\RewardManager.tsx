import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  FlatList,
  Alert,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { supabase } from '../../utils/supabase';

interface Child {
  id: string;
  name: string;
}

interface Reward {
  id: string;
  reward_name: string;
  stars_required: number;
  is_achieved: boolean;
  child_id: string;
  child_name?: string;
}

interface RewardManagerProps {
  parentId: string;
}

const RewardManager: React.FC<RewardManagerProps> = ({ parentId }) => {
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [children, setChildren] = useState<Child[]>([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedChild, setSelectedChild] = useState<Child | null>(null);
  const [childSelectVisible, setChildSelectVisible] = useState(false);
  const [newReward, setNewReward] = useState({
    reward_name: '',
    stars_required: '100',
  });

  useEffect(() => {
    fetchChildren();
    fetchRewards();
  }, [parentId]);

  const fetchChildren = async () => {
    try {
      const { data, error } = await supabase
        .from('parent_child_relationships')
        .select('child_id, users:child_id(id, name)')
        .eq('parent_id', parentId);

      if (error) {
        console.error('Error fetching children:', error);
        return;
      }

      if (data) {
        const formattedChildren = data.map(item => ({
          id: item.child_id,
          name: item.users?.name || 'Unknown Child',
        }));
        setChildren(formattedChildren);
      }
    } catch (err) {
      console.error('Exception fetching children:', err);
    }
  };

  const fetchRewards = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('rewards')
        .select('id, reward_name, stars_required, is_achieved, child_id, users!rewards_child_id_fkey(name)')
        .eq('parent_id', parentId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching rewards:', error);
        return;
      }

      if (data) {
        const formattedRewards = data.map(reward => ({
          ...reward,
          child_name: reward.users?.name || 'Unknown Child',
        }));
        setRewards(formattedRewards);
      }
    } catch (err) {
      console.error('Exception fetching rewards:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateReward = async () => {
    if (!selectedChild) {
      Alert.alert('Error', 'Please select a child');
      return;
    }

    if (!newReward.reward_name.trim()) {
      Alert.alert('Error', 'Please enter a reward name');
      return;
    }

    const starsRequired = parseInt(newReward.stars_required);
    if (isNaN(starsRequired) || starsRequired <= 0) {
      Alert.alert('Error', 'Please enter a valid number of stars');
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('rewards')
        .insert({
          parent_id: parentId,
          child_id: selectedChild.id,
          reward_name: newReward.reward_name,
          stars_required: starsRequired,
        })
        .select();

      if (error) {
        console.error('Error creating reward:', error);
        Alert.alert('Error', 'Failed to create reward. Please try again.');
        return;
      }

      if (data) {
        // Add the new reward to the list
        const newRewardWithChild = {
          ...data[0],
          child_name: selectedChild.name,
        };
        setRewards([newRewardWithChild, ...rewards]);

        // Reset form and close modal
        setNewReward({
          reward_name: '',
          stars_required: '100',
        });
        setSelectedChild(null);
        setModalVisible(false);

        Alert.alert('Success', 'Reward created successfully!');
      }
    } catch (err) {
      console.error('Exception creating reward:', err);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteReward = async (rewardId: string) => {
    Alert.alert(
      'Delete Reward',
      'Are you sure you want to delete this reward?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              const { error } = await supabase
                .from('rewards')
                .delete()
                .eq('id', rewardId);

              if (error) {
                console.error('Error deleting reward:', error);
                Alert.alert('Error', 'Failed to delete reward. Please try again.');
                return;
              }

              // Remove the deleted reward from the list
              setRewards(rewards.filter(reward => reward.id !== rewardId));
              Alert.alert('Success', 'Reward deleted successfully!');
            } catch (err) {
              console.error('Exception deleting reward:', err);
              Alert.alert('Error', 'An unexpected error occurred. Please try again.');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleMarkAchieved = async (rewardId: string, isAchieved: boolean) => {
    try {
      setLoading(true);
      const { error } = await supabase
        .from('rewards')
        .update({ is_achieved: isAchieved })
        .eq('id', rewardId);

      if (error) {
        console.error('Error updating reward:', error);
        Alert.alert('Error', 'Failed to update reward. Please try again.');
        return;
      }

      // Update the reward in the list
      setRewards(
        rewards.map(reward =>
          reward.id === rewardId ? { ...reward, is_achieved: isAchieved } : reward
        )
      );

      Alert.alert(
        'Success',
        isAchieved
          ? 'Reward marked as achieved!'
          : 'Reward marked as not achieved!'
      );
    } catch (err) {
      console.error('Exception updating reward:', err);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderRewardItem = ({ item }: { item: Reward }) => (
    <View style={styles.rewardCard}>
      <View style={styles.rewardHeader}>
        <View style={styles.rewardTitleContainer}>
          <FontAwesome5 name="gift" size={16} color="#8E44AD" />
          <Text style={styles.rewardTitle}>{item.reward_name}</Text>
        </View>
        {item.is_achieved && (
          <View style={styles.achievedBadge}>
            <FontAwesome5 name="check-circle" size={12} color="#4CAF50" />
            <Text style={styles.achievedText}>Achieved</Text>
          </View>
        )}
      </View>

      <View style={styles.rewardDetails}>
        <View style={styles.detailItem}>
          <FontAwesome5 name="star" size={14} color="#FFD700" />
          <Text style={styles.detailText}>{item.stars_required} stars required</Text>
        </View>
        <View style={styles.detailItem}>
          <FontAwesome5 name="child" size={14} color="#666" />
          <Text style={styles.detailText}>For: {item.child_name}</Text>
        </View>
      </View>

      <View style={styles.rewardActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.achieveButton]}
          onPress={() => handleMarkAchieved(item.id, !item.is_achieved)}
        >
          <FontAwesome5
            name={item.is_achieved ? 'undo' : 'check'}
            size={14}
            color="#FFFFFF"
          />
          <Text style={styles.actionButtonText}>
            {item.is_achieved ? 'Undo' : 'Mark Achieved'}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDeleteReward(item.id)}
        >
          <FontAwesome5 name="trash" size={14} color="#FFFFFF" />
          <Text style={styles.actionButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Rewards</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setModalVisible(true)}
        >
          <FontAwesome5 name="plus" size={16} color="#FFFFFF" />
          <Text style={styles.addButtonText}>Add Reward</Text>
        </TouchableOpacity>
      </View>

      {rewards.length === 0 ? (
        <View style={styles.emptyContainer}>
          <FontAwesome5 name="gift" size={50} color="#CCCCCC" />
          <Text style={styles.emptyText}>No rewards yet</Text>
          <Text style={styles.emptySubtext}>
            Create rewards for your children to motivate them to complete missions
          </Text>
        </View>
      ) : (
        <View style={styles.rewardsListContainer}>
          {rewards.map((item) => (
            <React.Fragment key={item.id}>
              {renderRewardItem({ item })}
            </React.Fragment>
          ))}
        </View>
      )}

      {/* Create Reward Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Create New Reward</Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <FontAwesome5 name="times" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              <Text style={styles.inputLabel}>Reward Name</Text>
              <TextInput
                style={styles.input}
                value={newReward.reward_name}
                onChangeText={(text) =>
                  setNewReward({ ...newReward, reward_name: text })
                }
                placeholder="Enter reward name (e.g. New Toy, Ice Cream)"
              />

              <Text style={styles.inputLabel}>Stars Required</Text>
              <TextInput
                style={styles.input}
                value={newReward.stars_required}
                onChangeText={(text) =>
                  setNewReward({
                    ...newReward,
                    stars_required: text.replace(/[^0-9]/g, ''),
                  })
                }
                placeholder="Enter number of stars required"
                keyboardType="numeric"
              />

              <Text style={styles.inputLabel}>Assign To</Text>
              <TouchableOpacity
                style={styles.childPickerButton}
                onPress={() => setChildSelectVisible(true)}
              >
                <Text style={styles.childPickerText}>
                  {selectedChild ? selectedChild.name : 'Select a child'}
                </Text>
                <FontAwesome5 name="chevron-down" size={16} color="#4630EB" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.createButton]}
                onPress={handleCreateReward}
              >
                <Text style={styles.createButtonText}>Create Reward</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Child Selection Modal */}
      <Modal
        visible={childSelectVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setChildSelectVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, styles.childSelectModal]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Child</Text>
              <TouchableOpacity onPress={() => setChildSelectVisible(false)}>
                <FontAwesome5 name="times" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            <FlatList
              data={children}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.modalContent}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.childOption}
                  onPress={() => {
                    setSelectedChild(item);
                    setChildSelectVisible(false);
                  }}
                >
                  <View style={styles.childIconContainer}>
                    <FontAwesome5 name="child" size={20} color="#4630EB" />
                  </View>
                  <Text style={styles.childOptionText}>{item.name}</Text>
                  {selectedChild?.id === item.id && (
                    <FontAwesome5 name="check" size={16} color="#4630EB" />
                  )}
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3.84,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4630EB',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#888888',
    textAlign: 'center',
    lineHeight: 20,
  },
  rewardsList: {
    paddingBottom: 16,
  },
  rewardsListContainer: {
    paddingBottom: 16,
    gap: 16,
  },
  rewardCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  rewardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  rewardTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rewardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginLeft: 8,
  },
  achievedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E9',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  achievedText: {
    fontSize: 12,
    color: '#4CAF50',
    marginLeft: 4,
    fontWeight: '600',
  },
  rewardDetails: {
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 8,
  },
  rewardActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 4,
    marginLeft: 8,
  },
  achieveButton: {
    backgroundColor: '#4CAF50',
  },
  deleteButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
  },
  childSelectModal: {
    maxHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  modalContent: {
    padding: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
  },
  childPickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  childPickerText: {
    fontSize: 16,
    color: '#333333',
  },
  modalFooter: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  modalButton: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
  },
  cancelButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '600',
  },
  createButton: {
    backgroundColor: '#4630EB',
  },
  createButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  childOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
  },
  childIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  childOptionText: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
  },
});

export default RewardManager;
