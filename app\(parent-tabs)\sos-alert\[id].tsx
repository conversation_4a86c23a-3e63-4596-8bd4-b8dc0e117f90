import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Linking,
  Platform
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { FontAwesome5 } from '@expo/vector-icons';
import MapView, { Marker } from 'react-native-maps';
import GoogleMapView from '../../../components/shared/GoogleMapView';
import Header from '../../../components/shared/Header';
import { getSOSAlertById, getChildById } from '../../../utils/supabase';
import { formatDistanceToNow } from 'date-fns';
import { it } from 'date-fns/locale';

interface SOSAlert {
  id: string;
  child_id: string;
  latitude: number;
  longitude: number;
  timestamp: string;
  status: string;
  battery_level?: number;
  children?: {
    name: string;
    age?: number;
  };
}

export default function SOSAlertDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [alert, setAlert] = useState<SOSAlert | null>(null);
  const [childName, setChildName] = useState('');
  const [childAge, setChildAge] = useState<number | undefined>(undefined);

  useEffect(() => {
    if (id) {
      fetchAlertDetails();
    }
  }, [id]);

  const fetchAlertDetails = async () => {
    try {
      setIsLoading(true);

      // Fetch alert details
      const alertData = await getSOSAlertById(id as string);

      if (alertData) {
        setAlert(alertData);

        // If the alert doesn't include child details, fetch them separately
        if (!alertData.children && alertData.child_id) {
          const childData = await getChildById(alertData.child_id);
          if (childData) {
            setChildName(childData.name || 'Child');
            setChildAge(childData.age);
          }
        } else if (alertData.children) {
          setChildName(alertData.children.name || 'Child');
          setChildAge(alertData.children.age);
        }
      } else {
        Alert.alert('Error', 'SOS alert not found');
        router.back();
      }
    } catch (error) {
      console.error('Error fetching SOS alert details:', error);
      Alert.alert('Error', 'Failed to load SOS alert details');
    } finally {
      setIsLoading(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString('it-IT', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (e) {
      return timestamp;
    }
  };

  const getTimeAgo = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true, locale: it });
    } catch (e) {
      return '';
    }
  };

  const openMapsApp = () => {
    if (!alert) return;

    const { latitude, longitude } = alert;
    const label = `${childName}'s SOS Location`;

    let url = '';
    if (Platform.OS === 'ios') {
      url = `maps:?q=${label}&ll=${latitude},${longitude}`;
    } else {
      url = `geo:${latitude},${longitude}?q=${latitude},${longitude}(${encodeURIComponent(label)})`;
    }

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          // Fallback to Google Maps
          const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
          return Linking.openURL(googleMapsUrl);
        }
      })
      .catch((err) => {
        console.error('Error opening maps app:', err);
        Alert.alert('Error', 'Could not open maps application');
      });
  };



  if (isLoading) {
    return (
      <View style={styles.container}>
        <Header title="SOS Alert Details" showBackButton onBackPress={() => router.back()} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF3B30" />
          <Text style={styles.loadingText}>Loading SOS alert details...</Text>
        </View>
      </View>
    );
  }

  if (!alert) {
    return (
      <View style={styles.container}>
        <Header title="SOS Alert Details" showBackButton onBackPress={() => router.back()} />
        <View style={styles.errorContainer}>
          <FontAwesome5 name="exclamation-circle" size={50} color="#FF3B30" />
          <Text style={styles.errorText}>SOS alert not found</Text>
          <TouchableOpacity style={styles.button} onPress={() => router.back()}>
            <Text style={styles.buttonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="SOS Alert Details"
        showBackButton
        onBackPress={() => router.back()}
      />

      <ScrollView style={styles.content}>
        <View style={styles.alertHeader}>
          <View style={styles.alertIconContainer}>
            <FontAwesome5 name="exclamation-triangle" size={32} color="#FFFFFF" />
          </View>
          <View style={styles.alertInfo}>
            <Text style={styles.alertTitle}>{childName} needs help!</Text>
            <Text style={styles.alertTime}>{getTimeAgo(alert.timestamp)}</Text>
          </View>
        </View>

        <View style={styles.mapContainer}>
          <GoogleMapView
            style={styles.map}
            initialRegion={{
              latitude: alert.latitude,
              longitude: alert.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}
          >
            <Marker
              coordinate={{
                latitude: alert.latitude,
                longitude: alert.longitude,
              }}
              title={`${childName}'s SOS Location`}
              description={formatTimestamp(alert.timestamp)}
            >
              <View style={styles.markerContainer}>
                <FontAwesome5 name="exclamation-triangle" size={24} color="#FF3B30" />
              </View>
            </Marker>
          </GoogleMapView>

          <TouchableOpacity style={styles.openMapsButton} onPress={openMapsApp}>
            <FontAwesome5 name="map-marked-alt" size={16} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.openMapsButtonText}>Open in Maps</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.detailsContainer}>
          <Text style={styles.detailsTitle}>Alert Details</Text>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Child</Text>
            <Text style={styles.detailValue}>{childName}</Text>
          </View>

          {childAge !== undefined && (
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Age</Text>
              <Text style={styles.detailValue}>{childAge} years</Text>
            </View>
          )}

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Time</Text>
            <Text style={styles.detailValue}>{formatTimestamp(alert.timestamp)}</Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Coordinates</Text>
            <Text style={styles.detailValue}>
              {alert.latitude.toFixed(6)}, {alert.longitude.toFixed(6)}
            </Text>
          </View>

          {alert.battery_level !== undefined && (
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Battery Level</Text>
              <Text style={styles.detailValue}>{alert.battery_level}%</Text>
            </View>
          )}

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Status</Text>
            <View style={[
              styles.statusBadge,
              { backgroundColor: alert.status === 'active' ? '#FF3B30' : '#999999' }
            ]}>
              <Text style={styles.statusText}>
                {alert.status === 'active' ? 'Active' : 'Resolved'}
              </Text>
            </View>
          </View>
        </View>


      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF8F7',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#FFECEC',
  },
  alertIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#FF3B30',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  alertInfo: {
    flex: 1,
  },
  alertTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FF3B30',
    marginBottom: 4,
  },
  alertTime: {
    fontSize: 16,
    color: '#666',
  },
  mapContainer: {
    height: 300,
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  markerContainer: {
    padding: 8,
  },
  openMapsButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    backgroundColor: '#4630EB',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  openMapsButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  detailsContainer: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  detailLabel: {
    fontSize: 16,
    color: '#666',
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  statusText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 14,
  },

  button: {
    backgroundColor: '#4630EB',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonIcon: {
    marginRight: 8,
  },
});
