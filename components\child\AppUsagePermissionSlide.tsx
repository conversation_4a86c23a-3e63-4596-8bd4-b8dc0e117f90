import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, AppState, AppStateStatus } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';

// Fallback functions for when the module is not available
const checkForPermission = async (): Promise<boolean> => {
  console.warn('[AppUsagePermissionSlide] Usage stats module not available in Expo Go');
  return false;
};

const showUsageAccessSettings = async (packageName: string): Promise<void> => {
  console.warn('[AppUsagePermissionSlide] Usage stats settings not available in Expo Go');
  Alert.alert(
    'Funzionalità non disponibile',
    'Il monitoraggio delle app richiede una build nativa dell\'app. Questa funzionalità sarà disponibile nella versione finale.',
    [{ text: 'OK' }]
  );
};

interface AppUsagePermissionSlideProps {
  onPermissionGranted: () => void;
}

const AppUsagePermissionSlide: React.FC<AppUsagePermissionSlideProps> = ({ onPermissionGranted }) => {
  const [hasPermission, setHasPermission] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // Controlla il permesso inizialmente
    checkUsageStatsPermission();

    // Controlla il permesso quando l'app torna in foreground
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        checkUsageStatsPermission();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Controlla periodicamente il permesso
    const interval = setInterval(checkUsageStatsPermission, 2000);

    return () => {
      subscription?.remove();
      clearInterval(interval);
    };
  }, []);

  useEffect(() => {
    // Se il permesso è stato concesso, notifica il genitore
    if (hasPermission) {
      onPermissionGranted();
    }
  }, [hasPermission, onPermissionGranted]);

  const checkUsageStatsPermission = async () => {
    try {
      setIsChecking(true);
      const permission = await checkForPermission();
      console.log('[AppUsagePermissionSlide] Usage stats permission:', permission);
      setHasPermission(permission);
    } catch (error) {
      console.error('[AppUsagePermissionSlide] Error checking permission:', error);
      setHasPermission(false);
    } finally {
      setIsChecking(false);
    }
  };

  const handleRequestPermission = async () => {
    try {
      Alert.alert(
        'Autorizzazione Monitoraggio App',
        'Per permettere ai tuoi genitori di vedere quali app utilizzi e per quanto tempo, devi concedere l\'autorizzazione di accesso alle statistiche di utilizzo.\n\nVerrai reindirizzato alle impostazioni di sistema dove dovrai:\n\n1. Trovare "KidSafety" nella lista\n2. Attivare l\'interruttore\n3. Confermare con "OK"',
        [
          { text: 'Annulla', style: 'cancel' },
          {
            text: 'Apri Impostazioni',
            onPress: async () => {
              try {
                await showUsageAccessSettings('');
              } catch (error) {
                console.error('[AppUsagePermissionSlide] Error opening settings:', error);
                Alert.alert(
                  'Errore',
                  'Impossibile aprire le impostazioni. Prova ad aprire manualmente le Impostazioni > App > Accesso speciale > Accesso ai dati di utilizzo.',
                  [{ text: 'OK' }]
                );
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('[AppUsagePermissionSlide] Error requesting permission:', error);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <FontAwesome5 name="chart-bar" size={60} color="#4630EB" />
        </View>
        <Text style={styles.title}>Monitoraggio App</Text>
        <Text style={styles.subtitle}>
          Per permettere ai tuoi genitori di vedere quali app utilizzi e per quanto tempo, 
          è necessario concedere l'autorizzazione di accesso alle statistiche di utilizzo.
        </Text>
      </View>

      <View style={styles.permissionContainer}>
        <View style={styles.permissionItem}>
          <View style={styles.permissionStatus}>
            {isChecking ? (
              <FontAwesome5 name="spinner" size={24} color="#FF9800" />
            ) : hasPermission ? (
              <FontAwesome5 name="check-circle" size={24} color="#4CAF50" />
            ) : (
              <FontAwesome5 name="times-circle" size={24} color="#F44336" />
            )}
          </View>
          <View style={styles.permissionInfo}>
            <Text style={styles.permissionTitle}>Accesso Statistiche Utilizzo</Text>
            <Text style={styles.permissionDescription}>
              {isChecking 
                ? 'Controllo autorizzazione...'
                : hasPermission 
                  ? 'Autorizzazione concessa ✓'
                  : 'Autorizzazione necessaria per il monitoraggio delle app'
              }
            </Text>
          </View>
          {!hasPermission && !isChecking && (
            <TouchableOpacity
              style={styles.permissionButton}
              onPress={handleRequestPermission}
            >
              <Text style={styles.permissionButtonText}>Attiva</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View style={styles.infoContainer}>
        <FontAwesome5 name="info-circle" size={16} color="#666" />
        <Text style={styles.infoText}>
          Questa autorizzazione è sicura e permette solo di vedere quali app vengono utilizzate e per quanto tempo. 
          Non permette di leggere il contenuto delle app o i dati personali.
        </Text>
      </View>

      {hasPermission && (
        <View style={styles.successContainer}>
          <FontAwesome5 name="check-circle" size={24} color="#4CAF50" />
          <Text style={styles.successText}>
            Perfetto! L'autorizzazione è stata concessa. I tuoi genitori potranno ora monitorare l'utilizzo delle app.
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F0F0FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  permissionContainer: {
    marginBottom: 20,
  },
  permissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9F9F9',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  permissionStatus: {
    marginRight: 12,
  },
  permissionInfo: {
    flex: 1,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  permissionButton: {
    backgroundColor: '#4630EB',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F0F8FF',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  infoText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
    flex: 1,
    lineHeight: 16,
  },
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E9',
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  successText: {
    fontSize: 14,
    color: '#2E7D32',
    marginLeft: 12,
    flex: 1,
    lineHeight: 18,
    fontWeight: '500',
  },
});

export default AppUsagePermissionSlide;
