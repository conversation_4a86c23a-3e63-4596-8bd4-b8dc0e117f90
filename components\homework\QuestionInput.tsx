import React, { useState } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Text,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';

interface QuestionInputProps {
  onSubmit: (question: string) => void;
  isLoading: boolean;
  placeholder?: string;
}

const QuestionInput: React.FC<QuestionInputProps> = ({
  onSubmit,
  isLoading,
  placeholder = 'Fai una domanda sui tuoi compiti...',
}) => {
  const [question, setQuestion] = useState('');

  const handleSubmit = () => {
    if (question.trim() && !isLoading) {
      onSubmit(question.trim());
      setQuestion('');
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={100}
      style={styles.container}
    >
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          value={question}
          onChangeText={setQuestion}
          placeholder={placeholder}
          multiline
          maxLength={500}
          returnKeyType="default"
          blurOnSubmit={false}
          editable={!isLoading}
          accessibilityLabel="Campo per inserire la domanda"
          accessibilityHint="Scrivi qui la tua domanda sui compiti"
        />
        <TouchableOpacity
          style={[
            styles.submitButton,
            (!question.trim() || isLoading) && styles.disabledButton,
          ]}
          onPress={handleSubmit}
          disabled={!question.trim() || isLoading}
        >
          {isLoading ? (
            <FontAwesome5 name="spinner" size={20} color="#FFFFFF" />
          ) : (
            <FontAwesome5 name="paper-plane" size={20} color="#FFFFFF" />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  input: {
    flex: 1,
    minHeight: 40,
    maxHeight: 120,
    fontSize: 16,
    color: '#333333',
    paddingTop: 8,
    paddingBottom: 8,
  },
  submitButton: {
    backgroundColor: '#4630EB',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    marginBottom: 4,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
});

export default QuestionInput;

