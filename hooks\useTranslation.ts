import { useLanguage } from '../contexts/LanguageContext';
import { getTranslations } from '../utils/translations';

/**
 * Hook personalizzato per utilizzare le traduzioni in base alla lingua corrente
 * @returns Oggetto con le traduzioni nella lingua corrente
 */
export const useTranslation = () => {
  const { currentLanguage } = useLanguage();
  const translations = getTranslations(currentLanguage.code);
  
  return translations;
};

export default useTranslation;
