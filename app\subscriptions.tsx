import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Stack } from 'expo-router';
import SubscriptionManager from '../components/SubscriptionManager';

export default function SubscriptionsScreen() {
  return (
    <View style={styles.container}>
      <Stack.Screen options={{ title: 'Manage Subscriptions' }} />
      <SubscriptionManager />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
}); 