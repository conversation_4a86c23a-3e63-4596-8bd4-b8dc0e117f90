import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Modal,
  Platform
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslations } from '../../contexts/TranslationContext';
import { getChildrenForParent, getChildRoutesForDate, getChildRouteStats } from '../../utils/supabase';
import Header from '../../components/shared/Header';
import ChildRouteMap from '../../components/parent/ChildRouteMap';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';

interface Child {
  child_id: string;
  users: {
    name: string;
    age?: number;
  };
}

interface RouteStats {
  totalDistance: number;
  totalDuration: number;
  routeCount: number;
}

export default function RoutesScreen() {
  const { user } = useAuth();
  const { t } = useTranslations();
  const [children, setChildren] = useState<Child[]>([]);
  const [selectedChild, setSelectedChild] = useState<Child | null>(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showMap, setShowMap] = useState(false);
  const [routeStats, setRouteStats] = useState<RouteStats | null>(null);

  useEffect(() => {
    fetchChildren();
  }, []);

  useEffect(() => {
    if (selectedChild) {
      fetchRouteStats();
    }
  }, [selectedChild, selectedDate]);

  const fetchChildren = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const childrenData = await getChildrenForParent(user.id);
      setChildren(childrenData || []);

      // Seleziona automaticamente il primo bambino se disponibile
      if (childrenData && childrenData.length > 0) {
        setSelectedChild(childrenData[0]);
      }
    } catch (error) {
      console.error('Error fetching children:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRouteStats = async () => {
    if (!selectedChild) return;

    try {
      setIsLoading(true);
      const statsData = await getChildRouteStats(selectedChild.child_id, selectedDate);

      if (statsData) {
        setRouteStats({
          totalDistance: statsData.total_distance || 0,
          totalDuration: statsData.total_duration * 1000 || 0, // Converti da secondi a millisecondi
          routeCount: statsData.route_count || 0
        });
      } else {
        setRouteStats({
          totalDistance: 0,
          totalDuration: 0,
          routeCount: 0
        });
      }
    } catch (error) {
      console.error('Error fetching route stats:', error);
      setRouteStats(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChildSelect = (child: Child) => {
    setSelectedChild(child);
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setSelectedDate(selectedDate);
    }
  };

  const formatDistance = (meters: number) => {
    if (meters < 1000) {
      return `${meters.toFixed(0)} m`;
    } else {
      return `${(meters / 1000).toFixed(2)} km`;
    }
  };

  const formatDuration = (milliseconds: number) => {
    const minutes = Math.floor(milliseconds / 60000);

    if (minutes < 60) {
      return `${minutes} min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours} h ${remainingMinutes} min`;
    }
  };

  const showRouteMap = () => {
    if (selectedChild) {
      setShowMap(true);
    }
  };

  if (isLoading && !selectedChild) {
    return (
      <View style={styles.container}>
        <Header title={t.routes?.title || "Tragitti"} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4630EB" />
          <Text style={styles.loadingText}>{t.routes?.loading || "Caricamento..."}</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header title={t.routes?.title || "Tragitti"} />

      {children.length === 0 ? (
        <View style={styles.emptyContainer}>
          <FontAwesome5 name="route" size={50} color="#ccc" />
          <Text style={styles.emptyText}>{t.routes?.noChildAdded || "Nessun bambino aggiunto"}</Text>
          <Text style={styles.emptySubtext}>
            {t.routes?.addChildToViewRoutes || "Aggiungi un bambino per visualizzare i suoi tragitti"}
          </Text>
        </View>
      ) : (
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
        >
          {/* Selezione del bambino */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t.routes?.selectChild || "Seleziona bambino"}</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.childSelector}
              contentContainerStyle={styles.childSelectorContent}
            >
              {children.map((child) => (
                <TouchableOpacity
                  key={child.child_id}
                  style={[
                    styles.childItem,
                    selectedChild?.child_id === child.child_id && styles.childItemSelected,
                  ]}
                  onPress={() => handleChildSelect(child)}
                >
                  <View style={styles.childIcon}>
                    <FontAwesome5
                      name="child"
                      size={20}
                      color={selectedChild?.child_id === child.child_id ? '#FFFFFF' : '#4630EB'}
                    />
                  </View>
                  <Text
                    style={[
                      styles.childName,
                      selectedChild?.child_id === child.child_id && styles.childNameSelected,
                    ]}
                  >
                    {child.users?.name || 'Bambino'}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* Selezione della data */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t.routes?.selectDate || "Seleziona data"}</Text>
            <TouchableOpacity
              style={styles.dateSelector}
              onPress={() => setShowDatePicker(true)}
            >
              <FontAwesome5 name="calendar-alt" size={20} color="#4630EB" style={styles.dateIcon} />
              <Text style={styles.dateText}>
                {format(selectedDate, 'EEEE d MMMM yyyy', { locale: it })}
              </Text>
            </TouchableOpacity>

            {showDatePicker && (
              <DateTimePicker
                value={selectedDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={handleDateChange}
                maximumDate={new Date()}
              />
            )}
          </View>

          {/* Statistiche dei tragitti */}
          {selectedChild && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>{t.routes?.statistics || "Statistiche"}</Text>

              {isLoading ? (
                <View style={styles.statsLoadingContainer}>
                  <ActivityIndicator size="small" color="#4630EB" />
                  <Text style={styles.statsLoadingText}>{t.routes?.loadingStatistics || "Caricamento statistiche..."}</Text>
                </View>
              ) : routeStats ? (
                <View style={styles.statsContainer}>
                  <View style={styles.statItem}>
                    <FontAwesome5 name="route" size={24} color="#4630EB" />
                    <Text style={styles.statValue}>{routeStats.routeCount}</Text>
                    <Text style={styles.statLabel}>{t.routes?.routes || "Tragitti"}</Text>
                  </View>

                  <View style={styles.statItem}>
                    <FontAwesome5 name="road" size={24} color="#4630EB" />
                    <Text style={styles.statValue}>{formatDistance(routeStats.totalDistance)}</Text>
                    <Text style={styles.statLabel}>{t.routes?.distance || "Distanza"}</Text>
                  </View>

                  <View style={styles.statItem}>
                    <FontAwesome5 name="clock" size={24} color="#4630EB" />
                    <Text style={styles.statValue}>{formatDuration(routeStats.totalDuration)}</Text>
                    <Text style={styles.statLabel}>{t.routes?.duration || "Durata"}</Text>
                  </View>
                </View>
              ) : (
                <View style={styles.noStatsContainer}>
                  <Text style={styles.noStatsText}>{t.routes?.noStatisticsAvailable || "Nessuna statistica disponibile"}</Text>
                </View>
              )}
            </View>
          )}

          {/* Pulsante per visualizzare la mappa */}
          {selectedChild && (
            <TouchableOpacity
              style={styles.viewMapButton}
              onPress={showRouteMap}
            >
              <FontAwesome5 name="map-marked-alt" size={20} color="#fff" />
              <Text style={styles.viewMapButtonText}>
                {t.routes?.viewRoutesOnMap || "Visualizza tragitti sulla mappa"}
              </Text>
            </TouchableOpacity>
          )}
        </ScrollView>
      )}

      {/* Modal per la mappa */}
      <Modal
        visible={showMap}
        animationType="slide"
        onRequestClose={() => setShowMap(false)}
      >
        {selectedChild && (
          <ChildRouteMap
            childId={selectedChild.child_id}
            childName={selectedChild.users?.name || 'Bambino'}
            date={selectedDate}
            onClose={() => setShowMap(false)}
          />
        )}
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  contentContainer: {
    paddingBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
    color: '#333',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  childSelector: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  childSelectorContent: {
    paddingRight: 16,
  },
  childItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0ff',
    borderRadius: 25,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 12,
  },
  childItemSelected: {
    backgroundColor: '#4630EB',
  },
  childIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  childName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4630EB',
  },
  childNameSelected: {
    color: '#fff',
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0ff',
    borderRadius: 8,
    padding: 12,
  },
  dateIcon: {
    marginRight: 12,
  },
  dateText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  statsLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  statsLoadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
  },
  noStatsContainer: {
    padding: 16,
    alignItems: 'center',
  },
  noStatsText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  viewMapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4630EB',
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
  },
  viewMapButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});
