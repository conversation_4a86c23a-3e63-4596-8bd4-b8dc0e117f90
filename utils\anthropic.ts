import Anthropic from '@anthropic-ai/sdk';
import { saveHomeworkSession } from './supabase';
import config from '../config';

// Verifica la presenza dell'API key
const apiKey = config.anthropicApiKey || '';
if (!apiKey) {
  console.warn('Anthropic API key is missing. The AI homework assistant will not work properly.');
}

// Initialize the Anthropic client
const anthropic = new Anthropic({ apiKey });

// Set up options for Anthropic API
const DEFAULT_MODEL = 'claude-3-sonnet-20240229';
const MAX_TOKENS = 1000;

/**
 * Ask a homework question to the AI assistant
 * @param {string} childId The ID of the child asking the question
 * @param {string} question The homework question
 * @param {string} imageUrl Optional URL to an image related to the question
 * @returns {Promise<{answer: string, sessionId: string}>} The answer and session ID
 */
export async function askHomeworkQuestion(
  childId: string,
  question: string,
  imageUrl?: string
): Promise<{ answer: string; sessionId: string }> {
  try {
    // Verifica se l'API key è disponibile
    if (!apiKey) {
      console.error("API key is missing in configuration");
      return { 
        answer: "Non posso rispondere in questo momento. C'è un problema di configurazione del sistema.", 
        sessionId: Date.now().toString() 
      };
    }

    // Create system prompt to guide Claude's behavior
    const systemPrompt = `You are Claude, a helpful, friendly homework assistant for a child.
Your goal is to explain concepts in simple, age-appropriate language while being encouraging and supportive.
You should:
- Explain concepts clearly and simply
- Use examples children can relate to
- Be encouraging and positive
- Guide the student to understand rather than just giving answers
- If the question appears to be asking you to complete homework for them, avoid directly solving the problem and instead guide them through the process of finding the answer themselves

Remember that you're talking to a child, so keep your language simple but not condescending.`;

    // Prepare message content for the API call
    let content: any[] = [];

    // Add image if provided (should be a data URI)
    if (imageUrl && imageUrl.startsWith('data:image/')) {
      content.push({
        type: 'image',
        source: {
          type: 'base64',
          media_type: imageUrl.split(';')[0].split(':')[1], // Extract MIME type
          data: imageUrl.split(',')[1] // Extract base64 data
        }
      });
    }

    // Add the question text
    content.push({ type: 'text', text: question });

    console.log('Sending content to Anthropic:', JSON.stringify(content, null, 2));

    // Call Anthropic API
    const response = await anthropic.messages.create({
      model: DEFAULT_MODEL,
      max_tokens: MAX_TOKENS,
      system: systemPrompt,
      messages: [{
        role: 'user',
        content: content as any
      }]
    });

    // Extract the answer text
    const answer = response.content.find(c => c.type === 'text')?.text || 'No answer provided';

    // Try to save the session to database, but continue even if it fails
    let sessionId = Date.now().toString();
    try {
      const session = await saveHomeworkSession(childId, question, answer, imageUrl);
      sessionId = session.id;
      console.log('Homework session saved successfully with ID:', sessionId);
    } catch (dbError) {
      console.error('Error saving homework session to database:', dbError);
      console.log('Continuing with temporary session ID:', sessionId);
    }

    return {
      answer,
      sessionId
    };
  } catch (error: any) {
    console.error('Error asking homework question:', error);
    // Restituisco una risposta fallback invece di lanciare un errore
    return { 
      answer: `Mi dispiace, non riesco a rispondere in questo momento. Si è verificato un problema: ${error.message}`,
      sessionId: Date.now().toString()
    };
  }
}

/**
 * Ask a follow-up question related to a previous homework session
 * @param {string} childId The ID of the child asking the question
 * @param {string} previousQuestions Previous questions in the conversation
 * @param {string} previousAnswers Previous answers in the conversation
 * @param {string} followUpQuestion The follow-up question
 * @param {string} imageUrl Optional URL to an image related to the question
 * @returns {Promise<{answer: string, sessionId: string}>} The answer and session ID
 */
export async function askFollowUpQuestion(
  childId: string,
  previousQuestions: string[],
  previousAnswers: string[],
  followUpQuestion: string,
  imageUrl?: string
): Promise<{ answer: string; sessionId: string }> {
  try {
    // Verifica se l'API key è disponibile
    if (!apiKey) {
      console.error("API key is missing in configuration");
      return { 
        answer: "Non posso rispondere in questo momento. C'è un problema di configurazione del sistema.", 
        sessionId: Date.now().toString() 
      };
    }

    // Create system prompt
    const systemPrompt = `You are Claude, a helpful, friendly homework assistant for a child.
Your goal is to explain concepts in simple, age-appropriate language while being encouraging and supportive.
Maintain context from the previous conversation.`;

    // Build conversation history
    const messages: Array<{role: 'user' | 'assistant', content: string}> = [];

    // Add conversation history
    for (let i = 0; i < previousQuestions.length; i++) {
      messages.push({
        role: 'user',
        content: previousQuestions[i]
      });

      if (previousAnswers[i]) {
        messages.push({
          role: 'assistant',
          content: previousAnswers[i]
        });
      }
    }

    // Prepare content for the follow-up question
    let followUpContent: any[] = [];

    // Add image if provided (should be a data URI)
    if (imageUrl && imageUrl.startsWith('data:image/')) {
      followUpContent.push({
        type: 'image',
        source: {
          type: 'base64',
          media_type: imageUrl.split(';')[0].split(':')[1], // Extract MIME type
          data: imageUrl.split(',')[1] // Extract base64 data
        }
      });
    }

    // Add the question text
    followUpContent.push({ type: 'text', text: followUpQuestion });

    // Add the follow-up question
    messages.push({
      role: 'user',
      content: followUpContent.length === 1 ? followUpQuestion : followUpContent as any
    });

    console.log('Sending follow-up content to Anthropic:', JSON.stringify(followUpContent, null, 2));

    // Call Anthropic API
    const response = await anthropic.messages.create({
      model: DEFAULT_MODEL,
      max_tokens: MAX_TOKENS,
      system: systemPrompt,
      messages
    });

    // Extract the answer text
    const answer = response.content.find(c => c.type === 'text')?.text || 'No answer provided';

    // Try to save the session to database, but continue even if it fails
    let sessionId = Date.now().toString();
    try {
      const session = await saveHomeworkSession(childId, followUpQuestion, answer, imageUrl);
      sessionId = session.id;
      console.log('Follow-up session saved successfully with ID:', sessionId);
    } catch (dbError) {
      console.error('Error saving follow-up session to database:', dbError);
      console.log('Continuing with temporary session ID:', sessionId);
    }

    return {
      answer,
      sessionId
    };
  } catch (error: any) {
    console.error('Error asking follow-up question:', error);
    // Restituisco una risposta fallback invece di lanciare un errore
    return { 
      answer: `Mi dispiace, non riesco a rispondere in questo momento. Si è verificato un problema: ${error.message}`,
      sessionId: Date.now().toString()
    };
  }
}
