import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import Header from '../../components/shared/Header';
import SubscriptionManagementScreen from '../../components/subscription/SubscriptionManagementScreen';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import { useTranslations } from '../../contexts/TranslationContext';

export default function SubscriptionManagementPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const { t } = useTranslations();

  // If no user is logged in, redirect to login
  if (!user && !isLoading) {
    router.replace('/auth/parent-login');
    return null;
  }

  if (isLoading) {
    return <LoadingIndicator fullScreen text={t.common.loading} />;
  }

  return (
    <View style={styles.container}>
      <Header title={t.subscription.managementTitle} showBackButton />
      <SubscriptionManagementScreen />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});
