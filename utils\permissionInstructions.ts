/**
 * Database di istruzioni per le autorizzazioni specifiche per produttore
 */

export interface PermissionInstructionStep {
  title: string;
  description?: string;
  menuItems: string[];
  finalAction?: string;
}

export interface PermissionInstructions {
  title: string;
  description: string;
  steps: PermissionInstructionStep[];
}

// Istruzioni per l'autorizzazione di monitoraggio app (Usage Stats)
const usageStatsInstructions: Record<string, PermissionInstructions> = {
  // Samsung
  samsung: {
    title: 'Attiva Accesso dati utilizzo',
    description: 'Su Samsung, segui questi passaggi per attivare l\'accesso ai dati di utilizzo:',
    steps: [
      {
        title: 'Apri Impostazioni',
        menuItems: ['Impostazioni']
      },
      {
        title: 'Vai a Applicazioni',
        menuItems: ['Applicazioni']
      },
      {
        title: 'Tocca Menu ⋮ e seleziona Accesso speciale',
        menuItems: ['Menu ⋮', 'Accesso speciale']
      },
      {
        title: 'Seleziona Accesso dati utilizzo',
        menuItems: ['Accesso dati utilizzo']
      },
      {
        title: 'Trova KidSafety e attiva',
        menuItems: ['KidSafety'],
        finalAction: 'Attiva l\'interruttore'
      }
    ]
  },
  
  // Xiaomi / MIUI
  xiaomi: {
    title: 'Attiva Accesso dati utilizzo',
    description: 'Su Xiaomi/MIUI, segui questi passaggi per attivare l\'accesso ai dati di utilizzo:',
    steps: [
      {
        title: 'Apri Impostazioni',
        menuItems: ['Impostazioni']
      },
      {
        title: 'Vai a App',
        menuItems: ['App', 'Gestione app']
      },
      {
        title: 'Tocca Autorizzazioni app',
        menuItems: ['Autorizzazioni app']
      },
      {
        title: 'Seleziona Altre autorizzazioni',
        menuItems: ['Altre autorizzazioni']
      },
      {
        title: 'Seleziona Accesso dati utilizzo',
        menuItems: ['Accesso dati utilizzo', 'Statistiche di utilizzo']
      },
      {
        title: 'Trova KidSafety e attiva',
        menuItems: ['KidSafety'],
        finalAction: 'Attiva l\'interruttore'
      }
    ]
  },
  
  // Huawei / EMUI
  huawei: {
    title: 'Attiva Accesso dati utilizzo',
    description: 'Su Huawei/EMUI, segui questi passaggi per attivare l\'accesso ai dati di utilizzo:',
    steps: [
      {
        title: 'Apri Impostazioni',
        menuItems: ['Impostazioni']
      },
      {
        title: 'Vai a App',
        menuItems: ['App', 'Applicazioni']
      },
      {
        title: 'Tocca Autorizzazioni',
        menuItems: ['Autorizzazioni']
      },
      {
        title: 'Seleziona Altre autorizzazioni',
        menuItems: ['Altre autorizzazioni']
      },
      {
        title: 'Seleziona Accesso dati utilizzo',
        menuItems: ['Accesso dati utilizzo', 'Statistiche di utilizzo']
      },
      {
        title: 'Trova KidSafety e attiva',
        menuItems: ['KidSafety'],
        finalAction: 'Attiva l\'interruttore'
      }
    ]
  },
  
  // OPPO / ColorOS
  oppo: {
    title: 'Attiva Accesso dati utilizzo',
    description: 'Su OPPO/ColorOS, segui questi passaggi per attivare l\'accesso ai dati di utilizzo:',
    steps: [
      {
        title: 'Apri Impostazioni',
        menuItems: ['Impostazioni']
      },
      {
        title: 'Vai a Privacy',
        menuItems: ['Privacy']
      },
      {
        title: 'Seleziona Gestione autorizzazioni',
        menuItems: ['Gestione autorizzazioni']
      },
      {
        title: 'Tocca Altre autorizzazioni',
        menuItems: ['Altre autorizzazioni']
      },
      {
        title: 'Seleziona Accesso dati utilizzo',
        menuItems: ['Accesso dati utilizzo', 'Statistiche di utilizzo']
      },
      {
        title: 'Trova KidSafety e attiva',
        menuItems: ['KidSafety'],
        finalAction: 'Attiva l\'interruttore'
      }
    ]
  },
  
  // OnePlus / OxygenOS
  oneplus: {
    title: 'Attiva Accesso dati utilizzo',
    description: 'Su OnePlus/OxygenOS, segui questi passaggi per attivare l\'accesso ai dati di utilizzo:',
    steps: [
      {
        title: 'Apri Impostazioni',
        menuItems: ['Impostazioni']
      },
      {
        title: 'Vai a App e notifiche',
        menuItems: ['App e notifiche', 'App']
      },
      {
        title: 'Tocca Accesso speciale',
        menuItems: ['Accesso speciale']
      },
      {
        title: 'Seleziona Accesso dati utilizzo',
        menuItems: ['Accesso dati utilizzo']
      },
      {
        title: 'Trova KidSafety e attiva',
        menuItems: ['KidSafety'],
        finalAction: 'Attiva l\'interruttore'
      }
    ]
  },
  
  // Google / Pixel
  google: {
    title: 'Attiva Accesso dati utilizzo',
    description: 'Su Google Pixel, segui questi passaggi per attivare l\'accesso ai dati di utilizzo:',
    steps: [
      {
        title: 'Apri Impostazioni',
        menuItems: ['Impostazioni']
      },
      {
        title: 'Vai a App',
        menuItems: ['App', 'Applicazioni']
      },
      {
        title: 'Tocca Accesso speciale',
        menuItems: ['Accesso speciale']
      },
      {
        title: 'Seleziona Accesso dati utilizzo',
        menuItems: ['Accesso dati utilizzo']
      },
      {
        title: 'Trova KidSafety e attiva',
        menuItems: ['KidSafety'],
        finalAction: 'Attiva l\'interruttore'
      }
    ]
  },
  
  // Motorola
  motorola: {
    title: 'Attiva Accesso dati utilizzo',
    description: 'Su Motorola, segui questi passaggi per attivare l\'accesso ai dati di utilizzo:',
    steps: [
      {
        title: 'Apri Impostazioni',
        menuItems: ['Impostazioni']
      },
      {
        title: 'Vai a App e notifiche',
        menuItems: ['App e notifiche', 'App']
      },
      {
        title: 'Tocca Accesso speciale',
        menuItems: ['Accesso speciale']
      },
      {
        title: 'Seleziona Accesso dati utilizzo',
        menuItems: ['Accesso dati utilizzo']
      },
      {
        title: 'Trova KidSafety e attiva',
        menuItems: ['KidSafety'],
        finalAction: 'Attiva l\'interruttore'
      }
    ]
  },
  
  // Generico (per tutti gli altri dispositivi)
  generic: {
    title: 'Attiva Accesso dati utilizzo',
    description: 'Segui questi passaggi per attivare l\'accesso ai dati di utilizzo:',
    steps: [
      {
        title: 'Apri Impostazioni',
        menuItems: ['Impostazioni']
      },
      {
        title: 'Vai a App',
        menuItems: ['App', 'Applicazioni']
      },
      {
        title: 'Cerca Accesso speciale o Autorizzazioni speciali',
        menuItems: ['Accesso speciale', 'Autorizzazioni speciali']
      },
      {
        title: 'Seleziona Accesso dati utilizzo',
        menuItems: ['Accesso dati utilizzo', 'Statistiche di utilizzo']
      },
      {
        title: 'Trova KidSafety e attiva',
        menuItems: ['KidSafety'],
        finalAction: 'Attiva l\'interruttore'
      }
    ]
  }
};



// Istruzioni per la disattivazione dell'ottimizzazione della batteria
const batteryOptimizationInstructions: Record<string, PermissionInstructions> = {
  // Samsung
  samsung: {
    title: 'Disattiva Ottimizzazione Batteria',
    description: 'Su Samsung, segui questi passaggi per disattivare l\'ottimizzazione della batteria:',
    steps: [
      {
        title: 'Apri Impostazioni',
        menuItems: ['Impostazioni']
      },
      {
        title: 'Vai a Assistenza dispositivo',
        menuItems: ['Assistenza dispositivo', 'Batteria']
      },
      {
        title: 'Tocca Batteria',
        menuItems: ['Batteria']
      },
      {
        title: 'Seleziona App non monitorate',
        menuItems: ['App non monitorate', 'App in sospensione']
      },
      {
        title: 'Trova KidSafety e disattiva',
        menuItems: ['KidSafety'],
        finalAction: 'Disattiva l\'interruttore'
      }
    ]
  },
  
  // Xiaomi / MIUI
  xiaomi: {
    title: 'Disattiva Ottimizzazione Batteria',
    description: 'Su Xiaomi/MIUI, segui questi passaggi per disattivare l\'ottimizzazione della batteria:',
    steps: [
      {
        title: 'Apri Impostazioni',
        menuItems: ['Impostazioni']
      },
      {
        title: 'Vai a Batteria e prestazioni',
        menuItems: ['Batteria e prestazioni', 'Batteria']
      },
      {
        title: 'Seleziona Risparmio batteria app',
        menuItems: ['Risparmio batteria app', 'Gestione batteria app']
      },
      {
        title: 'Trova KidSafety',
        menuItems: ['KidSafety']
      },
      {
        title: 'Seleziona Nessuna restrizione',
        menuItems: ['Nessuna restrizione'],
        finalAction: 'Tocca Nessuna restrizione'
      }
    ]
  },
  
  // Generico (per tutti gli altri dispositivi)
  generic: {
    title: 'Disattiva Ottimizzazione Batteria',
    description: 'Segui questi passaggi per disattivare l\'ottimizzazione della batteria:',
    steps: [
      {
        title: 'Apri Impostazioni',
        menuItems: ['Impostazioni']
      },
      {
        title: 'Vai a Batteria',
        menuItems: ['Batteria', 'Ottimizzazione batteria']
      },
      {
        title: 'Cerca Ottimizzazione batteria',
        menuItems: ['Ottimizzazione batteria', 'Risparmio energetico']
      },
      {
        title: 'Seleziona Tutte le app',
        menuItems: ['Tutte le app', 'Non ottimizzate']
      },
      {
        title: 'Trova KidSafety',
        menuItems: ['KidSafety']
      },
      {
        title: 'Seleziona Non ottimizzare',
        menuItems: ['Non ottimizzare'],
        finalAction: 'Tocca Non ottimizzare'
      }
    ]
  }
};

/**
 * Ottiene le istruzioni specifiche per il tipo di autorizzazione e il produttore del dispositivo
 * @param permissionType Tipo di autorizzazione
 * @param deviceType Tipo di dispositivo/UI
 * @returns Istruzioni specifiche per il dispositivo
 */
export const getPermissionInstructions = (
  permissionType: 'usageStats' | 'accessibility' | 'batteryOptimization',
  deviceType: 'samsung' | 'xiaomi' | 'huawei' | 'oppo' | 'oneplus' | 'google' | 'motorola' | 'generic'
): PermissionInstructions => {
  let instructionsMap: Record<string, PermissionInstructions>;
  
  switch (permissionType) {
    case 'usageStats':
      instructionsMap = usageStatsInstructions;
      break;

    case 'batteryOptimization':
      instructionsMap = batteryOptimizationInstructions;
      break;
    default:
      instructionsMap = usageStatsInstructions;
  }
  
  // Se non ci sono istruzioni specifiche per questo dispositivo, usa quelle generiche
  return instructionsMap[deviceType] || instructionsMap.generic;
};
