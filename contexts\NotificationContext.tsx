import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import * as Notifications from 'expo-notifications';
import { registerForPushNotificationsAsync, saveExpoTokenToUser, setupNotifications } from '../lib/notifications';

// Define the context type
type NotificationContextType = {
  expoPushToken: string | null;
  notification: Notifications.Notification | null;
  notificationResponse: Notifications.NotificationResponse | null;
  setupNotifications: () => Promise<void>;
};

// Create the context with default values
const NotificationContext = createContext<NotificationContextType>({
  expoPushToken: null,
  notification: null,
  notificationResponse: null,
  setupNotifications: async () => {},
});

// Custom hook to use the notification context
export const useNotifications = () => useContext(NotificationContext);

// Provider component
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [expoPushToken, setExpoPushToken] = useState<string | null>(null);
  const [notification, setNotification] = useState<Notifications.Notification | null>(null);
  const [notificationResponse, setNotificationResponse] = useState<Notifications.NotificationResponse | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    // Register for push notifications
    registerForPushNotificationsAsync().then(token => {
      if (token) {
        setExpoPushToken(token);

        // Save token to database if user is logged in
        if (user) {
          saveExpoTokenToUser(user.id, token);
        }
      }
    });

    // Set up notification handlers
    setupNotifications();

    // Listen for notifications received while app is in foreground
    const notificationListener = Notifications.addNotificationReceivedListener(notification => {
      setNotification(notification);
    });

    // Listen for notification interactions
    const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      setNotificationResponse(response);
    });

    return () => {
      // Clean up listeners
      Notifications.removeNotificationSubscription(notificationListener);
      Notifications.removeNotificationSubscription(responseListener);
    };
  }, [user]);

  // Set up notifications
  const initializeNotifications = async () => {
    await setupNotifications();
  };

  return (
    <NotificationContext.Provider
      value={{
        expoPushToken,
        notification,
        notificationResponse,
        setupNotifications: initializeNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

// Default export to satisfy Expo Router
export default NotificationProvider;
