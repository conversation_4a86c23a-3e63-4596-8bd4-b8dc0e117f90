import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

// Register for push notifications
export async function registerForPushNotificationsAsync() {
  let token;

  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return;
    }

    token = (await Notifications.getExpoPushTokenAsync()).data;
  } else {
    console.log('Must use physical device for Push Notifications');
  }

  return token;
}

// Schedule a local notification
export async function scheduleLocalNotification(title: string, body: string, data: any = {}) {
  await Notifications.scheduleNotificationAsync({
    content: {
      title,
      body,
      data,
      sound: true,
    },
    trigger: null, // null means show immediately
  });
}

// Send a zone notification
export async function sendZoneNotification(childName: string, zoneName: string, action: 'entered' | 'exited', parentToken?: string) {
  const title = `${childName} ${action} ${zoneName}`;
  const body = action === 'entered'
    ? `${childName} has arrived at ${zoneName}`
    : `${childName} has left ${zoneName}`;

  // Invia notifica locale
  await scheduleLocalNotification(title, body, {
    type: 'zone',
    childName,
    zoneName,
    action,
    timestamp: new Date().toISOString(),
  });

  // Se è fornito un token del genitore, invia anche una notifica push
  if (parentToken) {
    console.log(`Sending push notification to parent for zone alert: ${childName} ${action} ${zoneName}`);
    await sendPushNotification(parentToken, title, body, {
      type: 'zone',
      childName,
      zoneName,
      action,
      timestamp: new Date().toISOString(),
    });
  }
}

// Send a mission notification
export async function sendMissionNotification(childName: string, missionName: string, status: 'completed' | 'started') {
  const title = `Mission ${status}`;
  const body = status === 'completed'
    ? `${childName} has completed the mission: ${missionName}`
    : `${childName} has started the mission: ${missionName}`;

  await scheduleLocalNotification(title, body, {
    type: 'mission',
    childName,
    missionName,
    status,
    timestamp: new Date().toISOString(),
  });
}

// Send an SOS notification
export async function sendSOSNotification(childName: string, location: string, parentToken?: string) {
  const title = `🚨 SOS Alert from ${childName}`;
  const body = `${childName} has sent an emergency alert from ${location}`;

  // Se è fornito un token del genitore, invia SOLO una notifica push (NO notifica locale sul dispositivo del bambino)
  if (parentToken) {
    console.log(`Sending high priority push notification to parent for SOS alert: ${childName}`);
    await sendPushNotification(parentToken, title, body, {
      type: 'sos',
      childName,
      location,
      timestamp: new Date().toISOString(),
      priority: 'high',
      sound: 'default',
      badge: 1
    });
  } else {
    // Solo se non c'è un token del genitore, crea una notifica locale come fallback
    console.log(`No parent token provided, creating local notification as fallback`);
    await scheduleLocalNotification(title, body, {
      type: 'sos',
      childName,
      location,
      timestamp: new Date().toISOString(),
    });
  }
}

// Create an SOS notification
export async function createSOSNotification(childName: string) {
  try {
    const title = 'SOS Alert!';
    const body = `${childName} has triggered an SOS alert!`;

    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: { type: 'sos' },
        sound: true,
        priority: Notifications.AndroidNotificationPriority.MAX,
      },
      trigger: null, // null means show immediately
    });

    console.log(`SOS notification created for ${childName}`);
  } catch (error) {
    console.error('Error creating SOS notification:', error);
  }
}

// Save Expo push token to user
export async function saveExpoTokenToUser(userId: string, token: string) {
  try {
    // Make a direct API call to save the token
    const response = await fetch('https://urtuplpcvvvmnmebabxy.supabase.co/rest/v1/user_push_tokens', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVydHVwbHBjdnZ2bW5tZWJhYnh5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTMzNzA1NzYsImV4cCI6MjAyODk0NjU3Nn0.Nh0Qs9jXFLnYWvCdCRSXRgOCl0HQRDjgnLnrHoUYl8c',
        'Prefer': 'return=representation'
      },
      body: JSON.stringify({
        user_id: userId,
        token: token,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to save token: ${response.statusText}`);
    }

    console.log(`Expo push token saved for user ${userId}`);
    return true;
  } catch (error) {
    console.error('Error saving Expo push token:', error);
    return false;
  }
}

// Send a push notification to a specific device
export async function sendPushNotification(token: string, title: string, body: string, data: any = {}) {
  try {
    const message = {
      to: token,
      sound: data.type === 'sos' ? 'default' : 'default',
      title: title,
      body: body,
      data: data,
      priority: data.type === 'sos' ? 'high' : 'normal',
      badge: data.badge || 1,
      // Per le notifiche SOS, aggiungiamo configurazioni specifiche per Android
      ...(data.type === 'sos' && {
        android: {
          channelId: 'emergency',
          priority: 'max',
          sound: 'default',
          vibrate: [0, 250, 250, 250],
          lights: true,
          color: '#FF0000'
        }
      })
    };

    console.log(`Sending ${data.type === 'sos' ? 'HIGH PRIORITY SOS' : 'normal'} push notification to ${token}: ${title}`);

    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });

    const responseData = await response.json();
    console.log('Push notification response:', responseData);

    // Log the notification attempt
    if (data.alertId) {
      await logNotificationAttempt(data.alertId, token, responseData.data ? 'sent' : 'failed', responseData.details?.error);
    }

    return responseData;
  } catch (error) {
    console.error('Error sending push notification:', error);
    return false;
  }
}

// Add notification listeners
export function addNotificationListeners(
  onNotification: (notification: Notifications.Notification) => void
) {
  const notificationListener = Notifications.addNotificationReceivedListener(
    (notification) => {
      onNotification(notification);
    }
  );

  const responseListener = Notifications.addNotificationResponseReceivedListener(
    (response) => {
      console.log(response);
    }
  );

  return { notificationListener, responseListener };
}

// Remove notification listeners
export function removeNotificationListeners(listeners: {
  notificationListener: Notifications.Subscription;
  responseListener: Notifications.Subscription;
}) {
  Notifications.removeNotificationSubscription(listeners.notificationListener);
  Notifications.removeNotificationSubscription(listeners.responseListener);
}

// Get parent push token for a child
export async function getParentPushToken(childId: string): Promise<string | null> {
  try {
    // Prima ottieni l'ID del genitore
    const response = await fetch(`https://urtuplpcvvvmnmebabxy.supabase.co/rest/v1/parent_child?child_id=eq.${childId}&select=parent_id`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVydHVwbHBjdnZ2bW5tZWJhYnh5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTMzNzA1NzYsImV4cCI6MjAyODk0NjU3Nn0.Nh0Qs9jXFLnYWvCdCRSXRgOCl0HQRDjgnLnrHoUYl8c'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get parent ID: ${response.statusText}`);
    }

    const parentChildData = await response.json();
    if (!parentChildData || parentChildData.length === 0) {
      console.log(`No parent found for child ${childId}`);
      return null;
    }

    const parentId = parentChildData[0].parent_id;
    console.log(`Found parent ID ${parentId} for child ${childId}`);

    // Ora ottieni il token push del genitore
    const tokenResponse = await fetch(`https://urtuplpcvvvmnmebabxy.supabase.co/rest/v1/user_push_tokens?user_id=eq.${parentId}&select=token`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVydHVwbHBjdnZ2bW5tZWJhYnh5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTMzNzA1NzYsImV4cCI6MjAyODk0NjU3Nn0.Nh0Qs9jXFLnYWvCdCRSXRgOCl0HQRDjgnLnrHoUYl8c'
      }
    });

    if (!tokenResponse.ok) {
      throw new Error(`Failed to get parent token: ${tokenResponse.statusText}`);
    }

    const tokenData = await tokenResponse.json();
    if (!tokenData || tokenData.length === 0) {
      console.log(`No push token found for parent ${parentId}`);
      return null;
    }

    console.log(`Found push token for parent ${parentId}`);
    return tokenData[0].token;
  } catch (error) {
    console.error('Error getting parent push token:', error);
    return null;
  }
}

// Setup notifications
export async function setupNotifications() {
  // Configure notification handler
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
      shouldShowBanner: true,
      shouldShowList: true,
    }),
  });

  // Configure Android notification channel
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });

    // Canale ad alta priorità per SOS
    await Notifications.setNotificationChannelAsync('sos', {
      name: 'SOS Alerts',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250, 250, 250],
      lightColor: '#FF0000',
      sound: 'default',
    });
  }

  // Request notification permissions
  const { status } = await Notifications.requestPermissionsAsync();
  if (status !== 'granted') {
    console.log('Notification permissions not granted');
  } else {
    console.log('Notification permissions granted');
  }
}

// Log notification attempts for debugging and reliability
async function logNotificationAttempt(alertId: string, pushToken: string, status: 'sent' | 'failed', errorMessage?: string) {
  try {
    const response = await fetch('https://urtuplpcvvvmnmebabxy.supabase.co/rest/v1/notification_logs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVydHVwbHBjdnZ2bW5tZWJhYnh5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTMzNzA1NzYsImV4cCI6MjAyODk0NjU3Nn0.Nh0Qs9jXFLnYWvCdCRSXRgOCl0HQRDjgnLnrHoUYl8c'
      },
      body: JSON.stringify({
        alert_id: alertId,
        notification_type: 'sos_push',
        status: status,
        push_token: pushToken,
        error_message: errorMessage,
        created_at: new Date().toISOString()
      })
    });

    if (!response.ok) {
      console.error('Failed to log notification attempt:', response.statusText);
    }
  } catch (error) {
    console.error('Error logging notification attempt:', error);
  }
}

// Default export to satisfy Expo Router
export default {
  registerForPushNotificationsAsync,
  scheduleLocalNotification,
  sendZoneNotification,
  sendMissionNotification,
  sendSOSNotification,
  createSOSNotification,
  saveExpoTokenToUser,
  sendPushNotification,
  addNotificationListeners,
  removeNotificationListeners,
  getParentPushToken,
  setupNotifications,
  logNotificationAttempt
};
