const fs = require('fs');
const path = require('path');

// Lista dei file che necessitano di placeholder
const routeFiles = [
  // Child tabs
  'app/(child-tabs)/_layout.tsx',
  'app/(child-tabs)/dashboard.tsx',
  'app/(child-tabs)/homework-helper.tsx',
  'app/(child-tabs)/settings.tsx',
  'app/(child-tabs)/sos.tsx',
  
  // Parent tabs
  'app/(parent-tabs)/_layout.tsx',
  'app/(parent-tabs)/app-monitoring.tsx',
  'app/(parent-tabs)/children/[id].tsx',
  'app/(parent-tabs)/children/index.tsx',
  'app/(parent-tabs)/dashboard.tsx',
  'app/(parent-tabs)/family-account.tsx',
  'app/(parent-tabs)/family-invitation.tsx',
  'app/(parent-tabs)/home.tsx',
  'app/(parent-tabs)/map.tsx',
  'app/(parent-tabs)/missions.tsx',
  'app/(parent-tabs)/routes.tsx',
  'app/(parent-tabs)/safe-zones.tsx',
  'app/(parent-tabs)/settings.tsx',
  'app/(parent-tabs)/sos-alert/[id].tsx',
  'app/(parent-tabs)/subscription-management.tsx',
  'app/(parent-tabs)/subscription.tsx',
  
  // Auth
  'app/auth/_layout.tsx',
  'app/auth/child-login.tsx',
  'app/auth/child-onboarding.tsx',
  'app/auth/onboarding.tsx',
  'app/auth/parent-login.tsx',
  'app/auth/parent-signup.tsx',
  'app/auth/trial-info.tsx'
];

// Template per i placeholder
const createPlaceholder = (fileName, routePath) => {
  const componentName = fileName
    .replace(/\[|\]/g, '') // Rimuovi parentesi quadre
    .replace(/[^a-zA-Z0-9]/g, '') // Rimuovi caratteri speciali
    .replace(/^./, str => str.toUpperCase()); // Prima lettera maiuscola
  
  const displayName = fileName
    .replace(/\[|\]/g, '')
    .replace(/[-_]/g, ' ')
    .replace(/\.(tsx|ts)$/, '')
    .replace(/^./, str => str.toUpperCase());

  return `import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function ${componentName}() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>${displayName}</Text>
      <Text style={styles.subtitle}>Placeholder per: ${routePath}</Text>
      <Text style={styles.info}>Questa pagina verrà implementata successivamente.</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
    textAlign: 'center',
  },
  info: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
});
`;
};

// Funzione per creare le directory se non esistono
const ensureDirectoryExists = (filePath) => {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

// Crea i placeholder
console.log('🔧 Creazione placeholder per le route...');

routeFiles.forEach(routePath => {
  const fullPath = path.join(process.cwd(), routePath);
  const fileName = path.basename(routePath);
  
  // Controlla se il file esiste e se è vuoto o ha problemi
  let needsPlaceholder = false;
  
  if (!fs.existsSync(fullPath)) {
    needsPlaceholder = true;
    console.log(`📄 File non esistente: ${routePath}`);
  } else {
    const content = fs.readFileSync(fullPath, 'utf8').trim();
    if (content.length === 0 || !content.includes('export default')) {
      needsPlaceholder = true;
      console.log(`📄 File vuoto o senza export: ${routePath}`);
    }
  }
  
  if (needsPlaceholder) {
    ensureDirectoryExists(fullPath);
    const placeholder = createPlaceholder(fileName, routePath);
    fs.writeFileSync(fullPath, placeholder);
    console.log(`✅ Creato placeholder: ${routePath}`);
  } else {
    console.log(`⏭️  File OK: ${routePath}`);
  }
});

console.log('🎉 Completato! Tutti i placeholder sono stati creati.');
