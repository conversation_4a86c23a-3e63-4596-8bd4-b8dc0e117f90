import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { Marker, Callout } from 'react-native-maps';

type ChildLocationMarkerProps = {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  lastUpdated?: string;
  accuracy?: number;
  imageUrl?: string;
  batteryLevel?: number;
  onPress?: () => void;
};

export default function ChildLocationMarker({
  id,
  name,
  latitude,
  longitude,
  lastUpdated,
  accuracy,
  imageUrl,
  batteryLevel,
  onPress,
}: ChildLocationMarkerProps) {
  // Format date string
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) + 
           ', ' + date.toLocaleDateString();
  };

  // Format battery level
  const formatBattery = (level?: number) => {
    if (level === undefined || level === null) return 'Unknown';
    return `${Math.round(level * 100)}%`;
  };
  
  // Get battery indicator color
  const getBatteryColor = (level?: number) => {
    if (level === undefined || level === null) return '#999';
    if (level < 0.2) return '#FF3B30';
    if (level < 0.4) return '#FF9500';
    return '#34C759';
  };

  return (
    <Marker
      identifier={`child-location-${id}`}
      coordinate={{
        latitude,
        longitude,
      }}
      title={name}
      description={`Last seen: ${lastUpdated ? formatDate(lastUpdated) : 'Unknown'}`}
      onPress={onPress}
    >
      <View style={styles.markerContainer}>
        {/* Child image or placeholder */}
        <View style={styles.imageContainer}>
          {imageUrl ? (
            <Image source={{ uri: imageUrl }} style={styles.image} />
          ) : (
            <View style={styles.placeholder}>
              <Text style={styles.placeholderText}>{name.charAt(0)}</Text>
            </View>
          )}
          
          {/* Battery indicator */}
          {batteryLevel !== undefined && (
            <View style={[styles.batteryIndicator, { backgroundColor: getBatteryColor(batteryLevel) }]} />
          )}
        </View>
      </View>
      
      {/* Info window when marker is tapped */}
      <Callout tooltip>
        <View style={styles.calloutContainer}>
          <Text style={styles.calloutTitle}>{name}</Text>
          
          <View style={styles.calloutDetailsContainer}>
            {lastUpdated && (
              <Text style={styles.calloutDetail}>
                Last seen: {formatDate(lastUpdated)}
              </Text>
            )}
            
            {accuracy !== undefined && (
              <Text style={styles.calloutDetail}>
                Accuracy: ±{Math.round(accuracy)}m
              </Text>
            )}
            
            {batteryLevel !== undefined && (
              <Text style={styles.calloutDetail}>
                Battery: {formatBattery(batteryLevel)}
              </Text>
            )}
          </View>
        </View>
      </Callout>
    </Marker>
  );
}

const styles = StyleSheet.create({
  markerContainer: {
    alignItems: 'center',
  },
  imageContainer: {
    position: 'relative',
    borderWidth: 2,
    borderColor: '#FFFFFF',
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  image: {
    width: 40,
    height: 40,
  },
  placeholder: {
    width: 40,
    height: 40,
    backgroundColor: '#4630EB',
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 18,
  },
  batteryIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 10,
    height: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  calloutContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    padding: 10,
    width: 160,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  calloutTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 5,
    textAlign: 'center',
  },
  calloutDetailsContainer: {
    
  },
  calloutDetail: {
    fontSize: 12,
    color: '#666',
    marginVertical: 2,
  },
});