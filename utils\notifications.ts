import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { supabase } from './supabase';
import Constants from 'expo-constants';

// Configure notifications based on platform
export async function configurePushNotifications() {
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  // Set how notifications are handled when the app is in the foreground
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
      shouldShowBanner: true,
      shouldShowList: true,
    }),
  });
}

// Save Expo push token to Supabase for a specific user
export async function saveExpoTokenToUser(userId: string, token: string) {
  try {
    const { error } = await supabase
      .from('user_push_tokens')
      .upsert(
        {
          user_id: userId,
          token: token,
          device_name: Device.deviceName || 'Unknown device',
          updated_at: new Date().toISOString()
        },
        { onConflict: 'user_id' }
      );

    if (error) {
      console.error('Error saving push token:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error saving push token:', error);
    return false;
  }
}

// Send a push notification to a specific user
export async function sendPushNotification(
  expoPushToken: string,
  title: string,
  body: string,
  data: any = {}
) {
  const message = {
    to: expoPushToken,
    sound: 'default',
    title: title,
    body: body,
    data: data,
  };

  await fetch('https://exp.host/--/api/v2/push/send', {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Accept-encoding': 'gzip, deflate',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(message),
  });
}

// Register for push notifications and set up handlers
export async function registerForPushNotificationsAsync() {
  let token;

  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Constants.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    console.log('[Notifications] Current notification permission status:', existingStatus);

    if (existingStatus !== 'granted') {
      console.log('[Notifications] Requesting notification permissions...');
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
      console.log('[Notifications] New notification permission status:', status);
    }

    if (finalStatus !== 'granted') {
      console.log('[Notifications] Failed to get push token for push notification!');
      return null;
    }

    try {
      const pushToken = await Notifications.getExpoPushTokenAsync();
      token = pushToken.data;
      console.log('[Notifications] Push token:', token);
    } catch (error) {
      console.error('[Notifications] Error getting push token:', error);
      return null;
    }
  } else {
    console.log('[Notifications] Must use physical device for Push Notifications');
    return null;
  }

  // Configure platform-specific settings
  await configurePushNotifications();

  return token;
}

// Schedule a local notification
export async function scheduleLocalNotification(
  title: string,
  body: string,
  data = {},
  trigger: any = null
) {
  try {
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
      },
      trigger: trigger, // null will trigger immediately
    });

    return notificationId;
  } catch (error) {
    console.error('Error scheduling notification:', error);
    return null;
  }
}

// Create an immediate notification
export async function sendImmediateNotification(title: string, body: string, data = {}) {
  return scheduleLocalNotification(title, body, data);
}

// Create a geofence notification
export async function createGeofenceNotification(childName: string, zoneName: string, isEntering: boolean) {
  try {
    console.log(`[Notifications] Creating geofence notification for ${childName} ${isEntering ? 'entering' : 'leaving'} ${zoneName}`);

    const title = isEntering
      ? `${childName} has entered a safe zone`
      : `${childName} has left a safe zone`;

    const body = isEntering
      ? `${childName} has entered the "${zoneName}" safe zone`
      : `${childName} has left the "${zoneName}" safe zone`;

    // Assicuriamoci che il suono sia attivato
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: {
          type: 'geofence',
          childName,
          zoneName,
          isEntering,
          timestamp: new Date().toISOString(),
        },
        sound: true,
        priority: Notifications.AndroidNotificationPriority.HIGH,
      },
      trigger: null, // Trigger immediately
    });

    console.log(`[Notifications] Geofence notification sent successfully with ID: ${notificationId}`);
    return notificationId;
  } catch (error) {
    console.error('[Notifications] Error sending geofence notification:', error);
    return null;
  }
}

// Create an SOS notification
export async function createSOSNotification(childName: string) {
  await Notifications.scheduleNotificationAsync({
    content: {
      title: 'SOS Alert!',
      body: `${childName} has triggered an SOS alert!`,
      data: { type: 'sos' },
      sound: true,
      priority: Notifications.AndroidNotificationPriority.MAX,
    },
    trigger: null, // Trigger immediately
  });
}

// Create a mission notification
export async function createMissionNotification(missionTitle: string, assignedTo?: string) {
  const title = assignedTo
    ? `New mission assigned to ${assignedTo}`
    : 'New mission assigned';

  const body = `"${missionTitle}" has been assigned. Tap to see details.`;

  return sendImmediateNotification(title, body, {
    type: 'mission',
    missionTitle,
    timestamp: new Date().toISOString(),
  });
}

// Create a mission completion notification
export async function createMissionCompletionNotification(childName: string, missionTitle: string) {
  const title = `Mission completed by ${childName}`;
  const body = `${childName} has completed "${missionTitle}"! Tap to verify.`;

  return sendImmediateNotification(title, body, {
    type: 'mission_completed',
    childName,
    missionTitle,
    timestamp: new Date().toISOString(),
  });
}

// Create a mission verification notification
export async function createMissionVerificationNotification(missionTitle: string, rewardPoints: number) {
  const title = 'Mission Verified! 🎉';
  const body = `Your mission "${missionTitle}" has been verified! You earned ${rewardPoints} stars!`;

  try {
    return await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: {
          type: 'mission_verified',
          missionTitle,
          reward: rewardPoints.toString(),
          timestamp: new Date().toISOString(),
        },
      },
      trigger: null, // Trigger immediately
    });
  } catch (error) {
    console.error('Error scheduling mission verification notification:', error);
    return null;
  }
}

// Clear all notifications
export async function clearAllNotifications() {
  return Notifications.dismissAllNotificationsAsync();
}

// Listen for notification interactions
export function addNotificationResponseListener(callback: (response: Notifications.NotificationResponse) => void) {
  return Notifications.addNotificationResponseReceivedListener(callback);
}

// Listen for notifications received while app is in foreground
export function addNotificationReceivedListener(callback: (notification: Notifications.Notification) => void) {
  return Notifications.addNotificationReceivedListener(callback);
}

// Send a notification to a specific child (for parent use)
export async function sendNotificationToChild(
  childId: string,
  title: string,
  body: string,
  data: object = {}
) {
  try {
    // Get the child's push token from the database
    const { data: tokenData, error } = await supabase
      .from('user_push_tokens')
      .select('token')
      .eq('user_id', childId);

    if (error) {
      console.error('Error fetching child push token:', error);
      return false;
    }

    // If no tokens found, just create a local notification
    if (!tokenData || tokenData.length === 0) {
      console.log('No push tokens found for child, creating local notification only');
      // Safely access data properties
      const missionTitle = typeof data === 'object' && data ? (data as any).title || 'Mission' : 'Mission';
      const rewardPoints = typeof data === 'object' && data ? parseInt((data as any).reward) || 10 : 10;

      return await createMissionVerificationNotification(missionTitle, rewardPoints);
    }

    // Send the notification to all tokens
    let success = false;
    for (const token of tokenData) {
      try {
        await sendPushNotification(
          token.token,
          title,
          body,
          {
            ...data,
            senderType: 'parent',
            timestamp: new Date().toISOString()
          }
        );
        success = true;
      } catch (tokenError) {
        console.error('Error sending to token:', token.token, tokenError);
      }
    }

    return success;
  } catch (error) {
    console.error('Error sending notification to child:', error);
    return false;
  }
}

export function setupNotifications() {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
      shouldShowBanner: true,
      shouldShowList: true,
    }),
  });
}

// Default export to satisfy Expo Router
export default {
  setupNotifications,
  configurePushNotifications,
  registerForPushNotificationsAsync,
  saveExpoTokenToUser,
  sendPushNotification,
  scheduleLocalNotification,
  sendImmediateNotification,
  createGeofenceNotification,
  createSOSNotification,
  createMissionNotification,
  createMissionCompletionNotification,
  createMissionVerificationNotification,
  clearAllNotifications,
  addNotificationResponseListener,
  addNotificationReceivedListener,
  sendNotificationToChild
};