import React, { useState, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Vibration,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { useAuth } from '../contexts/AuthContext';
import { createSOSAlert } from '../utils/supabase';
import { useTranslations } from '../contexts/TranslationContext';

type SOSButtonProps = {
  childId: string;
  onAlertSent?: () => void;
};

const SOSButton: React.FC<SOSButtonProps> = ({ childId, onAlertSent }) => {
  const [sending, setSending] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [lastAlertTime, setLastAlertTime] = useState<Date | null>(null);
  const [alertSent, setAlertSent] = useState(false);
  const { user } = useAuth();
  const { t } = useTranslations();

  // Clear countdown if modal is closed
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (showModal && countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prev => {
          // Vibrate on each count
          Vibration.vibrate(100);

          if (prev === 1) {
            // Send the alert when countdown reaches 0
            sendSOSAlert();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [showModal, countdown]);

  const handleSOSPress = async () => {
    if (sending) return; // Prevent multiple presses while sending

    // If there was a recent alert (within 2 minutes), show a confirmation
    if (lastAlertTime && (new Date().getTime() - lastAlertTime.getTime() < 120000)) {
      Alert.alert(
        t.sos.sendAnotherAlertTitle || 'Send Another Alert?',
        t.sos.sendAnotherAlertMessage || 'You recently sent an alert. Are you sure you want to send another?',
        [
          { text: t.common.cancel || 'Cancel', style: 'cancel' },
          {
            text: t.sos.sendAlert || 'Send Alert',
            style: 'destructive',
            onPress: startCountdown
          }
        ],
        { cancelable: true }
      );
    } else {
      startCountdown();
    }
  };

  const startCountdown = () => {
    setCountdown(5); // 5 second countdown
    setShowModal(true);
    // Start the vibration pattern
    Vibration.vibrate([0, 500]);
  };

  const cancelCountdown = () => {
    setCountdown(0);
    setShowModal(false);
    Vibration.cancel(); // Stop vibration
  };

  const sendSOSAlert = async () => {
    if (!user || !childId) {
      Alert.alert(t.common.error || 'Error', t.sos.authError || 'Authentication error, please log in again');
      setShowModal(false);
      return;
    }

    try {
      setSending(true);
      setShowModal(false);

      // Strong vibration to indicate the alert is being sent
      Vibration.vibrate([500, 300, 500, 300, 500]);

      // Get current location
      let { status } = await Location.requestForegroundPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert(t.sos.permissionDenied || 'Permission Denied', t.sos.locationPermissionNeeded || 'We need location permissions to send your location with the SOS alert');
        setSending(false);
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High
      });

      // Send SOS alert to database
      const result = await createSOSAlert(
        childId,
        location.coords.latitude,
        location.coords.longitude,
        `Battery: ${Math.round(100 * Math.random())}%, Accuracy: ${Math.round(location.coords.accuracy || 0)}m`
      );

      // Record the time of this alert
      setLastAlertTime(new Date());

      // Set alertSent to true to change button color to green
      setAlertSent(true);

      // Set a timeout to reset the button color after 10 seconds
      setTimeout(() => {
        setAlertSent(false);
      }, 10000); // 10 seconds

      // Success message with appropriate details
      if (result.parentFound && result.notificationSent) {
        Alert.alert(
          t.sos.alertSent || '🚨 SOS Alert Sent',
          t.sos.alertSentToParent || 'Your emergency alert has been sent successfully to your parent\'s device. They will receive a notification immediately.',
          [{ text: t.common.ok || 'OK' }]
        );
      } else if (result.parentFound && !result.notificationSent) {
        Alert.alert(
          t.sos.alertCreated || '⚠️ SOS Alert Created',
          'Your emergency alert has been recorded and your parent has been found, but there was an issue sending the notification. Please contact your parent directly.',
          [{ text: t.common.ok || 'OK' }]
        );
      } else {
        Alert.alert(
          t.sos.alertCreated || '📝 SOS Alert Recorded',
          t.sos.alertCreatedNoParent || 'Your emergency alert has been recorded, but we could not find a parent to notify. Please contact your parent directly if possible.',
          [{ text: t.common.ok || 'OK' }]
        );
      }

      // Call the callback if provided
      if (onAlertSent) {
        onAlertSent();
      }

    } catch (error) {
      console.error('Error sending SOS alert:', error);
      Alert.alert(t.common.error || 'Error', t.sos.sendAlertError || 'Failed to send SOS alert. Please try again.');
    } finally {
      setSending(false);
    }
  };

  // Determine the button status color
  const getStatusColor = () => {
    if (sending) return '#999'; // Gray when sending
    if (alertSent) return '#4CAF50'; // Green when alert is successfully sent
    if (lastAlertTime && (new Date().getTime() - lastAlertTime.getTime() < 300000)) {
      return '#FF9500'; // Orange if alert was sent in the last 5 minutes
    }
    return '#F44336'; // Red for normal state
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.sosButton, { backgroundColor: getStatusColor() }]}
        onPress={handleSOSPress}
        disabled={sending}
        activeOpacity={0.7}
      >
        {sending ? (
          <ActivityIndicator size="large" color="#fff" />
        ) : (
          <>
            <Ionicons name="warning" size={36} color="#fff" />
            <Text style={styles.sosText}>SOS</Text>
          </>
        )}
      </TouchableOpacity>

      {lastAlertTime && (
        <Text style={styles.lastSentText}>
          {t.sos.lastAlertSent || 'Last alert sent'}: {lastAlertTime.toLocaleTimeString()}
        </Text>
      )}

      <Text style={styles.helpText}>
        {t.sos.pressInCaseOfEmergency || 'Press in case of emergency'}
      </Text>

      {/* Countdown Modal */}
      <Modal
        visible={showModal}
        transparent={true}
        animationType="fade"
        onRequestClose={cancelCountdown}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.countdownTitle}>{t.sos.sendingAlertIn || 'Sending SOS Alert in'}</Text>
            <Text style={styles.countdownNumber}>{countdown}</Text>
            <Text style={styles.countdownDesc}>
              {t.sos.locationWillBeSent || 'Your location will be sent to your parent/guardian'}
            </Text>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={cancelCountdown}
            >
              <Text style={styles.cancelText}>{t.common.cancel || 'Cancel'}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  sosButton: {
    backgroundColor: '#F44336',
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
  },
  sosText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 5,
  },
  helpText: {
    marginTop: 16,
    fontSize: 16,
    color: '#555',
    textAlign: 'center',
  },
  lastSentText: {
    marginTop: 8,
    fontSize: 14,
    color: '#FF9500',
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContent: {
    width: 280,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 22,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5
  },
  countdownTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  countdownNumber: {
    fontSize: 60,
    fontWeight: 'bold',
    color: '#F44336',
    marginVertical: 15,
  },
  countdownDesc: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#EFEFEF',
    borderRadius: 8,
  },
  cancelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  }
});

export default SOSButton;