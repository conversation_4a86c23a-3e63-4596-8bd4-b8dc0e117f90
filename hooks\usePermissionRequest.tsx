import { useState, useEffect, useCallback } from 'react';
import { Platform, Alert, Linking } from 'react-native';
import { checkPermission, requestPermission, PermissionType, PermissionStatus } from '../utils/permissions';

export default function usePermissionRequest(
  permissionType: PermissionType,
  userType: 'parent' | 'child',
  options = { showExplainer: true }
) {
  const [permissionStatus, setPermissionStatus] = useState<PermissionStatus>('undetermined');
  const [showPermissionExplainer, setShowPermissionExplainer] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  // Check permission status on mount and when dependencies change
  useEffect(() => {
    let isMounted = true;
    const checkStatus = async () => {
      setIsChecking(true);
      try {
        const status = await checkPermission(permissionType);
        if (isMounted) {
          setPermissionStatus(status);
          
          // If permission is undetermined and we want to show the explainer
          if (status === 'undetermined' && options.showExplainer) {
            setShowPermissionExplainer(true);
          }
        }
      } catch (error) {
        console.error(`Error checking ${permissionType} permission:`, error);
      } finally {
        if (isMounted) {
          setIsChecking(false);
        }
      }
    };
    
    checkStatus();
    
    return () => {
      isMounted = false;
    };
  }, [permissionType, options.showExplainer]);

  // Request permission
  const requestPermissionAsync = useCallback(async () => {
    setIsChecking(true);
    try {
      const status = await requestPermission(permissionType);
      setPermissionStatus(status);
      setShowPermissionExplainer(false);
      return status;
    } catch (error) {
      console.error(`Error requesting ${permissionType} permission:`, error);
      return 'unavailable' as PermissionStatus;
    } finally {
      setIsChecking(false);
    }
  }, [permissionType]);

  // Open app settings
  const openSettings = useCallback(() => {
    if (Platform.OS === 'ios') {
      Linking.openURL('app-settings:');
    } else {
      Linking.openSettings();
    }
  }, []);

  // Handle permission denied with explanation
  const handlePermissionDenied = useCallback(() => {
    let title = "";
    let message = "";
    
    if (permissionType === 'location') {
      title = userType === 'child' 
        ? "Posizione non autorizzata" 
        : "Autorizzazione posizione necessaria";
      message = userType === 'child'
        ? "Per far funzionare questa app, i tuoi genitori devono poter vedere dove sei. Puoi modificare questa impostazione in qualsiasi momento."
        : "Senza questa autorizzazione, non potrai vedere la posizione di tuo figlio. Puoi abilitarla nelle impostazioni.";
    } else if (permissionType === 'backgroundLocation') {
      title = userType === 'child' 
        ? "Posizione in background non autorizzata" 
        : "Autorizzazione posizione in background necessaria";
      message = userType === 'child'
        ? "Per far funzionare questa app anche quando non la stai usando, è necessaria questa autorizzazione."
        : "Senza questa autorizzazione, potrai vedere la posizione di tuo figlio solo quando l'app è aperta.";
    } else {
      title = userType === 'child' 
        ? "Notifiche non autorizzate" 
        : "Autorizzazione notifiche necessaria";
      message = userType === 'child'
        ? "Senza notifiche, non sapremo quando entri o esci dalle zone sicure."
        : "Senza questa autorizzazione, non riceverai avvisi quando tuo figlio entra o esce da una zona sicura.";
    }
    
    Alert.alert(
      title,
      message,
      [
        { text: "Apri Impostazioni", onPress: openSettings },
        { text: "Non ora", style: "cancel" }
      ]
    );
  }, [permissionType, userType, openSettings]);

  return {
    permissionStatus,
    isChecking,
    showPermissionExplainer,
    requestPermission: requestPermissionAsync,
    dismissExplainer: () => setShowPermissionExplainer(false),
    openSettings,
    handlePermissionDenied,
  };
} 