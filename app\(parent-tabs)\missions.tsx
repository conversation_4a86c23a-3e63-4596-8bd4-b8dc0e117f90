import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Modal, TextInput } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import supabase, { getChildrenForParent, createMission, assignMissionToChild, getChildMissions, deleteMission } from '../../utils/supabase';
import { sendPushNotification, createMissionVerificationNotification, sendNotificationToChild } from '../../utils/notifications';
import Header from '../../components/shared/Header';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import RewardManager from '../../components/parent/RewardManager';

// Importiamo DateTimePicker in modo condizionale per evitare errori
let DateTimePicker: any;
try {
  DateTimePicker = require('@react-native-community/datetimepicker').default;
} catch (error) {
  console.warn('DateTimePicker not available:', error);
}

interface Child {
  child_id: string;
  users: {
    name: string;
  };
}

interface Mission {
  id: string;
  title: string;
  description: string;
  reward_points: number;
  due_date?: string;
  status?: 'pending' | 'in_progress' | 'completed' | 'verified';
  child_id?: string;
  child_name?: string;
}

export default function MissionsScreen() {
  const { user } = useAuth();
  const [children, setChildren] = useState<Child[]>([]);
  const [missions, setMissions] = useState<Mission[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Modal states
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedChild, setSelectedChild] = useState<Child | null>(null);
  const [childSelectVisible, setChildSelectVisible] = useState(false);

  // New mission form state
  const [newMission, setNewMission] = useState({
    title: '',
    description: '',
    reward_points: '10',
    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Default to 1 week from now
  });
  const [showDatePicker, setShowDatePicker] = useState(false);

  useEffect(() => {
    loadChildren();

    // Set up real-time subscription to mission_assignments table
    console.log('Setting up real-time subscription for mission assignments (parent view)');
    const subscription = supabase
      .channel('parent_mission_updates')
      .on('postgres_changes', {
        event: 'UPDATE',  // Listen for UPDATE events
        schema: 'public',
        table: 'mission_assignments',
        filter: `status=eq.completed`,  // Only listen for completed missions
      }, (payload) => {
        console.log('Real-time update received (parent):', payload);
        // Refresh missions when a child completes a mission
        if (payload.new && payload.new.status === 'completed') {
          Alert.alert(
            'Mission Completed!',
            'A child has completed a mission. Tap OK to refresh the list.',
            [{ text: 'OK', onPress: () => loadChildren() }]
          );
        }
      })
      .subscribe((status) => {
        console.log('Parent subscription status:', status);
      });

    // Clean up subscription when component unmounts
    return () => {
      console.log('Cleaning up parent subscription');
      subscription.unsubscribe();
    };
  }, []);

  const loadChildren = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      const childrenData = await getChildrenForParent(user.id);
      setChildren(childrenData);

      // After loading children, load missions for each child
      await loadMissions(childrenData);
    } catch (err) {
      console.error('Error loading children:', err);
      Alert.alert('Error', 'Failed to load children data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadMissions = async (childrenData: Child[]) => {
    if (!childrenData.length || !user) return;

    try {
      // Get all missions created by this parent
      const { data: parentMissions, error: missionsError } = await supabase
        .from('missions')
        .select('*')
        .eq('parent_id', user.id);

      if (missionsError) {
        console.error('Error fetching parent missions:', missionsError);
        return;
      }

      console.log('Parent missions:', parentMissions);

      // Get all mission assignments for children of this parent
      const childIds = childrenData.map(child => child.child_id);
      const { data: assignments, error: assignmentsError } = await supabase
        .from('mission_assignments')
        .select('*')
        .in('child_id', childIds);

      if (assignmentsError) {
        console.error('Error fetching mission assignments:', assignmentsError);
        return;
      }

      console.log('Mission assignments:', assignments);

      // Combine the data
      const formattedMissions: Mission[] = [];

      for (const assignment of assignments) {
        const mission = parentMissions.find(m => m.id === assignment.mission_id);
        if (mission) {
          const child = childrenData.find(c => c.child_id === assignment.child_id);
          formattedMissions.push({
            id: mission.id,
            title: mission.title,
            description: mission.description,
            reward_points: parseInt(mission.reward) || 0,
            due_date: mission.due_date,
            status: assignment.status,
            child_id: assignment.child_id,
            child_name: child?.users?.name || 'Unknown Child'
          });
        }
      }

      console.log('Formatted missions:', formattedMissions);
      setMissions(formattedMissions);

      // If no missions found, show a message
      if (formattedMissions.length === 0) {
        console.log('No missions found for this parent');
      }
    } catch (err) {
      console.error('Error loading missions:', err);
    }
  };

  // Handle creating a new mission
  const handleCreateMission = async () => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to create a mission');
      return;
    }

    if (!selectedChild) {
      Alert.alert('Error', 'Please select a child for this mission');
      return;
    }

    if (!newMission.title.trim()) {
      Alert.alert('Error', 'Please enter a mission title');
      return;
    }

    try {
      setIsLoading(true);

      // Create the mission in the database
      console.log('Creating mission for parent:', user.id);
      const missionData = {
        title: newMission.title,
        description: newMission.description,
        reward: newMission.reward_points, // Note: the database field is 'reward', not 'reward_points'
        due_date: newMission.due_date.toISOString()
      };

      // Step 1: Create the mission
      const createdMission = await createMission(user.id, missionData);
      console.log('Mission created:', createdMission);

      // Step 2: Assign the mission to the child
      const assignment = await assignMissionToChild(createdMission.id, selectedChild.child_id);
      console.log('Mission assigned:', assignment);

      // Add to local state
      const newMissionWithChild: Mission = {
        ...createdMission,
        reward_points: parseInt(newMission.reward_points),
        status: 'pending',
        child_id: selectedChild.child_id,
        child_name: selectedChild.users.name
      };

      setMissions([...missions, newMissionWithChild]);

      // Reset form and close modal
      setNewMission({
        title: '',
        description: '',
        reward_points: '10',
        due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      });
      setSelectedChild(null);
      setModalVisible(false);

      Alert.alert('Success', 'Mission created and assigned successfully!');
    } catch (err) {
      console.error('Error creating mission:', err);
      Alert.alert('Error', 'Failed to create mission. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle date change in date picker
  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setNewMission({ ...newMission, due_date: selectedDate });
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle verifying a mission
  const handleVerifyMission = async (mission: Mission) => {
    try {
      setIsLoading(true);

      // Find the mission assignment ID
      const { data: assignments, error: assignmentError } = await supabase
        .from('mission_assignments')
        .select('id')
        .eq('mission_id', mission.id)
        .eq('child_id', mission.child_id)
        .eq('status', 'completed')
        .single();

      if (assignmentError || !assignments) {
        console.error('Error finding mission assignment:', assignmentError);
        Alert.alert('Error', 'Could not find the mission assignment. Please try again.');
        setIsLoading(false);
        return;
      }

      // Update the mission status to verified
      const { data: updatedAssignment, error: updateError } = await supabase
        .from('mission_assignments')
        .update({ status: 'verified' })
        .eq('id', assignments.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating mission status:', updateError);
        Alert.alert('Error', 'Failed to verify mission. Please try again.');
        setIsLoading(false);
        return;
      }

      console.log('Mission verified successfully:', updatedAssignment);

      // Update the local state
      setMissions(prev => prev.map(m =>
        m.id === mission.id ? { ...m, status: 'verified' } : m
      ));

      // Send a notification to the child
      try {
        // Create and send a local notification
        await createMissionVerificationNotification(mission.title, mission.reward_points);

        // Send a push notification to the child's device
        if (mission.child_id) {
          await sendNotificationToChild(
            mission.child_id,
            'Mission Verified! 🎉',
            `Your mission "${mission.title}" has been verified! You earned ${mission.reward_points} stars!`,
            {
              type: 'mission_verified',
              missionId: mission.id,
              title: mission.title,
              reward: mission.reward_points.toString()
            }
          );
        }
      } catch (notifError) {
        console.error('Error sending notification:', notifError);
        // Continue even if notification fails
      }

      Alert.alert('Success', `Mission "${mission.title}" has been verified!`);
    } catch (err) {
      console.error('Error verifying mission:', err);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Get status color
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'pending':
        return '#f39c12';
      case 'in_progress':
        return '#3498db';
      case 'completed':
        return '#2ecc71';
      case 'verified':
        return '#9b59b6';
      default:
        return '#95a5a6';
    }
  };

  // Get status icon
  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'pending':
        return 'clock';
      case 'in_progress':
        return 'spinner';
      case 'completed':
        return 'check-circle';
      case 'verified':
        return 'star';
      default:
        return 'question-circle';
    }
  };

  // Handle deleting a mission
  const handleDeleteMission = async (mission: Mission) => {
    if (!user) return;

    Alert.alert(
      'Delete Mission',
      `Are you sure you want to delete the mission "${mission.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              await deleteMission(mission.id, user.id);

              // Update local state
              setMissions(prev => prev.filter(m => m.id !== mission.id));

              Alert.alert('Success', 'Mission deleted successfully');
            } catch (err) {
              console.error('Error deleting mission:', err);
              Alert.alert('Error', 'Failed to delete mission. Please try again.');
            } finally {
              setIsLoading(false);
            }
          }
        }
      ]
    );
  };

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <View style={styles.container}>
      <Header title="Missions" />

      {children.length === 0 ? (
        <View style={styles.emptyContainer}>
          <FontAwesome5 name="tasks" size={50} color="#ccc" />
          <Text style={styles.emptyText}>
            No children registered yet
          </Text>
          <Text style={styles.emptySubtext}>
            Add children in the Children tab to assign missions
          </Text>
        </View>
      ) : (
        <>
          <ScrollView style={styles.scrollView}>
            {/* Reward Manager */}
            {user && <RewardManager parentId={user.id} />}

            {missions.length === 0 ? (
              <View style={styles.emptyContainer}>
                <FontAwesome5 name="tasks" size={50} color="#ccc" />
                <Text style={styles.emptyText}>No Missions Yet</Text>
                <Text style={styles.emptySubtext}>Create your first mission using the button below</Text>
              </View>
            ) : (
              <View style={styles.missionList}>
                {missions.map((mission) => (
                  <TouchableOpacity
                    key={mission.id}
                    style={styles.missionCard}
                    onPress={() => {
                      if (mission.status === 'completed') {
                        Alert.alert(
                          'Verify Mission',
                          `Do you want to verify that ${mission.child_name} has completed the mission "${mission.title}"?`,
                          [
                            { text: 'Cancel', style: 'cancel' },
                            {
                              text: 'Verify',
                              style: 'default',
                              onPress: () => handleVerifyMission(mission)
                            }
                          ]
                        );
                      } else {
                        Alert.alert(
                          mission.title,
                          mission.description,
                          [
                            { text: 'Close', style: 'cancel' },
                            {
                              text: 'View Details',
                              onPress: () => Alert.alert('Coming Soon', 'Mission details will be available soon!')
                            }
                          ]
                        );
                      }
                    }}
                  >
                    <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(mission.status) }]} />
                    <View style={styles.missionContent}>
                      <View style={styles.missionHeader}>
                        <Text style={styles.missionTitle}>{mission.title}</Text>
                        <View style={styles.rewardContainer}>
                          <FontAwesome5 name="star" size={14} color="#f1c40f" />
                          <Text style={styles.rewardText}>{mission.reward_points}</Text>
                        </View>
                      </View>

                      <Text style={styles.missionDescription} numberOfLines={2}>
                        {mission.description}
                      </Text>

                      <View style={styles.missionFooter}>
                        <View style={styles.statusContainer}>
                          <FontAwesome5
                            name={getStatusIcon(mission.status)}
                            size={12}
                            color={getStatusColor(mission.status)}
                          />
                          <Text style={[styles.statusText, { color: getStatusColor(mission.status) }]}>
                            {mission.status?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </Text>
                        </View>

                        <View style={styles.childContainer}>
                          <FontAwesome5 name="child" size={12} color="#666" />
                          <Text style={styles.childText}>{mission.child_name}</Text>
                        </View>

                        {mission.due_date && (
                          <Text style={styles.dueDate}>
                            Due: {new Date(mission.due_date).toLocaleDateString()}
                          </Text>
                        )}

                        <TouchableOpacity
                          style={styles.deleteButton}
                          onPress={(e) => {
                            e.stopPropagation(); // Prevent triggering the parent TouchableOpacity
                            handleDeleteMission(mission);
                          }}
                        >
                          <FontAwesome5 name="trash" size={12} color="#E74C3C" />
                        </TouchableOpacity>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </ScrollView>

          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setModalVisible(true)}
          >
            <FontAwesome5 name="plus" size={20} color="#fff" />
          </TouchableOpacity>
        </>
      )}

      {/* Create Mission Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Create New Mission</Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <FontAwesome5 name="times" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              <Text style={styles.inputLabel}>Mission Title</Text>
              <TextInput
                style={styles.input}
                value={newMission.title}
                onChangeText={(text) => setNewMission({ ...newMission, title: text })}
                placeholder="Enter mission title"
              />

              <Text style={styles.inputLabel}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={newMission.description}
                onChangeText={(text) => setNewMission({ ...newMission, description: text })}
                placeholder="Enter mission description"
                multiline
                numberOfLines={4}
              />

              <Text style={styles.inputLabel}>Reward Points</Text>
              <TextInput
                style={styles.input}
                value={newMission.reward_points}
                onChangeText={(text) => setNewMission({ ...newMission, reward_points: text.replace(/[^0-9]/g, '') })}
                placeholder="Enter reward points"
                keyboardType="numeric"
              />

              <Text style={styles.inputLabel}>Due Date</Text>
              <TouchableOpacity
                style={styles.datePickerButton}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={styles.dateText}>{formatDate(newMission.due_date)}</Text>
                <FontAwesome5 name="calendar-alt" size={16} color="#4630EB" />
              </TouchableOpacity>

              {showDatePicker && DateTimePicker && (
                <DateTimePicker
                  value={newMission.due_date}
                  mode="date"
                  display="default"
                  onChange={handleDateChange}
                  minimumDate={new Date()}
                />
              )}

              <Text style={styles.inputLabel}>Assign To</Text>
              <TouchableOpacity
                style={styles.childPickerButton}
                onPress={() => setChildSelectVisible(true)}
              >
                <Text style={styles.childPickerText}>
                  {selectedChild ? selectedChild.users.name : 'Select a child'}
                </Text>
                <FontAwesome5 name="chevron-down" size={16} color="#4630EB" />
              </TouchableOpacity>
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.createButton]}
                onPress={handleCreateMission}
              >
                <Text style={styles.createButtonText}>Create Mission</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Child Selection Modal */}
      <Modal
        visible={childSelectVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setChildSelectVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, styles.childSelectModal]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Child</Text>
              <TouchableOpacity onPress={() => setChildSelectVisible(false)}>
                <FontAwesome5 name="times" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              {children.map((child) => (
                <TouchableOpacity
                  key={child.child_id}
                  style={styles.childOption}
                  onPress={() => {
                    setSelectedChild(child);
                    setChildSelectVisible(false);
                  }}
                >
                  <View style={styles.childIconContainer}>
                    <FontAwesome5 name="child" size={20} color="#4630EB" />
                  </View>
                  <Text style={styles.childOptionText}>{child.users.name}</Text>
                  {selectedChild?.child_id === child.child_id && (
                    <FontAwesome5 name="check" size={16} color="#4630EB" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    marginTop: 100,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtext: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
    marginBottom: 20,
  },
  missionList: {
    width: '100%',
  },
  missionCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    overflow: 'hidden',
  },
  statusIndicator: {
    width: 6,
    height: '100%',
  },
  missionContent: {
    flex: 1,
    padding: 16,
  },
  missionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  missionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  rewardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff9e6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  rewardText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#f1c40f',
    marginLeft: 4,
  },
  missionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  missionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    marginLeft: 4,
  },
  childContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  childText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  dueDate: {
    fontSize: 12,
    color: '#95a5a6',
  },
  deleteButton: {
    padding: 8,
    borderRadius: 4,
    marginLeft: 'auto',
  },
  addButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#4630EB',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  childSelectModal: {
    maxHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalContent: {
    padding: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  childPickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  childPickerText: {
    fontSize: 16,
    color: '#333',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#666',
    fontWeight: 'bold',
  },
  createButton: {
    backgroundColor: '#4630EB',
    marginLeft: 8,
  },
  createButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  childOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  childIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  childOptionText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
});
