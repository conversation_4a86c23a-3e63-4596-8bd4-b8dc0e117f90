import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../contexts/AuthContext';
import { useLocation } from '../../contexts/LocationContext';
import usePermissionRequest from '../../hooks/usePermissionRequest';
import PermissionExplainer from '../../components/common/PermissionExplainer';
import PermissionRequestScreen from '../../components/child/PermissionRequestScreen';

export default function ChildMainScreen() {
  const { user } = useAuth();
  const { currentLocation, startTracking } = useLocation();
  const navigation = useNavigation();
  const [showPermissionScreen, setShowPermissionScreen] = useState(false);
  
  // Set up location permission handling
  const {
    permissionStatus: locationStatus,
    showPermissionExplainer: showLocationExplainer,
    requestPermission: requestLocationPermission,
    dismissExplainer: dismissLocationExplainer,
  } = usePermissionRequest('location', 'child');
  
  // Set up background location permission handling
  const {
    permissionStatus: backgroundLocationStatus,
    showPermissionExplainer: showBackgroundLocationExplainer,
    requestPermission: requestBackgroundLocationPermission,
    dismissExplainer: dismissBackgroundLocationExplainer,
  } = usePermissionRequest('backgroundLocation', 'child', {
    showExplainer: locationStatus === 'granted'
  });
  
  // Set up notifications permission handling
  const {
    permissionStatus: notificationsStatus,
    showPermissionExplainer: showNotificationsExplainer,
    requestPermission: requestNotificationsPermission,
    dismissExplainer: dismissNotificationsExplainer,
  } = usePermissionRequest('notifications', 'child', {
    showExplainer: locationStatus === 'granted' && backgroundLocationStatus === 'granted'
  });

  // Check if all required permissions are granted
  const allPermissionsGranted = 
    locationStatus === 'granted' && 
    backgroundLocationStatus === 'granted' && 
    notificationsStatus === 'granted';

  // Check if we should show the full permission screen
  useEffect(() => {
    if (locationStatus === 'denied' || backgroundLocationStatus === 'denied') {
      setShowPermissionScreen(true);
    } else {
      setShowPermissionScreen(false);
    }
  }, [locationStatus, backgroundLocationStatus]);

  // Start location tracking when all permissions are granted
  useEffect(() => {
    if (allPermissionsGranted && user) {
      const initializeTracking = async () => {
        try {
          await startTracking();
          console.log('Location tracking started successfully');
        } catch (error) {
          console.error('Failed to start location tracking:', error);
          Alert.alert(
            'Errore',
            'Non è stato possibile avviare il monitoraggio della posizione. Riprova più tardi.',
            [{ text: 'OK' }]
          );
        }
      };
      
      initializeTracking();
    }
  }, [allPermissionsGranted, user, startTracking]);

  // Main content
  if (showPermissionScreen) {
    return <PermissionRequestScreen />;
  }

  return (
    <View style={styles.container}>
      {/* Child app main content would go here */}
      
      {/* Permission explainers */}
      <PermissionExplainer
        visible={showLocationExplainer}
        onRequestPermission={requestLocationPermission}
        onDismiss={dismissLocationExplainer}
        permissionType="location"
        userType="child"
      />
      
      <PermissionExplainer
        visible={showBackgroundLocationExplainer}
        onRequestPermission={requestBackgroundLocationPermission}
        onDismiss={dismissBackgroundLocationExplainer}
        permissionType="backgroundLocation"
        userType="child"
      />
      
      <PermissionExplainer
        visible={showNotificationsExplainer}
        onRequestPermission={requestNotificationsPermission}
        onDismiss={dismissNotificationsExplainer}
        permissionType="notifications"
        userType="child"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
}); 