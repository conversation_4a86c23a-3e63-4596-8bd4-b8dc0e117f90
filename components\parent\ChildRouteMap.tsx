import React, { useState, useEffect, useRef, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, ScrollView } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import GoogleMapView from '../shared/GoogleMapView';
import { <PERSON><PERSON>, <PERSON>yline } from 'react-native-maps';
import { getChildRoutes, getChildRoutesForDate, getChildRoutesForToday } from '../../utils/supabase';
import { formatDistanceToNow } from 'date-fns';
import { it } from 'date-fns/locale';

interface ChildRouteMapProps {
  childId: string;
  childName: string;
  date?: Date; // Se non specificata, mostra i tragitti di oggi
  onClose?: () => void;
}

interface RoutePoint {
  id: string;
  latitude: number;
  longitude: number;
  timestamp: string;
  accuracy?: number;
  battery_level?: number;
}

interface Route {
  id: string;
  child_id: string;
  route_date: string;
  start_time: string;
  end_time: string;
  distance_meters: number;
  created_at: string;
  updated_at: string;
}

const ChildRouteMap: React.FC<ChildRouteMapProps> = ({ childId, childName, date, onClose }) => {
  const [routes, setRoutes] = useState<Route[]>([]);
  const [routePoints, setRoutePoints] = useState<{ [routeId: string]: RoutePoint[] }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRoute, setSelectedRoute] = useState<string | null>(null);
  const mapRef = useRef<any>(null);

  // Colori per i diversi tragitti
  const routeColors = [
    '#FF5733', // Rosso-arancio
    '#33FF57', // Verde chiaro
    '#3357FF', // Blu
    '#FF33F5', // Rosa
    '#33FFF5', // Ciano
    '#F5FF33', // Giallo
    '#FF8333', // Arancione
    '#8333FF', // Viola
    '#33FF83', // Verde acqua
    '#FF3333', // Rosso
  ];

  useEffect(() => {
    fetchRoutes();
  }, [childId, date]);

  const fetchRoutes = async () => {
    try {
      setIsLoading(true);
      setError(null);

      let routesData;
      if (date) {
        routesData = await getChildRoutesForDate(childId, date);
      } else {
        routesData = await getChildRoutesForToday(childId);
      }

      if (routesData) {
        setRoutes(routesData.routes || []);
        setRoutePoints(routesData.points || {});

        // Seleziona automaticamente il primo tragitto se ce n'è uno
        if (routesData.routes && routesData.routes.length > 0) {
          setSelectedRoute(routesData.routes[0].id);
        }

        // Centra la mappa sul primo punto dell'ultimo tragitto
        if (routesData.routes && routesData.routes.length > 0 && routesData.points) {
          const lastRouteId = routesData.routes[0].id;
          const points = routesData.points[lastRouteId];

          if (points && points.length > 0 && mapRef.current) {
            const firstPoint = points[0];
            mapRef.current.animateToRegion({
              latitude: firstPoint.latitude,
              longitude: firstPoint.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            });
          }
        }
      }
    } catch (err: any) {
      console.error('Error fetching routes:', err);
      setError(err.message || 'Failed to load routes');
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' });
  };

  const formatDistance = (meters: number) => {
    if (meters < 1000) {
      return `${meters.toFixed(0)} m`;
    } else {
      return `${(meters / 1000).toFixed(2)} km`;
    }
  };

  const formatDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime || new Date());
    const durationMs = end.getTime() - start.getTime();
    const minutes = Math.floor(durationMs / 60000);

    if (minutes < 60) {
      return `${minutes} min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours} h ${remainingMinutes} min`;
    }
  };

  const getRouteColor = (index: number) => {
    return routeColors[index % routeColors.length];
  };

  // Seleziona i punti del tragitto selezionato
  const selectedRoutePoints = useMemo(() => {
    if (!selectedRoute) return [];

    const points = routePoints[selectedRoute] || [];

    // Filtra i punti per assicurarsi che abbiano coordinate valide
    return points.filter(point =>
      point &&
      typeof point.latitude === 'number' &&
      typeof point.longitude === 'number' &&
      !isNaN(point.latitude) &&
      !isNaN(point.longitude)
    );
  }, [selectedRoute, routePoints]);

  // Log dei punti del tragitto selezionato per debug
  useEffect(() => {
    if (selectedRoutePoints.length > 0) {
      console.log('Selected route points count:', selectedRoutePoints.length);
      console.log('First point:', selectedRoutePoints[0]);
      if (selectedRoutePoints.length > 1) {
        console.log('Last point:', selectedRoutePoints[selectedRoutePoints.length - 1]);
      }
    } else {
      console.log('No selected route points');
    }
  }, [selectedRoutePoints]);

  const handleRouteSelect = (routeId: string) => {
    setSelectedRoute(routeId);

    // Centra la mappa sul primo punto del tragitto selezionato
    const points = routePoints[routeId];
    if (points && points.length > 0 && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: points[0].latitude,
        longitude: points[0].longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4630EB" />
        <Text style={styles.loadingText}>Caricamento tragitti...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <FontAwesome5 name="exclamation-circle" size={50} color="#FF3B30" />
        <Text style={styles.errorText}>Errore: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchRoutes}>
          <Text style={styles.retryButtonText}>Riprova</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (routes.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Tragitti di {childName}</Text>
          {onClose && (
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <FontAwesome5 name="times" size={20} color="#333" />
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.emptyContainer}>
          <FontAwesome5 name="route" size={50} color="#ccc" />
          <Text style={styles.emptyText}>Nessun tragitto trovato</Text>
          <Text style={styles.emptySubtext}>
            {date ? `Non ci sono tragitti per il ${date.toLocaleDateString('it-IT')}` : 'Non ci sono tragitti per oggi'}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Tragitti di {childName}</Text>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <FontAwesome5 name="times" size={20} color="#333" />
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.mapContainer}>
        <GoogleMapView
          ref={mapRef}
          style={styles.map}
          initialRegion={{
            latitude: selectedRoutePoints.length > 0 ? selectedRoutePoints[0].latitude : 41.9028,
            longitude: selectedRoutePoints.length > 0 ? selectedRoutePoints[0].longitude : 12.4964,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }}
        >
          {/* Marker per tutti i punti di inizio e fine dei tragitti */}
          {routes.flatMap((route, index) => {
            const points = routePoints[route.id] || [];
            if (points.length === 0) return null;

            const markers = [];

            // Marker per l'inizio del tragitto
            if (points[0] && typeof points[0].latitude === 'number' && typeof points[0].longitude === 'number') {
              markers.push(
                <Marker
                  key={`start-${route.id}`}
                  coordinate={{
                    latitude: points[0].latitude,
                    longitude: points[0].longitude,
                  }}
                  title={`Inizio: ${formatTime(points[0].timestamp)}`}
                >
                  <View style={[styles.routeMarker, { backgroundColor: getRouteColor(index) }]}>
                    <FontAwesome5 name="play" size={12} color="#fff" />
                  </View>
                </Marker>
              );
            }

            // Marker per la fine del tragitto
            if (points.length > 1) {
              const lastPoint = points[points.length - 1];
              if (lastPoint && typeof lastPoint.latitude === 'number' && typeof lastPoint.longitude === 'number') {
                markers.push(
                  <Marker
                    key={`end-${route.id}`}
                    coordinate={{
                      latitude: lastPoint.latitude,
                      longitude: lastPoint.longitude,
                    }}
                    title={`Fine: ${formatTime(lastPoint.timestamp)}`}
                  >
                    <View style={[styles.routeMarker, { backgroundColor: getRouteColor(index) }]}>
                      <FontAwesome5 name="stop" size={12} color="#fff" />
                    </View>
                  </Marker>
                );
              }
            }

            return markers;
          })}

          {/* Polylines per i tragitti */}
          {routes.map((route, index) => {
            const points = routePoints[route.id] || [];
            if (points.length < 2) return null;

            return (
              <Polyline
                key={`route-${route.id}`}
                coordinates={points.map(point => ({
                  latitude: point.latitude,
                  longitude: point.longitude,
                }))}
                strokeWidth={4}
                strokeColor={getRouteColor(index)}
              />
            );
          })}
        </GoogleMapView>
      </View>

      <View style={styles.routesListContainer}>
        <Text style={styles.routesListTitle}>Tragitti</Text>
        <ScrollView style={styles.routesList}>
          {routes.map((route, index) => (
            <TouchableOpacity
              key={route.id}
              style={[
                styles.routeItem,
                selectedRoute === route.id && styles.selectedRouteItem
              ]}
              onPress={() => handleRouteSelect(route.id)}
            >
              <View style={[styles.routeColorIndicator, { backgroundColor: getRouteColor(index) }]} />
              <View style={styles.routeInfo}>
                <Text style={styles.routeTime}>
                  {formatTime(route.start_time)} - {route.end_time ? formatTime(route.end_time) : 'In corso'}
                </Text>
                <Text style={styles.routeDetails}>
                  {formatDistance(route.distance_meters)} • {formatDuration(route.start_time, route.end_time)}
                </Text>
              </View>
              <FontAwesome5
                name="chevron-right"
                size={16}
                color={selectedRoute === route.id ? '#4630EB' : '#ccc'}
              />
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  mapContainer: {
    flex: 1,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  routesListContainer: {
    backgroundColor: '#fff',
    padding: 16,
    maxHeight: 200,
  },
  routesList: {
    flexGrow: 0,
  },
  routesListTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  routeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  selectedRouteItem: {
    backgroundColor: '#f0f0ff',
  },
  routeColorIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  routeInfo: {
    flex: 1,
  },
  routeTime: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  routeDetails: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 20,
    backgroundColor: '#4630EB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  emptySubtext: {
    marginTop: 8,
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  callout: {
    width: 200,
    padding: 8,
  },
  calloutTitle: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  routeMarker: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
});

export default ChildRouteMap;
