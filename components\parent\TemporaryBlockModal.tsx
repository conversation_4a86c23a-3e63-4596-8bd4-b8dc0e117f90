import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { setTemporaryBlock, removeTemporaryBlock } from '../../utils/supabase';

interface TemporaryBlockModalProps {
  visible: boolean;
  onClose: () => void;
  childId: string;
  appInfo: {
    displayName: string;
    packageName: string;
  };
  onBlockSet: (appInfo: { displayName: string, packageName: string }, blockMinutes: number | null) => void;
}

export default function TemporaryBlockModal({
  visible,
  onClose,
  childId,
  appInfo,
  onBlockSet,
}: TemporaryBlockModalProps) {
  const [blockMinutes, setBlockMinutes] = useState('30');
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!blockMinutes.trim()) {
      Alert.alert('Errore', 'Inserisci un tempo di blocco');
      return;
    }

    const minutes = parseInt(blockMinutes.trim(), 10);
    if (isNaN(minutes) || minutes <= 0) {
      Alert.alert('Errore', 'Inserisci un tempo di blocco valido (maggiore di 0)');
      return;
    }

    setIsLoading(true);
    try {
      // Calcola la data di fine del blocco
      const endTime = new Date();
      endTime.setMinutes(endTime.getMinutes() + minutes);

      // Blocca temporaneamente l'app
      await setTemporaryBlock(childId, appInfo.packageName, endTime.toISOString());
      onBlockSet(appInfo, minutes);
      onClose();

      Alert.alert(
        'App Bloccata Temporaneamente',
        `${appInfo.displayName} è stata bloccata per ${minutes} minuti. Si sbloccherà automaticamente alle ${endTime.toLocaleTimeString()}.`
      );
    } catch (error) {
      console.error('Error setting temporary block:', error);
      Alert.alert('Errore', 'Impossibile impostare il blocco temporaneo');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Blocco Temporaneo</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <FontAwesome5 name="times" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <Text style={styles.appName}>{appInfo.displayName}</Text>

            <View style={styles.appIconContainer}>
              <View style={styles.appIconBackground}>
                <FontAwesome5 name="lock-clock" size={24} color="#F44336" />
              </View>
            </View>

            {/* Sezione centrale con il timer */}
            <View style={styles.timerSection}>
              <Text style={styles.label}>Blocca temporaneamente per</Text>

              <View style={styles.quickTimeButtons}>
                {[15, 30, 60, 120].map(minutes => (
                  <TouchableOpacity
                    key={minutes}
                    style={[
                      styles.quickTimeButton,
                      parseInt(blockMinutes) === minutes ? styles.quickTimeButtonSelected : {}
                    ]}
                    onPress={() => setBlockMinutes(minutes.toString())}
                  >
                    <Text
                      style={[
                        styles.quickTimeButtonText,
                        parseInt(blockMinutes) === minutes ? styles.quickTimeButtonTextSelected : {}
                      ]}
                    >
                      {minutes} min
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <View style={styles.timeInputContainer}>
                <TextInput
                  style={styles.input}
                  value={blockMinutes}
                  onChangeText={setBlockMinutes}
                  keyboardType="number-pad"
                  placeholder="Inserisci minuti"
                  placeholderTextColor="#999"
                />
                <Text style={styles.minutesLabel}>minuti</Text>
              </View>
            </View>

            {blockMinutes && !isNaN(parseInt(blockMinutes)) && (
              <View style={styles.unlockTimeContainer}>
                <FontAwesome5 name="unlock" size={14} color="#4CAF50" style={{ marginRight: 6 }} />
                <Text style={styles.unlockTimeText}>
                  Si sbloccherà automaticamente alle{' '}
                  <Text style={styles.unlockTimeHighlight}>
                    {(() => {
                      const endTime = new Date();
                      endTime.setMinutes(endTime.getMinutes() + parseInt(blockMinutes));
                      return endTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                    })()}
                  </Text>
                </Text>
              </View>
            )}

            <Text style={styles.helpText}>
              L'app verrà bloccata <Text style={styles.boldText}>immediatamente</Text> e si sbloccherà automaticamente dopo il tempo impostato.
            </Text>
          </View>

          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onClose}
              disabled={isLoading}
            >
              <Text style={styles.cancelButtonText}>Annulla</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.blockButton}
              onPress={handleSave}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <>
                  <FontAwesome5 name="lock" size={16} color="#FFFFFF" style={{ marginRight: 8 }} />
                  <Text style={styles.blockButtonText}>Blocca</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '85%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
  },
  appName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#F44336',
    marginBottom: 16,
    textAlign: 'center',
  },
  appIconContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  appIconBackground: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#FFEBEE',
    justifyContent: 'center',
    alignItems: 'center',
  },
  timerSection: {
    backgroundColor: '#FFF8E1',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FFE0B2',
  },
  label: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF9800',
    marginBottom: 16,
    textAlign: 'center',
  },
  timeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    width: '100%',
  },
  input: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: '#FFB74D',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    color: '#333333',
    backgroundColor: '#FFFFFF',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  minutesLabel: {
    fontSize: 16,
    color: '#FF9800',
    marginLeft: 8,
    width: 60,
    fontWeight: 'bold',
  },
  quickTimeButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    width: '100%',
  },
  quickTimeButton: {
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#FFB74D',
    backgroundColor: '#FFFFFF',
  },
  quickTimeButtonSelected: {
    backgroundColor: '#FF9800',
    borderColor: '#F57C00',
  },
  quickTimeButtonText: {
    fontSize: 14,
    color: '#FF9800',
    fontWeight: '500',
  },
  quickTimeButtonTextSelected: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  helpText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 20,
  },
  boldText: {
    fontWeight: 'bold',
    color: '#F44336',
  },
  unlockTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E9',
    padding: 12,
    borderRadius: 8,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  unlockTimeText: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
  },
  unlockTimeHighlight: {
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  blockButton: {
    backgroundColor: '#F44336',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  blockButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#DDDDDD',
  },
  cancelButtonText: {
    color: '#666666',
    fontWeight: 'bold',
    fontSize: 16,
  },
});
