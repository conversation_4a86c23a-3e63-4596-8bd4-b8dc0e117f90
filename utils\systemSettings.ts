import { Platform, Linking, Alert } from 'react-native';
import { getDeviceUIType } from './deviceInfo';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Application from 'expo-application';
import {
  openBatteryOptimizationSettingsNative,
  openNotificationListenerSettingsNative,
  openLocationSettingsNative,
  isBatteryOptimizationDisabledNative
} from './systemSettingsNative';

/**
 * Apre le impostazioni di sistema per la posizione in background
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
export const openBackgroundLocationSettings = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    try {
      console.log('[SystemSettings] Opening iOS settings for location');
      await Linking.openURL('app-settings:location');
      return true;
    } catch (error) {
      console.error('[SystemSettings] Error opening iOS location settings:', error);
      try {
        // Fallback to general settings
        await Linking.openURL('app-settings:');
        return true;
      } catch (fallbackError) {
        console.error('[SystemSettings] Error opening general iOS settings:', fallbackError);
        return false;
      }
    }
  }

  try {
    // Apriamo direttamente le impostazioni di geolocalizzazione del dispositivo
    console.log('[SystemSettings] Opening device location settings directly');

    // Mostriamo un messaggio all'utente per spiegare cosa deve fare
    Alert.alert(
      'Autorizzazione posizione in background',
      'Per permettere all\'app di monitorare la tua posizione anche quando non è in uso, devi concedere l\'autorizzazione "Consenti sempre". Ti porteremo alle impostazioni di geolocalizzazione del dispositivo.',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Apri Impostazioni',
          onPress: async () => {
            try {
              // Prima proviamo con il modulo nativo
              console.log('[SystemSettings] Trying native module for location settings');
              const nativeResult = await openLocationSettingsNative();

              if (!nativeResult) {
                // Se il modulo nativo fallisce, proviamo con IntentLauncher
                console.log('[SystemSettings] Native module failed, trying IntentLauncher');

                // Apriamo direttamente le impostazioni di geolocalizzazione del dispositivo
                await IntentLauncher.startActivityAsync(
                  IntentLauncher.ActivityAction.LOCATION_SOURCE_SETTINGS
                );
              }
            } catch (error) {
              console.error('[SystemSettings] Error opening location settings:', error);

              try {
                // Come fallback, proviamo con le impostazioni generali
                console.log('[SystemSettings] Trying general settings');
                await Linking.openSettings();
              } catch (fallbackError) {
                console.error('[SystemSettings] Error opening general settings:', fallbackError);
                Alert.alert(
                  'Errore',
                  'Non è stato possibile aprire le impostazioni di geolocalizzazione. Prova ad aprirle manualmente dalle impostazioni del dispositivo.'
                );
              }
            }
          }
        }
      ]
    );
    return true;
  } catch (error) {
    console.error('[SystemSettings] Error in openBackgroundLocationSettings:', error);
    return false;
  }
};

/**
 * Apre le impostazioni di sistema per l'ottimizzazione della batteria
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
/**
 * Apre le impostazioni di sistema appropriate in base al tipo di autorizzazione
 * @param permissionType - Il tipo di autorizzazione per cui aprire le impostazioni
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
export const openSystemSettings = async (permissionType: string): Promise<boolean> => {
  console.log(`[SystemSettings] Opening system settings for ${permissionType}`);

  switch (permissionType) {
    case 'location':
    case 'backgroundLocation':
      return openBackgroundLocationSettings();
    case 'notifications':
      return openNotificationSettings();
    case 'appMonitoring':
    case 'usageStats':
      return openUsageAccessSettings();
    case 'batteryOptimization':
      return openBatteryOptimizationSettings();
    case 'accessibility':
      return openAccessibilitySettings();
    default:
      // Se il tipo di autorizzazione non è specificato, apriamo le impostazioni generali
      try {
        await Linking.openSettings();
        return true;
      } catch (error) {
        console.error('[SystemSettings] Error opening general settings:', error);
        return false;
      }
  }
};

export const openBatteryOptimizationSettings = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    try {
      await Linking.openURL('app-settings:');
      return true;
    } catch (error) {
      console.error('Error opening iOS settings:', error);
      return false;
    }
  }

  try {
    // Su Android, utilizziamo il modulo nativo migliorato per aprire direttamente le impostazioni di ottimizzazione della batteria
    const deviceType = getDeviceUIType();
    console.log('[SystemSettings] Device type for battery settings:', deviceType);

    // Verifichiamo prima se l'ottimizzazione della batteria è già disabilitata
    const isDisabled = await isBatteryOptimizationDisabledNative();
    console.log('[SystemSettings] Battery optimization already disabled:', isDisabled);

    // Mostriamo un messaggio all'utente per spiegare cosa deve fare
    Alert.alert(
      'Ottimizzazione della batteria',
      isDisabled
        ? 'L\'ottimizzazione della batteria sembra essere già disabilitata, ma potrebbe essere necessario verificare. Ti porteremo alle impostazioni di sistema per controllare.'
        : 'Per permettere all\'app di funzionare correttamente in background, devi disattivare l\'ottimizzazione della batteria per KidSafety. Ti porteremo alle impostazioni di sistema, dove dovrai trovare e selezionare KidSafety nell\'elenco e poi selezionare "Non ottimizzare".',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Apri Impostazioni',
          onPress: async () => {
            try {
              // Utilizziamo il modulo nativo migliorato
              console.log('[SystemSettings] Opening battery optimization settings with improved native module');
              const success = await openBatteryOptimizationSettingsNative();

              if (!success) {
                // Se il modulo nativo fallisce, proviamo con gli intent standard
                console.log('[SystemSettings] Native module failed, trying standard intents');

                // Proviamo con un intent specifico per aprire direttamente la schermata di ottimizzazione della batteria
                console.log('[SystemSettings] Trying direct battery optimization intent');

                // Utilizziamo un intent specifico che dovrebbe aprire direttamente la schermata di ottimizzazione della batteria
                await IntentLauncher.startActivityAsync(
                  'android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS',
                  { data: 'package:' + Application.applicationId }
                );
              }

              // Verifichiamo lo stato dell'ottimizzazione della batteria dopo un breve ritardo
              setTimeout(async () => {
                try {
                  const newStatus = await isBatteryOptimizationDisabledNative();
                  console.log('[SystemSettings] Battery optimization status after settings:', newStatus);
                } catch (checkError) {
                  console.error('[SystemSettings] Error checking battery optimization status after settings:', checkError);
                }
              }, 3000);

            } catch (error1) {
              console.log('[SystemSettings] Direct battery optimization intent failed, trying general settings', error1);

              try {
                // Proviamo con le impostazioni generali di ottimizzazione della batteria
                await IntentLauncher.startActivityAsync(
                  'android.settings.IGNORE_BATTERY_OPTIMIZATION_SETTINGS'
                );
              } catch (error2) {
                console.log('[SystemSettings] General battery optimization settings failed, trying power usage summary', error2);

                try {
                  // Proviamo con le impostazioni di utilizzo energetico
                  await IntentLauncher.startActivityAsync(
                    'android.settings.POWER_USAGE_SUMMARY'
                  );
                } catch (error3) {
                  console.log('[SystemSettings] Power usage summary failed, trying battery saver settings', error3);

                  try {
                    // Proviamo con le impostazioni del risparmio energetico
                    await IntentLauncher.startActivityAsync(
                      IntentLauncher.ActivityAction.BATTERY_SAVER_SETTINGS
                    );
                  } catch (error4) {
                    console.log('[SystemSettings] Battery saver settings failed, trying app details', error4);

                    try {
                      // Proviamo con le impostazioni dell'app
                      await IntentLauncher.startActivityAsync(
                        IntentLauncher.ActivityAction.APPLICATION_DETAILS_SETTINGS,
                        { data: 'package:' + Application.applicationId }
                      );
                    } catch (error5) {
                      console.log('[SystemSettings] App details failed, trying general settings', error5);

                      // Come ultima risorsa, proviamo con le impostazioni generali
                      await Linking.openSettings();
                    }
                  }
                }
              }
            }
          }
        }
      ]
    );
    return true;
  } catch (error) {
    console.error('[SystemSettings] Error opening battery settings:', error);
    return false;
  }
};

/**
 * Apre le impostazioni di sistema per l'accessibilità
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
/**
 * Apre le impostazioni di sistema per l'accesso ai dati di utilizzo
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
export const openUsageAccessSettings = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    try {
      await Linking.openURL('app-settings:');
      return true;
    } catch (error) {
      console.error('Error opening iOS settings:', error);
      return false;
    }
  }

  try {
    // Su Android, utilizziamo IntentLauncher per aprire direttamente le impostazioni di accesso ai dati di utilizzo
    const deviceType = getDeviceUIType();
    console.log('[SystemSettings] Device type for usage access settings:', deviceType);

    // Mostriamo un messaggio all'utente per spiegare cosa deve fare
    Alert.alert(
      'Accesso ai dati di utilizzo',
      'Per permettere all\'app di monitorare quali app utilizzi, devi concedere l\'accesso ai dati di utilizzo per KidSafety. Ti porteremo alle impostazioni di sistema, dove dovrai cercare KidSafety e attivare l\'interruttore.',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Apri Impostazioni',
          onPress: async () => {
            try {
              // Proviamo prima con le impostazioni di accesso ai dati di utilizzo
              console.log('[SystemSettings] Opening usage access settings with IntentLauncher');
              await IntentLauncher.startActivityAsync(
                'android.settings.USAGE_ACCESS_SETTINGS'
              );
            } catch (error1) {
              console.log('[SystemSettings] Usage access settings failed, trying app details');

              try {
                // Proviamo con le impostazioni dell'app
                await IntentLauncher.startActivityAsync(
                  IntentLauncher.ActivityAction.APPLICATION_DETAILS_SETTINGS,
                  { data: 'package:' + Application.applicationId }
                );
              } catch (error2) {
                console.log('[SystemSettings] App details failed, trying general settings');

                try {
                  // Come ultima risorsa, apriamo le impostazioni generali
                  await Linking.openSettings();
                } catch (error3) {
                  console.error('[SystemSettings] All usage access settings methods failed:', error3);
                  Alert.alert('Errore', 'Non è stato possibile aprire le impostazioni di sistema. Prova ad aprirle manualmente.');
                }
              }
            }
          }
        }
      ]
    );
    return true;
  } catch (error) {
    console.error('[SystemSettings] Error opening usage access settings:', error);
    return false;
  }
};

/**
 * Apre le impostazioni di sistema per le notifiche
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
export const openNotificationSettings = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    try {
      await Linking.openURL('app-settings:notification');
      return true;
    } catch (error) {
      console.error('Error opening iOS notification settings:', error);
      try {
        await Linking.openURL('app-settings:');
        return true;
      } catch (error2) {
        console.error('Error opening iOS settings:', error2);
        return false;
      }
    }
  }

  try {
    // Su Android, utilizziamo IntentLauncher per aprire direttamente le impostazioni di notifica
    const deviceType = getDeviceUIType();
    console.log('[SystemSettings] Device type for notification settings:', deviceType);

    // Mostriamo un messaggio all'utente per spiegare cosa deve fare
    Alert.alert(
      'Accesso alle Notifiche',
      'Per ricevere notifiche importanti, devi concedere l\'accesso alle notifiche per KidSafety. Ti porteremo alle impostazioni di sistema "Accesso alle notifiche", dove dovrai trovare e attivare KidSafety nell\'elenco.',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Apri Impostazioni',
          onPress: async () => {
            try {
              // Proviamo prima con il modulo nativo
              console.log('[SystemSettings] Trying to open notification listener settings with native module');
              const success = await openNotificationListenerSettingsNative();

              if (!success) {
                // Se il modulo nativo fallisce, proviamo con gli intent standard
                console.log('[SystemSettings] Native module failed, trying standard intents');

                // Proviamo ad aprire direttamente le impostazioni di accesso alle notifiche
                console.log('[SystemSettings] Trying to open notification listener settings');
                await IntentLauncher.startActivityAsync(
                  'android.settings.NOTIFICATION_LISTENER_SETTINGS'
                );
              }
            } catch (error1) {
              console.log('[SystemSettings] Notification listener settings failed, trying notification settings', error1);

              try {
                // Proviamo con le impostazioni di notifica generali
                await IntentLauncher.startActivityAsync(
                  'android.settings.NOTIFICATION_SETTINGS'
                );
              } catch (error2) {
                console.log('[SystemSettings] Notification settings failed, trying app notification settings', error2);

                try {
                  // Proviamo con le impostazioni di notifica specifiche per l'app
                  await IntentLauncher.startActivityAsync(
                    'android.settings.APP_NOTIFICATION_SETTINGS',
                    { data: 'package:' + Application.applicationId }
                  );
                } catch (error3) {
                  console.log('[SystemSettings] App notification settings failed, trying app details', error3);

                  try {
                    // Proviamo con le impostazioni dell'app
                    await IntentLauncher.startActivityAsync(
                      IntentLauncher.ActivityAction.APPLICATION_DETAILS_SETTINGS,
                      { data: 'package:' + Application.applicationId }
                    );
                  } catch (error4) {
                    console.log('[SystemSettings] App details failed, trying general settings', error4);

                    // Come ultima risorsa, proviamo con le impostazioni generali
                    await Linking.openSettings();
                  }
                }
              }
            }
          }
        }
      ]
    );
    return true;
  } catch (error) {
    console.error('[SystemSettings] Error opening notification settings:', error);
    return false;
  }
};

export const openAccessibilitySettings = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    try {
      await Linking.openURL('app-settings:');
      return true;
    } catch (error) {
      console.error('Error opening iOS settings:', error);
      return false;
    }
  }

  try {
    // Su Android, utilizziamo IntentLauncher per aprire direttamente le impostazioni di accessibilità
    const deviceType = getDeviceUIType();
    console.log('[SystemSettings] Device type for accessibility settings:', deviceType);

    // Mostriamo un messaggio all'utente per spiegare cosa deve fare
    Alert.alert(
      'Servizio di Accessibilità',
      'Per permettere all\'app di monitorare quali app utilizzi, devi attivare il servizio di accessibilità per KidSafety. Ti porteremo alle impostazioni di sistema, dove dovrai cercare KidSafety e attivare il servizio.',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Apri Impostazioni',
          onPress: async () => {
            try {
              // Proviamo prima con le impostazioni di accessibilità
              console.log('[SystemSettings] Opening accessibility settings with IntentLauncher');
              await IntentLauncher.startActivityAsync(
                IntentLauncher.ActivityAction.ACCESSIBILITY_SETTINGS
              );
            } catch (error1) {
              console.log('[SystemSettings] Accessibility settings failed, trying usage access settings');

              try {
                // Proviamo con le impostazioni di accesso ai dati di utilizzo
                console.log('[SystemSettings] Trying to open usage access settings');
                await IntentLauncher.startActivityAsync(
                  'android.settings.USAGE_ACCESS_SETTINGS'
                );
              } catch (error2) {
                console.log('[SystemSettings] Usage access settings failed, trying app details');

                try {
                  // Proviamo con le impostazioni dell'app
                  await IntentLauncher.startActivityAsync(
                    IntentLauncher.ActivityAction.APPLICATION_DETAILS_SETTINGS,
                    { data: 'package:' + Application.applicationId }
                  );
                } catch (error3) {
                  console.log('[SystemSettings] App details failed, trying general settings');

                  try {
                    // Come ultima risorsa, apriamo le impostazioni generali
                    await Linking.openSettings();
                  } catch (error4) {
                    console.error('[SystemSettings] All accessibility settings methods failed:', error4);
                    Alert.alert('Errore', 'Non è stato possibile aprire le impostazioni di sistema. Prova ad aprirle manualmente.');
                  }
                }
              }
            }
          }
        }
      ]
    );
    return true;
  } catch (error) {
    console.error('[SystemSettings] Error opening accessibility settings:', error);
    return false;
  }
};
