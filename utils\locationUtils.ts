// Importa geokit se disponibile, altrimenti usa una funzione di fallback
let getDistanceFromGeokit: (from: {lat: number, lng: number}, to: {lat: number, lng: number}) => number;

try {
  const geokit = require('geokit');
  getDistanceFromGeokit = geokit.getDistance;
} catch (error) {
  console.warn('Geokit not available, using fallback distance calculation');
  // Implementazione della formula di Haversine come fallback
  getDistanceFromGeokit = (from, to) => {
    const R = 6371; // Raggio della Terra in km
    const dLat = (to.lat - from.lat) * Math.PI / 180;
    const dLon = (to.lng - from.lng) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(from.lat * Math.PI / 180) * Math.cos(to.lat * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c; // Distanza in km
  };
}

/**
 * Calcola la distanza tra due coordinate geografiche in metri
 */
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  return getDistanceFromGeokit(
    { lat: lat1, lng: lon1 },
    { lat: lat2, lng: lon2 }
  ) * 1000; // Converte in metri
};

/**
 * Verifica se una posizione è all'interno di una zona circolare
 */
export const isLocationInZone = (
  latitude: number,
  longitude: number,
  zoneLatitude: number,
  zoneLongitude: number,
  zoneRadius: number
): boolean => {
  const distance = calculateDistance(
    latitude,
    longitude,
    zoneLatitude,
    zoneLongitude
  );
  return distance <= zoneRadius;
};

/**
 * Verifica se una posizione è all'interno di una delle zone sicure
 * Restituisce la zona se è all'interno, altrimenti null
 */
export const checkSafeZoneStatus = (
  latitude: number,
  longitude: number,
  safeZones: Array<{
    id: string;
    name: string;
    latitude: number;
    longitude: number;
    radius: number;
  }>
): { isInside: boolean; zone: any } | null => {
  if (!safeZones || safeZones.length === 0) {
    console.log('[LocationUtils] No safe zones to check');
    return null;
  }

  console.log(`[LocationUtils] Checking ${safeZones.length} safe zones for location: ${latitude}, ${longitude}`);

  for (const zone of safeZones) {
    if (!zone || !zone.latitude || !zone.longitude || !zone.radius) {
      console.warn('[LocationUtils] Invalid zone data:', zone);
      continue;
    }

    console.log(`[LocationUtils] Checking zone: ${zone.name} (${zone.id})`);
    console.log(`[LocationUtils] Zone coordinates: ${zone.latitude}, ${zone.longitude}, radius: ${zone.radius}m`);

    try {
      const distance = calculateDistance(
        latitude,
        longitude,
        zone.latitude,
        zone.longitude
      );

      console.log(`[LocationUtils] Distance to zone center: ${distance}m, Zone radius: ${zone.radius}m`);

      const isInside = distance <= zone.radius;
      console.log(`[LocationUtils] Location is ${isInside ? 'inside' : 'outside'} zone ${zone.name}`);

      if (isInside) {
        return { isInside: true, zone };
      }
    } catch (error) {
      console.error(`[LocationUtils] Error checking zone ${zone.name}:`, error);
    }
  }

  console.log('[LocationUtils] Location is not inside any safe zone');
  return null;
};

export default {
  calculateDistance,
  isLocationInZone,
  checkSafeZoneStatus
};
