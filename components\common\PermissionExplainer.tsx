import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

type PermissionExplainerProps = {
  visible: boolean;
  onRequestPermission: () => void;
  onDismiss: () => void;
  permissionType: 'location' | 'backgroundLocation' | 'notifications' | 'appMonitoring' | 'batteryOptimization' | 'accessibility';
  userType: 'parent' | 'child';
  permissionStatus?: boolean;
  onCheckStatus?: () => void;
};

const PermissionExplainer = ({
  visible,
  onRequestPermission,
  onDismiss,
  permissionType,
  userType,
  permissionStatus,
  onCheckStatus,
}: PermissionExplainerProps) => {

  const getContent = () => {
    switch (permissionType) {
      case 'location':
        return {
          title: userType === 'child'
            ? "Possiamo vedere dove sei?"
            : "Monitoraggio della posizione",
          description: userType === 'child'
            ? "Questa app ha bisogno di sapere dove sei per far vedere ai tuoi genitori che sei al sicuro."
            : "Questa autorizzazione è necessaria per vedere la posizione di tuo figlio sulla mappa.",
          icon: "location",
          action: userType === 'child' ? "Va bene!" : "Consenti"
        };

      case 'backgroundLocation':
        return {
          title: userType === 'child'
            ? "Possiamo vedere dove sei anche quando l'app è chiusa?"
            : "Monitoraggio in background",
          description: userType === 'child'
            ? "Anche quando non stai usando l'app, i tuoi genitori potranno sapere dove sei e assicurarsi che tu sia al sicuro."
            : "Con questa autorizzazione, potrai monitorare la posizione di tuo figlio anche quando non ha l'app aperta.",
          icon: "locate",
          action: userType === 'child' ? "Certo!" : "Consenti"
        };

      case 'notifications':
        return {
          title: userType === 'child'
            ? "Possiamo mandarti messaggi importanti?"
            : "Notifiche importanti",
          description: userType === 'child'
            ? "Ti invieremo messaggi quando entri o esci dalle zone sicure."
            : "Riceverai notifiche quando tuo figlio entra o esce da una zona sicura.",
          icon: "notifications",
          action: userType === 'child' ? "Si!" : "Consenti"
        };

      case 'appMonitoring':
        return {
          title: userType === 'child'
            ? "Monitoraggio App"
            : "Monitoraggio App",
          description: userType === 'child'
            ? "Per monitorare quali app utilizzi e quanto tempo ci passi, abbiamo bisogno di alcune autorizzazioni."
            : "Questa autorizzazione è necessaria per monitorare quali app utilizza tuo figlio e per quanto tempo.",
          icon: "phone-portrait",
          action: userType === 'child' ? "Attiva" : "Attiva"
        };

      case 'batteryOptimization':
        return {
          title: userType === 'child'
            ? "Ottimizzazione Batteria"
            : "Ottimizzazione Batteria",
          description: userType === 'child'
            ? "Disattiva l'ottimizzazione della batteria per KidSafety per permettere all'app di funzionare correttamente in background."
            : "Disattiva l'ottimizzazione della batteria per KidSafety per permettere all'app di monitorare tuo figlio anche in background.",
          icon: "battery-charging",
          action: userType === 'child' ? "Attiva" : "Attiva"
        };

      case 'accessibility':
        return {
          title: userType === 'child'
            ? "Servizio di Accessibilità"
            : "Servizio di Accessibilità",
          description: userType === 'child'
            ? "Per monitorare quali app utilizzi, devi attivare il servizio di accessibilità per KidSafety."
            : "Questa autorizzazione è necessaria per monitorare quali app utilizza tuo figlio.",
          icon: "eye",
          action: userType === 'child' ? "Attiva" : "Attiva"
        };

      default:
        return {
          title: "Autorizzazione richiesta",
          description: "Questa autorizzazione è necessaria per il corretto funzionamento dell'app.",
          icon: "alert-circle",
          action: "Consenti"
        };
    }
  };

  const content = getContent();

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onDismiss}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.iconContainer}>
            <Ionicons name={content.icon as any} size={40} color="#4630EB" />
          </View>

          <Text style={styles.title}>{content.title}</Text>

          <Text style={styles.description}>{content.description}</Text>

          {userType === 'child' && (
            <View style={styles.childExplanation}>
              <Image
                source={require('../../assets/child-safety.png')}
                style={styles.childImage}
                defaultSource={require('../../assets/child-safety-placeholder.png')}
              />
              <Text style={styles.childExplanationText}>
                È solo per la tua sicurezza, così i tuoi genitori possono essere sicuri che tu stia bene!
              </Text>
            </View>
          )}

          {Platform.OS === 'ios' && permissionType === 'backgroundLocation' && (
            <View style={styles.infoBox}>
              <Ionicons name="information-circle-outline" size={20} color="#4630EB" />
              <Text style={styles.infoText}>
                {userType === 'child'
                  ? "Seleziona 'Sempre' nelle impostazioni quando richiesto."
                  : "Nelle impostazioni di iOS, assicurati di scegliere 'Sempre' per monitorare in background."}
              </Text>
            </View>
          )}

          <View style={styles.buttonsContainer}>
            <TouchableOpacity
              style={styles.secondaryButton}
              onPress={onDismiss}
            >
              <Text style={styles.secondaryButtonText}>
                {userType === 'child' ? "Dopo" : "Non ora"}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.primaryButton}
              onPress={onRequestPermission}
            >
              <Text style={styles.primaryButtonText}>{content.action}</Text>
            </TouchableOpacity>
          </View>

          {(permissionType === 'appMonitoring' || permissionType === 'batteryOptimization' || permissionType === 'accessibility') && onCheckStatus && (
            <TouchableOpacity
              style={styles.checkButton}
              onPress={onCheckStatus}
            >
              <Text style={styles.checkButtonText}>Verifica stato</Text>
            </TouchableOpacity>
          )}

          {permissionStatus !== undefined && (
            <View style={styles.statusContainer}>
              <Text style={styles.statusText}>
                Stato: {permissionStatus ? 'Autorizzazione concessa' : 'Autorizzazione non concessa'}
              </Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#EBE7FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
    color: '#333',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    lineHeight: 22,
  },
  childExplanation: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F7FF',
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
  },
  childImage: {
    width: 40,
    height: 40,
    marginRight: 12,
  },
  childExplanationText: {
    flex: 1,
    fontSize: 14,
    color: '#4630EB',
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#EBE7FF',
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    width: '100%',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#4630EB',
    marginLeft: 8,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 8,
  },
  primaryButton: {
    backgroundColor: '#4630EB',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginLeft: 8,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  secondaryButton: {
    backgroundColor: '#F1F1F1',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 20,
    flex: 1,
    marginRight: 8,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#666',
    fontWeight: '600',
    fontSize: 16,
  },
  checkButton: {
    backgroundColor: '#EBE7FF',
    borderRadius: 30,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginTop: 16,
    alignItems: 'center',
    alignSelf: 'center',
  },
  checkButtonText: {
    color: '#4630EB',
    fontWeight: 'bold',
    fontSize: 14,
  },
  statusContainer: {
    marginTop: 16,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 10,
    width: '100%',
  },
  statusText: {
    textAlign: 'center',
    fontSize: 14,
    color: '#666',
  },
});

export default PermissionExplainer;