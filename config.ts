import Constants from 'expo-constants';

// Default values for development - D<PERSON> NOT use these in production
const DEV_SUPABASE_URL = 'https://urtuplpcvvvmnmebabxy.supabase.co';
const DEV_SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVydHVwbHBjdnZ2bW5tZWJhYnh5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ1NDcwNTMsImV4cCI6MjA2MDEyMzA1M30.4XyPhHLi61Kn5owrh9j6WcfvotOj-b2WGCIWm3OeqlU';
// Nota: questo è solo un segnaposto. Per far funzionare l'assistente ai compiti, 
// è necessario fornire un'API key valida per Anthropic nelle variabili d'ambiente.
const DEV_ANTHROPIC_API_KEY = ''; 

// Google Play Billing public key for purchase verification
const GOOGLE_PLAY_PUBLIC_KEY = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3a9VXB8MQI72xnPZDZKY/TqI253wuk78UBFs+wgy+isemPBP9VxiGSl/PjgXF9jfogUA1eI25O5knhxvAhfLx5ZGOHpPNm30XhPfILAtHHdzEO3SZq2nj/xoWtTEl+bdmgqdsx2Ej8rmdWwiGi+hPyCplgCWdJfUiCTOazB9MxoqTWYmDY0Bf++YGqXuFhfYKZdlPSwzlviOIpVaVmFRBabM/6bwhisBUWr8jTJsIFgECdvApUTxDSydArk59ZiTI06Nh2DK+3P05Mh4cdcsGvnhn24lQqiOk+nAEr6RoSc3f6Sfrsoc0ZDUMAQ9IGlp1NmPlmdNc9DdC/s2PWVngQIDAQAB';

// Anthropic API Key - Esplicitamente definita qui per evitare problemi di caricamento da .env
// In produzione, questo dovrebbe essere caricato in modo sicuro dalle variabili d'ambiente
const ANTHROPIC_API_KEY = '************************************************************************************************************';

const config = {
  supabaseUrl: Constants.expoConfig?.extra?.supabaseUrl || process.env.EXPO_PUBLIC_SUPABASE_URL || DEV_SUPABASE_URL,
  supabaseAnonKey: Constants.expoConfig?.extra?.supabaseAnonKey || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || DEV_SUPABASE_ANON_KEY,
  anthropicApiKey: Constants.expoConfig?.extra?.anthropicApiKey || process.env.EXPO_PUBLIC_ANTHROPIC_API_KEY || ANTHROPIC_API_KEY,
  googlePlayPublicKey: GOOGLE_PLAY_PUBLIC_KEY,
  revenueCatAndroidKey: process.env.EXPO_PUBLIC_REVENUECAT_GOOGLE_API_KEY,
  revenueCatIosKey: process.env.EXPO_PUBLIC_REVENUECAT_APPLE_API_KEY,
};

export default config;