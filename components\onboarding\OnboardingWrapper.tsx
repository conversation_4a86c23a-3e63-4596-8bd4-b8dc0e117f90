import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter, useSegments } from 'expo-router';

interface OnboardingWrapperProps {
  children: React.ReactNode;
}

export default function OnboardingWrapper({ children }: OnboardingWrapperProps) {
  const [hasSeenOnboarding, setHasSeenOnboarding] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const segments = useSegments();

  // Check onboarding status on mount
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        console.log('OnboardingWrapper: Checking onboarding status...');
        const onboardingStatus = await AsyncStorage.getItem('hasSeenOnboarding');
        const hasSeenIt = onboardingStatus === 'true';
        console.log('OnboardingWrapper: Onboarding status:', hasSeenIt, 'Current segments:', segments);

        setHasSeenOnboarding(hasSeenIt);
        setIsLoading(false);
      } catch (error) {
        console.error('OnboardingWrapper: Error checking onboarding status:', error);
        setHasSeenOnboarding(false);
        setIsLoading(false);
      }
    };

    checkOnboardingStatus();
  }, []);

  // Handle navigation based on onboarding status
  useEffect(() => {
    if (isLoading || hasSeenOnboarding === null) return;

    // Ritarda la navigazione per assicurarsi che il Root Layout sia montato
    const timer = setTimeout(() => {
      // Se l'utente non ha visto l'onboarding e non è già nella pagina onboarding
      if (hasSeenOnboarding === false && segments[0] !== 'onboarding') {
        console.log('🚀 OnboardingWrapper: User has not seen onboarding, navigating to /onboarding');
        router.replace('/onboarding');
        return;
      }

      // Se l'utente ha visto l'onboarding ma è ancora nella schermata di onboarding
      if (hasSeenOnboarding === true && segments[0] === 'onboarding') {
        console.log('🚀 OnboardingWrapper: User has seen onboarding but is on onboarding page, navigating to login');
        router.replace('/auth/parent-login');
        return;
      }
    }, 100); // Ritardo di 100ms per permettere al layout di montarsi

    return () => clearTimeout(timer);
  }, [hasSeenOnboarding, isLoading, segments, router]);

  // Non blocchiamo mai il rendering dei children - lasciamo che expo-router gestisca il routing
  return <>{children}</>;
}
