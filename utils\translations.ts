// Definizione del tipo per le traduzioni
interface Translations {
  [key: string]: {
    permissions: {
      welcome: {
        title: string;
        description: string;
        buttonText: string;
      };
      location: {
        title: string;
        description: string;
      };
      backgroundLocation: {
        title: string;
        description: string;
      };
      notifications: {
        title: string;
        description: string;
      };
      complete: {
        title: string;
        description: string;
        buttonText: string;
      };
      missingPermissionsAlert: {
        title: string;
        message: string;
        button: string;
      };
      back: string;
      next: string;
    };
    auth: {
      login: string;
      signup: string;
      email: string;
      password: string;
      forgotPassword: string;
      loginButton: string;
      signupButton: string;
      alreadyHaveAccount: string;
      dontHaveAccount: string;
      tagline: string;
      loggingIn: string;
      signingUp: string;
      childLoginHere: string;
      parentLoginHere: string;
      loginError: string;
      signupError: string;
      passwordReset: string;
      passwordResetSent: string;
      passwordResetError: string;
    };
    homework: {
      title: string;
      loading: string;
      loginRequired: string;
      emptyStateTitle: string;
      emptyStateText: string;
      questionPlaceholder: string;
      imagePlaceholder: string;
      yourQuestion: string;
      assistantAnswer: string;
      error: string;
      errorMessage: string;
    };
    settings: {
      title: string;
      language: string;
      logout: string;
      languageChanged: string;
      confirmLogout: {
        title: string;
        message: string;
        cancel: string;
        logout: string;
      };
      version: string;
    };
    common: {
      loading: string;
      error: string;
      success: string;
      cancel: string;
      ok: string;
      save: string;
      delete: string;
      edit: string;
      add: string;
      remove: string;
      search: string;
      back: string;
      next: string;
      previous: string;
      finish: string;
      continue: string;
      skip: string;
      done: string;
      details: string;
      confirm: string;
    };
    subscription: {
      title: string;
      subtitle: string;
      managementTitle: string;
      managementSubtitle: string;
      trialInfoTitle: string;
      trialInfoSubtitle: string;
      checking: string;
      noSubscription: string;
      trialActive: string;
      subscriptionActive: string;
      expired: string;
      loadingDetails: string;
      loadingPackages: string;
      noPackagesAvailable: string;
      noSubscriptionTitle: string;
      noSubscriptionMessage: string;
      plan: string;
      daysRemaining: string;
      trialStarted: string;
      trialEnds: string;
      subscriptionStarted: string;
      subscriptionEnds: string;
      active: string;
      inactive: string;
      restorePurchases: string;
      managementInfo: string;
      subscriptionInfo: string;
      subscriptionRequired: string;
      paymentInfo: string;
      monthlyPlan: string;
      yearlyPlan: string;
      savePercent: string;
      monthlyDescription: string;
      yearlyDescription: string;
      billedMonthly: string;
      billedAnnually: string;
      afterTrialTitle: string;
      autoRenewalNotice: string;
      feature1: string;
      feature2: string;
      feature3: string;
      subscribe: string;
      alreadySubscribed: string;
      choosePlan: string;
      freeTrial: string;
      unknown: string;
      featuresTitle: string;
      featureTitle1: string;
      featureDesc1: string;
      featureTitle2: string;
      featureDesc2: string;
      featureTitle3: string;
      featureDesc3: string;
      trialPeriodTitle: string;
      trialPeriodDesc: string;
      startTrial: string;
      trialTerms: string;
      subscriptionStatus: string;
    };
    dashboard: {
      title: string;
      children: string;
      safeZones: string;
      missions: string;
      addChild: string;
      addSafeZone: string;
      noChildrenAdded: string;
      noSafeZonesAdded: string;
      noRecentActivities: string;
      quickActions: string;
      viewMap: string;
      routes: string;
      home: string;
      homework: string;
    };
    child: {
      title: string;
      name: string;
      age: string;
      location: string;
      addChild: string;
      editChild: string;
      deleteChild: string;
      confirmDelete: {
        title: string;
        message: string;
        cancel: string;
        delete: string;
      };
      enterName: string;
      enterAge: string;
    };
    safeZone: {
      title: string;
      name: string;
      radius: string;
      addSafeZone: string;
      editSafeZone: string;
      deleteSafeZone: string;
      confirmDelete: {
        title: string;
        message: string;
        cancel: string;
        delete: string;
      };
      enterName: string;
      enterRadius: string;
      selectLocation: string;
      tapToSelectLocation: string;
    };
    mission: {
      title: string;
      name: string;
      description: string;
      reward: string;
      status: string;
      addMission: string;
      editMission: string;
      deleteMission: string;
      assignTo: string;
      confirmDelete: {
        title: string;
        message: string;
        cancel: string;
        delete: string;
      };
      statuses: {
        pending: string;
        completed: string;
        verified: string;
        new: string;
        inProgress: string;
      };
      enterName: string;
      enterDescription: string;
      enterReward: string;
      selectChild: string;
      noChildren: string;
      missionCompleted: string;
      rewardAchieved: string;
    };
    sos: {
      title: string;
      sendAlert: string;
      alertSent: string;
      alertHistory: string;
      noAlerts: string;
      emergencySOS: string;
      instructions: string;
      recentAlerts: string;
      acknowledged: string;
      pending: string;
      sendAnotherAlertTitle: string;
      sendAnotherAlertMessage: string;
      authError: string;
      permissionDenied: string;
      locationPermissionNeeded: string;
      alertSentToParent: string;
      alertCreated: string;
      alertCreatedNoParent: string;
      sendAlertError: string;
      lastAlertSent: string;
      pressInCaseOfEmergency: string;
      sendingAlertIn: string;
      locationWillBeSent: string;
    };
    routes: {
      title: string;
      selectChild: string;
      selectDate: string;
      statistics: string;
      routes: string;
      distance: string;
      duration: string;
      viewRoutesOnMap: string;
      noChildAdded: string;
      addChildToViewRoutes: string;
      loading: string;
      loadingStatistics: string;
      noStatisticsAvailable: string;
    };
  };
}

// Traduzioni disponibili
const translations: Translations = {
  en: {
    permissions: {
      welcome: {
        title: 'Welcome to KidSafety!',
        description: 'This app helps your parents know where you are and keep you safe. We need some permissions to work properly. Follow the instructions in the next screens.',
        buttonText: 'Let\'s Start',
      },
      location: {
        title: 'Location',
        description: 'We need to know your location to help your parents know where you are and keep you safe.',
      },
      backgroundLocation: {
        title: 'Background Location',
        description: 'We need to track your location even when the app is closed to keep your parents informed about where you are.',
      },
      notifications: {
        title: 'Notifications',
        description: 'We need to send you notifications to alert you about important events and messages from your parents.',
      },
      complete: {
        title: 'All Set!',
        description: 'Thank you for setting up the permissions! You can now start using KidSafety.',
        buttonText: 'Start Using KidSafety',
      },
      missingPermissionsAlert: {
        title: 'Missing Permissions',
        message: 'You haven\'t granted all required permissions. Some app features may not work properly. Do you want to grant all permissions before continuing?',
        button: 'Grant Permissions',
      },
      back: 'Back',
      next: 'Next',
    },
    auth: {
      login: 'Login',
      signup: 'Sign Up',
      email: 'Email',
      password: 'Password',
      forgotPassword: 'Forgot Password?',
      loginButton: 'Login',
      signupButton: 'Sign Up',
      alreadyHaveAccount: 'Already have an account? Login',
      dontHaveAccount: 'Don\'t have an account? Sign Up',
      tagline: 'Keeping your children safe',
      loggingIn: 'Logging in...',
      signingUp: 'Signing up...',
      childLoginHere: 'Child? Login here',
      parentLoginHere: 'Parent? Login here',
      loginError: 'Login failed. Please check your credentials.',
      signupError: 'Sign up failed. Please try again.',
      passwordReset: 'Reset Password',
      passwordResetSent: 'Password reset email sent.',
      passwordResetError: 'Failed to send password reset email.',
    },
    homework: {
      title: 'Homework Helper',
      loading: 'Loading...',
      loginRequired: 'You must be logged in to use this feature.',
      emptyStateTitle: 'Homework Helper',
      emptyStateText: 'Ask a question about your homework or upload a photo to get help.',
      questionPlaceholder: 'Ask a question about your homework...',
      imagePlaceholder: 'Ask a question about the image...',
      yourQuestion: 'Your question:',
      assistantAnswer: 'Assistant\'s answer:',
      error: 'Error',
      errorMessage: 'Could not get an answer: ',
    },
    settings: {
      title: 'Settings',
      language: 'Language',
      logout: 'Logout',
      languageChanged: 'The language has been changed to English',
      confirmLogout: {
        title: 'Confirm Logout',
        message: 'Are you sure you want to log out?',
        cancel: 'Cancel',
        logout: 'Logout',
      },
      version: 'Version',
    },
    common: {
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      cancel: 'Cancel',
      ok: 'OK',
      save: 'Save',
      delete: 'Delete',
      edit: 'Edit',
      add: 'Add',
      remove: 'Remove',
      search: 'Search',
      back: 'Back',
      next: 'Next',
      previous: 'Previous',
      finish: 'Finish',
      continue: 'Continue',
      skip: 'Skip',
      done: 'Done',
      details: 'Details',
      confirm: 'Confirm',
    },
    subscription: {
      title: 'Subscription',
      subtitle: 'Choose a subscription plan to access all features',
      managementTitle: 'Manage Subscription',
      managementSubtitle: 'View and manage your current subscription',
      trialInfoTitle: 'Free Trial',
      trialInfoSubtitle: 'Try all premium features for 3 days',
      checking: 'Checking subscription status...',
      noSubscription: 'No subscription found',
      trialActive: 'Trial Active',
      subscriptionActive: 'Subscription Active',
      expired: 'Expired',
      loadingDetails: 'Loading subscription details...',
      loadingPackages: 'Loading subscription packages...',
      noPackagesAvailable: 'No subscription packages available',
      noSubscriptionTitle: 'No Subscription',
      noSubscriptionMessage: 'You don\'t have an active subscription. Subscribe to access all features.',
      plan: 'Plan',
      daysRemaining: 'Days Remaining',
      trialStarted: 'Trial Started',
      trialEnds: 'Trial Ends',
      subscriptionStarted: 'Subscription Started',
      subscriptionEnds: 'Subscription Ends',
      active: 'Active',
      inactive: 'Inactive',
      restorePurchases: 'Restore Purchases',
      managementInfo: 'You can manage your subscription through your app store account settings.',
      subscriptionInfo: 'Subscriptions will automatically renew unless canceled at least 24 hours before the end of the current period.',
      subscriptionRequired: 'A subscription is required to use all premium features of the app.',
      paymentInfo: 'Payment will be charged to your Google Play account at confirmation of purchase.',
      monthlyPlan: 'Monthly Plan',
      yearlyPlan: 'Yearly Plan',
      savePercent: 'Save 67%',
      monthlyDescription: 'Full access to all premium features',
      yearlyDescription: 'Full access to all premium features at a discounted rate',
      billedMonthly: 'Billed monthly at €3.99',
      billedAnnually: 'Billed annually at €15.99',
      afterTrialTitle: 'After the trial period ends, you will be charged:',
      autoRenewalNotice: 'SUBSCRIPTION WILL AUTO-RENEW.',
      feature1: 'Real-time location tracking',
      feature2: 'Unlimited safe zones with geofencing',
      feature3: 'SOS alerts and emergency notifications',
      subscribe: 'Subscribe',
      alreadySubscribed: 'Already Subscribed',
      choosePlan: 'Choose a Plan',
      freeTrial: 'Free Trial',
      unknown: 'Unknown',
      featuresTitle: 'Premium Features',
      featureTitle1: 'Real-time Location Tracking',
      featureDesc1: 'Know where your children are at all times',
      featureTitle2: 'Safe Zones',
      featureDesc2: 'Create unlimited safe zones with entry/exit notifications',
      featureTitle3: 'SOS Alerts',
      featureDesc3: 'Receive immediate notifications in case of emergency',
      trialPeriodTitle: '3-Day Free Trial',
      trialPeriodDesc: 'Try all premium features for 3 days',
      startTrial: 'Start Free Trial',
      trialTerms: 'By starting the free trial, you agree to our terms.',
      subscriptionStatus: 'Subscription Status',
    },
    dashboard: {
      title: 'Dashboard',
      children: 'Children',
      safeZones: 'Safe Zones',
      missions: 'Missions',
      addChild: 'Add Child',
      addSafeZone: 'Add Safe Zone',
      noChildrenAdded: 'No children added yet',
      noSafeZonesAdded: 'No safe zones added yet',
      noRecentActivities: 'No recent activities',
      quickActions: 'Quick Actions',
      viewMap: 'View Map',
      routes: 'Routes',
      home: 'Home',
      homework: 'Homework',
    },
    child: {
      title: 'Child',
      name: 'Name',
      age: 'Age',
      location: 'Location',
      addChild: 'Add Child',
      editChild: 'Edit Child',
      deleteChild: 'Delete Child',
      confirmDelete: {
        title: 'Confirm Delete',
        message: 'Are you sure you want to delete this child?',
        cancel: 'Cancel',
        delete: 'Delete',
      },
      enterName: 'Enter name',
      enterAge: 'Enter age',
    },
    safeZone: {
      title: 'Safe Zone',
      name: 'Name',
      radius: 'Radius',
      addSafeZone: 'Add Safe Zone',
      editSafeZone: 'Edit Safe Zone',
      deleteSafeZone: 'Delete Safe Zone',
      confirmDelete: {
        title: 'Confirm Delete',
        message: 'Are you sure you want to delete this safe zone?',
        cancel: 'Cancel',
        delete: 'Delete',
      },
      enterName: 'Enter name',
      enterRadius: 'Enter radius',
      selectLocation: 'Select Location',
      tapToSelectLocation: 'Tap on the map to select a location',
    },
    mission: {
      title: 'Mission',
      name: 'Name',
      description: 'Description',
      reward: 'Reward',
      status: 'Status',
      addMission: 'Add Mission',
      editMission: 'Edit Mission',
      deleteMission: 'Delete Mission',
      assignTo: 'Assign To',
      confirmDelete: {
        title: 'Confirm Delete',
        message: 'Are you sure you want to delete this mission?',
        cancel: 'Cancel',
        delete: 'Delete',
      },
      statuses: {
        pending: 'Pending',
        completed: 'Completed',
        verified: 'Verified',
        new: 'New',
        inProgress: 'In Progress',
      },
      enterName: 'Enter name',
      enterDescription: 'Enter description',
      enterReward: 'Enter reward',
      selectChild: 'Select child',
      noChildren: 'No children',
      missionCompleted: 'Mission completed!',
      rewardAchieved: 'Reward Achieved!',
    },
    sos: {
      title: 'SOS',
      sendAlert: 'Send Alert',
      alertSent: 'Alert Sent',
      alertHistory: 'Alert History',
      noAlerts: 'No Alerts',
      emergencySOS: 'Emergency SOS',
      instructions: 'Press the SOS button in case of emergency.',
      recentAlerts: 'Recent SOS Alerts',
      acknowledged: 'Acknowledged',
      pending: 'Pending',
      sendAnotherAlertTitle: 'Send Another Alert?',
      sendAnotherAlertMessage: 'You recently sent an alert. Are you sure?',
      authError: 'Authentication error',
      permissionDenied: 'Permission Denied',
      locationPermissionNeeded: 'Location permission needed',
      alertSentToParent: 'Alert sent to parent',
      alertCreated: 'SOS Alert Created',
      alertCreatedNoParent: 'Alert created but no parent found',
      sendAlertError: 'Failed to send SOS alert',
      lastAlertSent: 'Last alert sent',
      pressInCaseOfEmergency: 'Press in case of emergency',
      sendingAlertIn: 'Sending SOS Alert in',
      locationWillBeSent: 'Your location will be sent',
    },
    routes: {
      title: 'Routes',
      selectChild: 'Select Child',
      selectDate: 'Select Date',
      statistics: 'Statistics',
      routes: 'Routes',
      distance: 'Distance',
      duration: 'Duration',
      viewRoutesOnMap: 'View Routes on Map',
      noChildAdded: 'No children added',
      addChildToViewRoutes: 'Add a child to view routes',
      loading: 'Loading...',
      loadingStatistics: 'Loading statistics...',
      noStatisticsAvailable: 'No statistics available',
    },
  },
};

// Funzione per ottenere le traduzioni in base alla lingua selezionata
export const getTranslations = (languageCode: string) => {
  console.log('getTranslations called with language code:', languageCode);
  const result = translations[languageCode] || translations.en;
  console.log('getTranslations returning translations for:', languageCode in translations ? languageCode : 'en (fallback)');
  return result;
};

export default translations;
