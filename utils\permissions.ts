import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import * as TaskManager from 'expo-task-manager';
import { Platform } from 'react-native';
// Fallback functions for Expo Go compatibility
const checkForPermission = async (): Promise<boolean> => {
  console.warn('[Permissions] Usage stats module not available in Expo Go');
  return false;
};

const showUsageAccessSettings = async (packageName: string): Promise<void> => {
  console.warn('[Permissions] Usage stats settings not available in Expo Go');
};

/**
 * Permission types supported by the app
 */
export type PermissionType =
  | 'location'
  | 'backgroundLocation'
  | 'notifications'
  | 'usageStats'
  | 'camera'
  | 'mediaLibrary';

/**
 * Permission status types
 */
export type PermissionStatus =
  | 'granted'
  | 'denied'
  | 'undetermined'
  | 'unavailable';

/**
 * Request a specific permission
 *
 * @param permission - The permission type to request
 * @returns The status of the permission after requesting
 */
export const requestPermission = async (permission: PermissionType): Promise<PermissionStatus> => {
  try {
    switch (permission) {
      case 'location':
        const { status: locationStatus } = await Location.requestForegroundPermissionsAsync();
        return locationStatus;

      case 'backgroundLocation':
        // Must request foreground location permission first
        const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
        if (foregroundStatus !== 'granted') {
          return 'denied';
        }

        const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
        return backgroundStatus;

      case 'notifications':
        console.log('[Permissions] Requesting notification permissions...');
        try {
          // Prima verifichiamo lo stato attuale
          const { status: currentStatus } = await Notifications.getPermissionsAsync();
          console.log('[Permissions] Current notification permission status:', currentStatus);

          // Se le autorizzazioni sono già concesse, restituiamo lo stato attuale
          if (currentStatus === 'granted') {
            return currentStatus;
          }

          // Altrimenti, richiediamo le autorizzazioni
          const { status: notificationStatus } = await Notifications.requestPermissionsAsync({
            ios: {
              allowAlert: true,
              allowBadge: true,
              allowSound: true,
              allowAnnouncements: true,
            },
          });
          console.log('[Permissions] New notification permission status:', notificationStatus);
          return notificationStatus;
        } catch (error) {
          console.error('[Permissions] Error requesting notification permissions:', error);
          return 'denied';
        }

      case 'usageStats':
        try {
          console.log('[Permissions] Requesting usage stats permission...');
          // Apre le impostazioni per concedere il permesso
          await showUsageAccessSettings('');
          // Dopo aver aperto le impostazioni, controlliamo se il permesso è stato concesso
          // Nota: questo richiede che l'utente torni all'app dopo aver concesso il permesso
          const hasUsagePermission = await checkForPermission();
          return hasUsagePermission ? 'granted' : 'denied';
        } catch (error) {
          console.error('[Permissions] Error requesting usage stats permission:', error);
          return 'denied';
        }

      case 'camera':
        const { status: cameraStatus } = await requestCameraPermission();
        return cameraStatus;

      case 'mediaLibrary':
        const { status: mediaStatus } = await requestMediaLibraryPermission();
        return mediaStatus;

      default:
        return 'unavailable';
    }
  } catch (error) {
    console.error(`Error requesting permission (${permission}):`, error);
    return 'unavailable';
  }
};

/**
 * Check the current status of a permission
 *
 * @param permission - The permission type to check
 * @returns The current status of the permission
 */
export const checkPermission = async (permission: PermissionType): Promise<PermissionStatus> => {
  try {
    switch (permission) {
      case 'location':
        const { status: locationStatus } = await Location.getForegroundPermissionsAsync();
        return locationStatus;

      case 'backgroundLocation':
        const { status: backgroundStatus } = await Location.getBackgroundPermissionsAsync();
        return backgroundStatus;

      case 'notifications':
        console.log('[Permissions] Checking notification permissions...');
        try {
          // Verifichiamo lo stato attuale
          const { status: notificationStatus } = await Notifications.getPermissionsAsync();
          console.log('[Permissions] Notification permission status:', notificationStatus);

          // Su Android, dobbiamo verificare anche se le notifiche sono abilitate a livello di sistema
          if (Platform.OS === 'android' && notificationStatus === 'granted') {
            try {
              // Verifichiamo se le notifiche sono abilitate a livello di sistema
              const areEnabled = await Notifications.getDevicePushTokenAsync();
              console.log('[Permissions] Device push token available:', !!areEnabled);
              return 'granted';
            } catch (tokenError) {
              console.warn('[Permissions] Error getting device push token:', tokenError);
              // Se non riusciamo a ottenere il token, consideriamo le notifiche come non abilitate
              return 'denied';
            }
          }

          return notificationStatus;
        } catch (error) {
          console.error('[Permissions] Error checking notification permissions:', error);
          return 'denied';
        }

      case 'usageStats':
        try {
          console.log('[Permissions] Checking usage stats permission...');
          const hasUsagePermission = await checkForPermission();
          return hasUsagePermission ? 'granted' : 'denied';
        } catch (error) {
          console.error('[Permissions] Error checking usage stats permission:', error);
          return 'denied';
        }

      case 'camera':
        const { status: cameraStatus } = await checkCameraPermission();
        return cameraStatus;

      case 'mediaLibrary':
        const { status: mediaStatus } = await checkMediaLibraryPermission();
        return mediaStatus;

      default:
        return 'unavailable';
    }
  } catch (error) {
    console.error(`Error checking permission (${permission}):`, error);
    return 'unavailable';
  }
};

/**
 * Request all permissions needed by the app
 * Simplified version that only requests essential permissions
 *
 * @returns An object with the status of each permission
 */
export const requestAllPermissions = async () => {
  console.log('🔐 Permissions: Requesting essential permissions only');

  try {
    // Only request essential permissions to avoid crashes
    const location = await requestPermission('location');
    const notifications = await requestPermission('notifications');

    // Background location is optional and can be requested later
    let backgroundLocation: PermissionStatus = 'undetermined';
    if (location === 'granted') {
      try {
        backgroundLocation = await requestPermission('backgroundLocation');
      } catch (error) {
        console.warn('🔐 Permissions: Background location permission failed, continuing without it:', error);
        backgroundLocation = 'denied';
      }
    }

    const result = {
      location,
      backgroundLocation,
      notifications,
      // Set optional permissions as granted to avoid blocking the app
      backgroundFetch: 'granted' as PermissionStatus,
      camera: 'granted' as PermissionStatus,
      mediaLibrary: 'granted' as PermissionStatus,
    };

    console.log('🔐 Permissions: Request completed:', result);
    return result;
  } catch (error) {
    console.error('🔐 Permissions: Error requesting permissions:', error);
    // Return safe defaults to prevent app crashes
    return {
      location: 'denied' as PermissionStatus,
      backgroundLocation: 'denied' as PermissionStatus,
      notifications: 'denied' as PermissionStatus,
      backgroundFetch: 'granted' as PermissionStatus,
      camera: 'granted' as PermissionStatus,
      mediaLibrary: 'granted' as PermissionStatus,
    };
  }
};

/**
 * Check all permissions needed by the app
 * Simplified version that only checks essential permissions
 *
 * @returns An object with the status of each permission
 */
export const checkAllPermissions = async () => {
  console.log('🔐 Permissions: Checking essential permissions only');

  try {
    // Only check essential permissions to avoid crashes
    const location = await checkPermission('location');
    const backgroundLocation = await checkPermission('backgroundLocation');
    const notifications = await checkPermission('notifications');

    const result = {
      location,
      backgroundLocation,
      notifications,
      // Set optional permissions as granted to avoid blocking the app
      backgroundFetch: 'granted' as PermissionStatus,
      camera: 'granted' as PermissionStatus,
      mediaLibrary: 'granted' as PermissionStatus,
    };

    console.log('🔐 Permissions: Check completed:', result);
    return result;
  } catch (error) {
    console.error('🔐 Permissions: Error checking permissions:', error);
    // Return safe defaults to prevent app crashes
    return {
      location: 'undetermined' as PermissionStatus,
      backgroundLocation: 'undetermined' as PermissionStatus,
      notifications: 'undetermined' as PermissionStatus,
      backgroundFetch: 'granted' as PermissionStatus,
      camera: 'granted' as PermissionStatus,
      mediaLibrary: 'granted' as PermissionStatus,
    };
  }
};

// Helper functions for camera and media library permissions
// These would need to be implemented using expo-camera and expo-media-library
const requestCameraPermission = async () => {
  // Placeholder - implement with expo-camera
  return { status: 'unavailable' as PermissionStatus };
};

const checkCameraPermission = async () => {
  // Placeholder - implement with expo-camera
  return { status: 'unavailable' as PermissionStatus };
};

const requestMediaLibraryPermission = async () => {
  // Placeholder - implement with expo-media-library
  return { status: 'unavailable' as PermissionStatus };
};

const checkMediaLibraryPermission = async () => {
  // Placeholder - implement with expo-media-library
  return { status: 'unavailable' as PermissionStatus };
};