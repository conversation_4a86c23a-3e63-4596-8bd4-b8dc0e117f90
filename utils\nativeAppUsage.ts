import { NativeModules, Platform, Linking } from 'react-native';
import { openUsageAccessSettings as openSystemUsageAccessSettings } from './systemSettings';

// Verifica se il modulo nativo è disponibile
console.log('[NativeAppUsage] Checking if AppUsageModule is available:', !!NativeModules.AppUsageModule);
if (!NativeModules.AppUsageModule) {
  console.log('[NativeAppUsage] AppUsageModule not found, using fallback implementation');
}

const AppUsageModule = NativeModules.AppUsageModule || {
  // Implementazione di fallback per quando il modulo nativo non è disponibile
  hasUsageStatsPermission: async () => {
    console.warn('AppUsageModule non disponibile: hasUsageStatsPermission');
    return false;
  },
  openUsageAccessSettings: async () => {
    console.warn('AppUsageModule non disponibile: openUsageAccessSettings');
    alert('Il modulo nativo per il monitoraggio delle app non è disponibile. Esegui "npx expo run:android" per creare una build di sviluppo con i moduli nativi.');
    // Su Android, proviamo ad aprire le impostazioni di sistema come fallback
    if (Platform.OS === 'android') {
      try {
        await Linking.openSettings();
        return true;
      } catch (e) {
        console.error('Errore nell\'apertura delle impostazioni:', e);
        return false;
      }
    }
    return false;
  },
  getAppUsageStats: async () => {
    console.warn('AppUsageModule non disponibile: getAppUsageStats');
    return [];
  },
  getCurrentAppUsage: async () => {
    console.warn('AppUsageModule non disponibile: getCurrentAppUsage');
    return null;
  }
};

// Constants for interval types
export const INTERVAL_TYPES = {
  DAILY: 0,
  WEEKLY: 1,
  MONTHLY: 2,
  YEARLY: 3,
};

// Interface for app usage data
export interface AppUsageData {
  packageName: string;
  appName: string;
  usageTime: number; // in seconds
  lastTimeUsed: number;
}

// Interface for current app usage
export interface CurrentAppUsage {
  packageName: string;
  appName: string;
  lastTimeUsed: number;
}

/**
 * Check if the app usage stats permission is granted
 * @returns Promise<boolean> - true if permission is granted, false otherwise
 */
export const hasUsageStatsPermission = async (): Promise<boolean> => {
  console.log('[NativeAppUsage] Checking usage stats permission');
  if (Platform.OS !== 'android') {
    console.log('[NativeAppUsage] Not on Android, automatically granting permission');
    return true; // On iOS, we'll just pretend we have permission
  }

  try {
    const hasPermission = await AppUsageModule.hasUsageStatsPermission();
    console.log('[NativeAppUsage] Usage stats permission check result:', hasPermission);
    return hasPermission;
  } catch (error) {
    console.error('[NativeAppUsage] Error checking usage stats permission:', error);
    return false;
  }
};

/**
 * Open the usage access settings screen
 * @returns Promise<boolean> - true if the settings screen was opened successfully
 */
/**
 * Apre direttamente le impostazioni di accesso utilizzo app
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
/**
 * Apre direttamente le impostazioni di Accesso speciale per le app
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
export const openSpecialAppAccessSettings = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    return false; // Non disponibile su iOS
  }

  try {
    // Proviamo ad aprire direttamente le impostazioni di Accesso speciale per le app
    console.log('[NativeAppUsage] Trying to open special app access settings directly');

    // Proviamo con diversi URL per aprire le impostazioni di Accesso speciale per le app
    try {
      await Linking.openURL('android:settings/special_app_access');
      return true;
    } catch (error1) {
      console.log('[NativeAppUsage] Failed with android:settings/special_app_access, trying alternative');

      try {
        // Proviamo con un altro URL
        await Linking.openURL('android:settings/special_access');
        return true;
      } catch (error2) {
        console.log('[NativeAppUsage] Failed with android:settings/special_access, trying another');

        try {
          // Proviamo con un altro URL ancora
          await Linking.openURL('android:settings/application_special_access');
          return true;
        } catch (error3) {
          console.log('[NativeAppUsage] All specific special app access URLs failed');
          return false;
        }
      }
    }
  } catch (error) {
    console.log('[NativeAppUsage] Failed to open special app access settings directly:', error);
    return false;
  }
};

export const openDirectUsageAccessSettings = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    return false; // Non disponibile su iOS
  }

  try {
    // Proviamo ad aprire direttamente le impostazioni di accesso utilizzo app
    console.log('[NativeAppUsage] Trying to open usage access settings directly');

    // Proviamo con diversi URL per aprire le impostazioni di accesso utilizzo app
    try {
      await Linking.openURL('android:settings/usage_access');
      return true;
    } catch (error1) {
      console.log('[NativeAppUsage] Failed with android:settings/usage_access, trying alternative');

      try {
        // Proviamo con un altro URL
        await Linking.openURL('package:android.settings/usage_access');
        return true;
      } catch (error2) {
        console.log('[NativeAppUsage] Failed with package:android.settings/usage_access, trying another');

        try {
          // Proviamo con un altro URL ancora
          await Linking.openURL('android-app://com.android.settings/usage_access');
          return true;
        } catch (error3) {
          console.log('[NativeAppUsage] Failed with android-app URL, trying intent URL');

          try {
            // Proviamo con un URL di intent
            await Linking.openURL('intent://settings/usage_access#Intent;scheme=android;package=com.android.settings;end');
            return true;
          } catch (error4) {
            console.log('[NativeAppUsage] All specific usage access URLs failed');
            return false;
          }
        }
      }
    }
  } catch (error) {
    console.log('[NativeAppUsage] Failed to open usage access settings directly:', error);
    return false;
  }
};

/**
 * Apre le impostazioni principali del dispositivo
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
export const openMainSettings = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    return false; // Non disponibile su iOS
  }

  try {
    // Proviamo ad aprire direttamente le impostazioni principali del dispositivo
    console.log('[NativeAppUsage] Trying to open main device settings');

    // Proviamo con diversi URL per aprire le impostazioni principali
    try {
      // Proviamo ad aprire direttamente le impostazioni principali
      await Linking.openURL('android:settings');
      return true;
    } catch (error1) {
      console.log('[NativeAppUsage] Failed with android:settings, trying alternative');

      try {
        // Proviamo con un altro URL
        await Linking.openURL('package:android.settings');
        return true;
      } catch (error2) {
        console.log('[NativeAppUsage] Failed with package:android.settings, trying another');

        try {
          // Proviamo con un altro URL ancora
          await Linking.openURL('android-app://com.android.settings');
          return true;
        } catch (error3) {
          console.log('[NativeAppUsage] All specific URLs failed, falling back to default');

          // Come ultima risorsa, usiamo il metodo standard
          await Linking.openSettings();
          return true;
        }
      }
    }
  } catch (error) {
    console.error('[NativeAppUsage] Error opening device settings:', error);
    return false;
  }
};

/**
 * Apre le impostazioni di accesso utilizzo app
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
/**
 * Apre direttamente le impostazioni delle app
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
export const openAppSettings = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    return false; // Non disponibile su iOS
  }

  try {
    // Proviamo ad aprire direttamente le impostazioni delle app
    console.log('[NativeAppUsage] Trying to open app settings directly');

    // Proviamo con diversi URL per aprire le impostazioni delle app
    try {
      await Linking.openURL('android:settings/applications');
      return true;
    } catch (error1) {
      console.log('[NativeAppUsage] Failed with android:settings/applications, trying alternative');

      try {
        // Proviamo con un altro URL
        await Linking.openURL('package:android.settings/applications');
        return true;
      } catch (error2) {
        console.log('[NativeAppUsage] Failed with package:android.settings/applications, trying another');

        try {
          // Proviamo con un altro URL ancora
          await Linking.openURL('android-app://com.android.settings/applications');
          return true;
        } catch (error3) {
          console.log('[NativeAppUsage] All specific app settings URLs failed');
          return false;
        }
      }
    }
  } catch (error) {
    console.log('[NativeAppUsage] Failed to open app settings directly:', error);
    return false;
  }
};

/**
 * Apre le impostazioni di accesso utilizzo app
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
export const openUsageAccessSettings = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    return false; // Non disponibile su iOS
  }

  try {
    // Utilizziamo la nuova funzione che usa IntentLauncher per aprire direttamente le impostazioni di accesso ai dati di utilizzo
    console.log('[NativeAppUsage] Using systemSettings.openUsageAccessSettings');
    const success = await openSystemUsageAccessSettings();

    if (success) {
      return true;
    }

    // Se fallisce, proviamo con i metodi precedenti come fallback
    console.log('[NativeAppUsage] systemSettings.openUsageAccessSettings failed, trying fallback methods');

    // Prima proviamo ad aprire direttamente le impostazioni di accesso utilizzo app
    console.log('[NativeAppUsage] Trying to open direct usage access settings first');
    const directSuccess = await openDirectUsageAccessSettings();
    if (directSuccess) {
      return true;
    }

    // Se fallisce, proviamo ad aprire le impostazioni di accesso speciale per le app
    console.log('[NativeAppUsage] Direct usage access failed, trying special app access');
    const specialAccessSuccess = await openSpecialAppAccessSettings();
    if (specialAccessSuccess) {
      return true;
    }

    // Se fallisce, proviamo ad aprire le impostazioni delle app
    console.log('[NativeAppUsage] Special app access failed, trying app settings');
    const appSettingsSuccess = await openAppSettings();
    if (appSettingsSuccess) {
      return true;
    }

    // Se fallisce anche questo, proviamo ad aprire le impostazioni principali
    console.log('[NativeAppUsage] App settings failed, opening main settings');
    return await openMainSettings();
  } catch (error) {
    console.error('[NativeAppUsage] Error opening any settings:', error);
    return false;
  }
};

/**
 * Get app usage statistics
 * @param intervalType - The type of interval (daily, weekly, monthly, yearly)
 * @param startTime - The start time in seconds from now (e.g., 86400 for 24 hours ago)
 * @param endTime - The end time in seconds from now (e.g., 0 for now)
 * @returns Promise<AppUsageData[]> - Array of app usage data
 */
export const getAppUsageStats = async (
  intervalType: number = INTERVAL_TYPES.DAILY,
  startTime: number = 86400, // Default to 24 hours
  endTime: number = 0
): Promise<AppUsageData[]> => {
  if (Platform.OS !== 'android') {
    return []; // Not available on iOS
  }

  try {
    return await AppUsageModule.getAppUsageStats(intervalType, startTime, endTime);
  } catch (error) {
    console.error('Error getting app usage stats:', error);
    return [];
  }
};

/**
 * Get the currently used app
 * @returns Promise<CurrentAppUsage | null> - The currently used app or null if not available
 */
export const getCurrentAppUsage = async (): Promise<CurrentAppUsage | null> => {
  if (Platform.OS !== 'android') {
    console.log('[NativeAppUsage] getCurrentAppUsage not available on iOS');
    return null; // Not available on iOS
  }

  try {
    const result = await AppUsageModule.getCurrentAppUsage();
    if (result) {
      console.log('[NativeAppUsage] Current app usage:', result.appName);
    } else {
      console.log('[NativeAppUsage] No current app usage data available');
    }
    return result;
  } catch (error) {
    console.error('[NativeAppUsage] Error getting current app usage:', error);
    return null;
  }
};

export default {
  hasUsageStatsPermission,
  openUsageAccessSettings,
  openSpecialAppAccessSettings,
  openDirectUsageAccessSettings,
  openAppSettings,
  openMainSettings,
  getAppUsageStats,
  getCurrentAppUsage,
  INTERVAL_TYPES,
};
