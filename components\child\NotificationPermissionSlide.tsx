import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Alert } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import * as Notifications from 'expo-notifications';
import { openSystemSettings } from '../../utils/systemSettings';
import { checkPermission, requestPermission } from '../../utils/permissions';

interface NotificationPermissionSlideProps {
  onPermissionGranted: () => void;
}

const NotificationPermissionSlide: React.FC<NotificationPermissionSlideProps> = ({ onPermissionGranted }) => {
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [checking, setChecking] = useState(true);
  const [permissionGranted, setPermissionGranted] = useState(false);

  useEffect(() => {
    // Resettiamo lo stato delle autorizzazioni all'avvio del componente
    setNotificationsEnabled(false);
    setPermissionGranted(false);

    // Verifichiamo le autorizzazioni iniziali
    checkNotificationPermission();

    // Verifica periodicamente le autorizzazioni (ogni secondo)
    const intervalId = setInterval(checkNotificationPermission, 1000);

    // Pulizia dell'intervallo quando il componente viene smontato
    return () => clearInterval(intervalId);
  }, []);

  // Effetto per notificare il componente padre quando l'autorizzazione è concessa
  useEffect(() => {
    if (notificationsEnabled && !permissionGranted) {
      console.log('[NotificationPermissionSlide] Permission granted, notifying parent');
      setPermissionGranted(true);
      // Notifichiamo il componente padre che l'autorizzazione è stata concessa
      onPermissionGranted();
    }
  }, [notificationsEnabled, permissionGranted]);

  const checkNotificationPermission = async () => {
    try {
      setChecking(true);
      console.log('[NotificationPermissionSlide] Checking notification permission...');

      // Verifichiamo lo stato attuale delle autorizzazioni
      const status = await checkPermission('notifications');
      console.log('[NotificationPermissionSlide] Notification permission status:', status);

      setNotificationsEnabled(status === 'granted');
      setChecking(false);
    } catch (error) {
      console.error('[NotificationPermissionSlide] Error checking notification permission:', error);
      setChecking(false);
    }
  };

  const handleRequestPermission = async () => {
    try {
      console.log('[NotificationPermissionSlide] Requesting notification permission...');

      // Richiediamo l'autorizzazione per le notifiche
      const status = await requestPermission('notifications');
      console.log('[NotificationPermissionSlide] Notification permission status after request:', status);

      if (status === 'granted') {
        setNotificationsEnabled(true);
      } else {
        // Se l'autorizzazione non è stata concessa, mostriamo un messaggio all'utente
        Alert.alert(
          'Autorizzazione necessaria',
          'Per ricevere notifiche importanti, devi concedere l\'autorizzazione per le notifiche. Vuoi aprire le impostazioni di sistema per concedere l\'autorizzazione?',
          [
            { text: 'No', style: 'cancel' },
            {
              text: 'Apri Impostazioni',
              onPress: async () => {
                // Apriamo le impostazioni di sistema per le notifiche
                await openSystemSettings('notifications');
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('[NotificationPermissionSlide] Error requesting notification permission:', error);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Notifiche</Text>
        <Text style={styles.subtitle}>
          Per ricevere notifiche importanti, devi concedere l'autorizzazione per le notifiche
        </Text>
      </View>

      <View style={styles.imageContainer}>
        <View style={styles.iconBackground}>
          <FontAwesome5 name="bell" size={80} color="#4630EB" />
        </View>
      </View>

      <View style={styles.permissionsContainer}>
        <View style={styles.permissionItem}>
          <View style={styles.permissionStatus}>
            {notificationsEnabled ? (
              <FontAwesome5 name="check-circle" size={24} color="#4CAF50" />
            ) : (
              <FontAwesome5 name="times-circle" size={24} color="#F44336" />
            )}
          </View>
          <View style={styles.permissionInfo}>
            <Text style={styles.permissionTitle}>Notifiche</Text>
            <Text style={styles.permissionDescription}>
              Consenti a KidSafety di inviarti notifiche importanti
            </Text>
          </View>
          {!notificationsEnabled && (
            <TouchableOpacity
              style={styles.permissionButton}
              onPress={handleRequestPermission}
            >
              <Text style={styles.permissionButtonText}>Attiva</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {checking && (
        <Text style={styles.checkingText}>Verifica delle autorizzazioni in corso...</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  imageContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  iconBackground: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  permissionsContainer: {
    marginBottom: 20,
  },
  permissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  permissionStatus: {
    marginRight: 15,
  },
  permissionInfo: {
    flex: 1,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  permissionDescription: {
    fontSize: 14,
    color: '#666',
  },
  permissionButton: {
    backgroundColor: '#4630EB',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
  },
  permissionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  checkingText: {
    textAlign: 'center',
    color: '#666',
    marginTop: 10,
  },
});

export default NotificationPermissionSlide;
