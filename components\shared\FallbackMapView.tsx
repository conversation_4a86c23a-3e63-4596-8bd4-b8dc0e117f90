import React, { useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';

interface FallbackMapViewProps {
  onRetry?: () => void;
  error?: string;
}

const FallbackMapView: React.FC<FallbackMapViewProps> = ({ onRetry, error }) => {
  // Ottimizzare con useMemo per prevenire re-render inutili
  const memoizedContent = useMemo(() => (
    <>
      <FontAwesome5 name="map-marked-alt" size={60} color="#ccc" />
      <Text style={styles.title}>Impossibile caricare la mappa</Text>
      <Text style={styles.message}>
        {error || 'Si è verificato un errore durante il caricamento della mappa.'}
      </Text>
    </>
  ), [error]);

  return (
    <View style={styles.container}>
      {memoizedContent}
      <Text style={styles.hint}>
        Verifica la tua connessione internet e assicurati che l'app abbia le autorizzazioni necessarie.
      </Text>
      {onRetry && (
        <TouchableOpacity style={styles.button} onPress={onRetry}>
          <Text style={styles.buttonText}>Riprova</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    color: '#333',
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
  },
  hint: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#4630EB',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default FallbackMapView;

