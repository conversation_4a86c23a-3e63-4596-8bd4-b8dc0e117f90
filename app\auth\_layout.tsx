import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

export default function AuthLayout() {
  return (
    <>
      <StatusBar style="dark" />
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: '#f5f5f5' },
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen name="parent-login" options={{ title: 'Parent Login' }} />
        <Stack.Screen name="parent-signup" options={{ title: 'Parent Signup' }} />
        <Stack.Screen name="child-login" options={{ title: 'Child Login' }} />
        <Stack.Screen name="child-onboarding" options={{ title: 'Child Onboarding', gestureEnabled: false }} />
        <Stack.Screen name="onboarding" options={{ title: 'Onboarding' }} />
      </Stack>
    </>
  );
}