import React, { useState } from 'react';
import { View, StyleSheet, Text, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import AuthForm from '../../components/auth/AuthForm';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import Header from '../../components/shared/Header';
import { useTranslations } from '../../contexts/TranslationContext';
import { Ionicons } from '@expo/vector-icons';

export default function ParentSignupScreen() {
  const router = useRouter();
  const { signUp, error, isLoading, refreshSubscriptionStatus } = useAuth();
  const { t } = useTranslations();
  const [isCheckingSubscription, setIsCheckingSubscription] = useState(false);

  // Handle form submission
  const handleSignup = async (values: Record<string, string>) => {
    try {
      console.log('ParentSignupScreen: Starting signup for:', values.email);
      setIsCheckingSubscription(true);

      // Sign up the user
      try {
        await signUp(values.email, values.password, values.name);
      } catch (err: any) {
        console.error('Signup error:', err?.message);
        Alert.alert('Error', err?.message || 'Failed to create account. Please try again.');
        return;
      }

      // Refresh subscription status
      await refreshSubscriptionStatus();

      // After successful signup, navigate to trial info screen
      console.log('ParentSignupScreen: Navigating to trial info screen');
      router.replace('/auth/trial-info');
    } catch (err: any) {
      console.error('Signup flow error:', err?.message);
      Alert.alert('Error', err?.message || 'Failed to create account. Please try again.');
    } finally {
      setIsCheckingSubscription(false);
    }
  };

  // Navigate back to login
  const navigateToLogin = () => {
    router.back();
  };

  if (isLoading || isCheckingSubscription) {
    return <LoadingIndicator fullScreen text={isCheckingSubscription ? "Checking subscription..." : "Creating your account..."} />;
  }

  return (
    <View style={styles.container}>
      <Header
        titleKey="auth.createAccount"
        showBackButton
        onBackPress={navigateToLogin}
      />

      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <View style={styles.logoIcon}>
            <Ionicons name="shield-checkmark" size={64} color="#4630EB" />
          </View>
          <Text style={styles.appName}>KidSafety</Text>
        </View>

        <View style={styles.formContainer}>
          <AuthForm
            formType="signup"
            onSubmit={handleSignup}
            isLoading={isLoading}
            error={error}
            onToggleForm={navigateToLogin}
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  logoIcon: {
    width: 100,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: '#F0F0F0',
    borderRadius: 50,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4630EB',
  },
  formContainer: {
    flex: 1,
  },
});