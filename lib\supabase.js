import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Load environment variables from process.env
const EXPO_PUBLIC_SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://urtuplpcvvvmnmebabxy.supabase.co';
const EXPO_PUBLIC_SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVydHVwbHBjdnZ2bW5tZWJhYnh5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ1NDcwNTMsImV4cCI6MjA2MDEyMzA1M30.4XyPhHLi61Kn5owrh9j6WcfvotOj-b2WGCIWm3OeqlU';

// Log environment variables for debugging
console.log('🔧 Caricamento variabili d\'ambiente dal process.env:', {
  SUPABASE_URL: EXPO_PUBLIC_SUPABASE_URL ? EXPO_PUBLIC_SUPABASE_URL.substring(0, 30) + '...' : '❌ NON TROVATA',
  SUPABASE_ANON_KEY: EXPO_PUBLIC_SUPABASE_ANON_KEY ? EXPO_PUBLIC_SUPABASE_ANON_KEY.substring(0, 30) + '...' : '❌ NON TROVATA',
  FROM_ENV: process.env.EXPO_PUBLIC_SUPABASE_URL ? '✅ DA .ENV' : '❌ FALLBACK',
});

// Initialize Supabase client
const supabase = createClient(
  EXPO_PUBLIC_SUPABASE_URL,
  EXPO_PUBLIC_SUPABASE_ANON_KEY,
  {
    auth: {
      storage: AsyncStorage,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
  }
);

// Sign up a new user
export async function signUp(email, password, userData) {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: userData,
    },
  });

  if (error) throw error;
  return data;
}

// Sign in a user
export async function signIn(email, password) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) throw error;
  return data;
}

// Sign out a user
export async function signOut() {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
}

// Get the current user
export async function getCurrentUser() {
  const { data: { user } } = await supabase.auth.getUser();
  return user;
}

// Get user data from the profiles table
export async function getUserData(userId) {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();

  if (error) throw error;
  return data;
}

// Get children for a parent
export async function getChildrenForParent(parentId) {
  const { data, error } = await supabase
    .from('children')
    .select(`
      child_id,
      token,
      users (
        name
      )
    `)
    .eq('parent_id', parentId);

  if (error) throw error;
  return data;
}

// Get safe zones for a parent (simplified to avoid recursion)
export async function getSafeZones(parentId) {
  const { data, error } = await supabase
    .from('safe_zones')
    .select(`
      id,
      name,
      latitude,
      longitude,
      radius,
      address
    `)
    .eq('parent_id', parentId);

  if (error) throw error;

  // Get child assignments separately
  const { data: assignments, error: assignmentsError } = await supabase
    .from('safe_zone_children')
    .select(`
      safe_zone_id,
      child_id
    `)
    .in('safe_zone_id', data.map(zone => zone.id));

  if (assignmentsError) throw assignmentsError;

  // Get children data
  const childIds = [...new Set(assignments.map(a => a.child_id))];
  const { data: children, error: childrenError } = await supabase
    .from('children')
    .select(`
      child_id,
      users (
        name
      )
    `)
    .in('child_id', childIds);

  if (childrenError) throw childrenError;

  // Combine data
  const zonesWithChildren = data.map(zone => {
    const zoneAssignments = assignments.filter(a => a.safe_zone_id === zone.id);
    const zoneChildren = zoneAssignments.map(a => {
      return children.find(c => c.child_id === a.child_id);
    }).filter(Boolean);

    return {
      ...zone,
      children: zoneChildren
    };
  });

  return zonesWithChildren;
}

// Create a safe zone
export async function createSafeZone(parentId, zoneData) {
  const { data, error } = await supabase
    .from('safe_zones')
    .insert([
      {
        parent_id: parentId,
        name: zoneData.name,
        latitude: zoneData.latitude,
        longitude: zoneData.longitude,
        radius: zoneData.radius,
        address: zoneData.address
      }
    ])
    .select()
    .single();

  if (error) throw error;
  return data;
}

// Assign a safe zone to a child
export async function assignSafeZoneToChild(zoneId, childId) {
  const { data, error } = await supabase
    .from('safe_zone_children')
    .insert([
      {
        safe_zone_id: zoneId,
        child_id: childId
      }
    ]);

  if (error) throw error;
  return data;
}

// Update child location
export async function updateChildLocation(childId, latitude, longitude) {
  const { data, error } = await supabase
    .from('child_locations')
    .upsert([
      {
        child_id: childId,
        latitude,
        longitude,
        timestamp: new Date().toISOString()
      }
    ]);

  if (error) throw error;
  return data;
}

// Get child location
export async function getChildLocation(childId) {
  const { data, error } = await supabase
    .from('child_locations')
    .select('*')
    .eq('child_id', childId)
    .order('timestamp', { ascending: false })
    .limit(1)
    .single();

  if (error) throw error;
  return data;
}

// Delete a child and all related data
export async function deleteChild(childId) {
  console.log('Deleting child:', childId);
  try {
    // Step 1: Delete all zone assignments for this child
    const { error: zoneAssignmentError } = await supabase
      .from('zone_assignments')
      .delete()
      .eq('child_id', childId);

    if (zoneAssignmentError) {
      console.error('Error deleting zone assignments:', zoneAssignmentError);
      // Continue with the deletion process
    }

    // Step 2: Delete all mission assignments for this child
    const { error: missionAssignmentError } = await supabase
      .from('mission_assignments')
      .delete()
      .eq('child_id', childId);

    if (missionAssignmentError) {
      console.error('Error deleting mission assignments:', missionAssignmentError);
      // Continue with the deletion process
    }

    // Step 3: Delete all location history for this child
    const { error: locationHistoryError } = await supabase
      .from('location_history')
      .delete()
      .eq('child_id', childId);

    if (locationHistoryError) {
      console.error('Error deleting location history:', locationHistoryError);
      // Continue with the deletion process
    }

    // Step 4: Delete all location updates for this child
    const { error: locationUpdatesError } = await supabase
      .from('location_updates')
      .delete()
      .eq('user_id', childId);

    if (locationUpdatesError) {
      console.error('Error deleting location updates:', locationUpdatesError);
      // Continue with the deletion process
    }

    // Step 5: Delete all SOS alerts for this child
    const { error: sosAlertsError } = await supabase
      .from('sos_alerts')
      .delete()
      .eq('child_id', childId);

    if (sosAlertsError) {
      console.error('Error deleting SOS alerts:', sosAlertsError);
      // Continue with the deletion process
    }

    // Step 6: Delete all push tokens for this child
    const { error: pushTokensError } = await supabase
      .from('user_push_tokens')
      .delete()
      .eq('user_id', childId);

    if (pushTokensError) {
      console.error('Error deleting push tokens:', pushTokensError);
      // Continue with the deletion process
    }

    // Step 7: Delete the parent-child relationship
    const { error: relationshipError } = await supabase
      .from('parent_child_relationships')
      .delete()
      .eq('child_id', childId);

    if (relationshipError) {
      console.error('Error deleting parent-child relationship:', relationshipError);
      throw relationshipError;
    }

    // Step 8: Delete the child from the users table
    const { error: userError } = await supabase
      .from('users')
      .delete()
      .eq('id', childId);

    if (userError) {
      console.error('Error deleting child from users table:', userError);
      // Continue with the deletion process
    }

    console.log('Child deleted successfully');
    return true;
  } catch (err) {
    console.error('Error in deleteChild:', err);
    throw err;
  }
}

// SOS alert functions
export async function createSOSAlert(childId, latitude, longitude, additional_info) {
  console.log('Creating SOS alert for child:', childId);
  console.log('Location:', { latitude, longitude });

  let alertData;
  let notificationSent = false;
  let parentFound = false;

  try {
    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();
    console.log('Current authenticated user:', user ? user.id : 'No user');
    console.log('Child ID for SOS alert:', childId);

    // First create the alert using the RPC function
    const { data, error } = await supabase.rpc(
      'create_sos_alert',
      {
        p_child_id: childId,
        p_latitude: latitude,
        p_longitude: longitude,
        p_additional_info: additional_info,
        p_timestamp: new Date().toISOString()
      }
    );

    if (error) {
      console.error('Error from RPC function:', error);
      throw error;
    }

    alertData = data;
    console.log('SOS alert created successfully:', alertData);
  } catch (err) {
    console.error('Error creating SOS alert:', err);
    throw err;
  }

  try {
    // Then get the child details to include in notification
    const { data: childData, error: childError } = await supabase
      .from('users')
      .select('name, id')
      .eq('id', childId);

    if (childError) {
      console.error('Error fetching child data for notification:', childError);
      // Create a default notification without child name
      // Implement local notification here if needed
      notificationSent = true;
      return { alertData, notificationSent, parentFound };
    }

    // If no child data found, use a default name
    const childName = childData && childData.length > 0 ? childData[0].name : 'Your child';

    // Get parent associated with this child
    console.log('Fetching parent relationship for child:', childId);
    const { data: relationData, error: relationError } = await supabase
      .from('parent_child_relationships')
      .select('parent_id')
      .eq('child_id', childId);

    console.log('Parent relationship query result:', { relationData, relationError });

    if (relationError) {
      console.error('Error fetching parent relationship for notification:', relationError);
      // Create a notification without trying to send to parent
      // Implement local notification here if needed
      notificationSent = true;
      return { alertData, notificationSent, parentFound };
    }

    if (!relationData || relationData.length === 0) {
      console.error('No parent relationship found for child:', childId);
      // Create a notification without trying to send to parent
      // Implement local notification here if needed
      notificationSent = true;
      return { alertData, notificationSent, parentFound };
    }

    // Parent found
    parentFound = true;
    notificationSent = true;

    // Try to send push notification to parent if possible
    try {
      const parentId = relationData[0].parent_id;
      const { data: tokenData, error: tokenError } = await supabase
        .from('user_push_tokens')
        .select('token')
        .eq('user_id', parentId);

      if (tokenData && tokenData.length > 0 && !tokenError) {
        // Implement push notification here if needed
      }
    } catch (notifError) {
      console.error('Error sending push notification:', notifError);
      // We still consider the notification sent if we were able to create it locally
    }
  } catch (err) {
    console.error('Error sending SOS alert:', err);
    // Don't rethrow here, we already created the alert successfully
  }

  return { alertData, notificationSent, parentFound };
}

// Missions
export async function createMission(parentId, missionData) {
  const { data, error } = await supabase
    .from('missions')
    .insert({
      parent_id: parentId,
      ...missionData,
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function assignMissionToChild(missionId, childId) {
  const { data, error } = await supabase
    .from('mission_assignments')
    .insert({
      mission_id: missionId,
      child_id: childId,
      status: 'assigned',
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function getChildMissions(childId) {
  console.log('Getting missions for child:', childId);

  try {
    // First, check if there are any mission assignments for this child
    const { data: assignmentsData, error: assignmentsError } = await supabase
      .from('mission_assignments')
      .select('id, mission_id, status, completion_date')
      .eq('child_id', childId);

    if (assignmentsError) {
      console.error('Error fetching mission assignments:', assignmentsError);
      throw assignmentsError;
    }

    console.log('Mission assignments found:', assignmentsData?.length || 0);

    if (!assignmentsData || assignmentsData.length === 0) {
      return [];
    }

    // Get the mission details for each assignment
    const missionIds = assignmentsData.map(assignment => assignment.mission_id);

    const { data: missionsData, error: missionsError } = await supabase
      .from('missions')
      .select('id, title, description, reward, due_date, created_at, parent_id')
      .in('id', missionIds);

    if (missionsError) {
      console.error('Error fetching mission details:', missionsError);
      throw missionsError;
    }

    console.log('Mission details found:', missionsData?.length || 0);

    // Combine the data
    const result = assignmentsData.map(assignment => {
      const mission = missionsData?.find(m => m.id === assignment.mission_id);
      return {
        id: assignment.id,
        mission_id: assignment.mission_id,
        child_id: childId,
        status: assignment.status,
        completion_date: assignment.completion_date,
        mission: mission || undefined
      };
    });

    console.log('Combined mission data:', result);
    return result;
  } catch (err) {
    console.error('Error in getChildMissions:', err);
    throw err;
  }
}

export async function updateMissionStatus(assignmentId, status) {
  const { data, error } = await supabase
    .from('mission_assignments')
    .update({
      status,
      ...(status === 'completed' ? { completion_date: new Date() } : {}),
    })
    .eq('id', assignmentId)
    .select()
    .single();

  if (error) throw error;
  return data;
}

export default supabase;
