import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Alert,
  Platform,
  Modal,
  AppState,
  AppStateStatus,
} from 'react-native';
import { useRouter } from 'expo-router';
import { FontAwesome5, Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslations } from '../../contexts/TranslationContext';
import { checkPermission, requestPermission } from '../../utils/permissions';
import { startLocationTracking } from '../../utils/location';
import { openBackgroundLocationSettings } from '../../utils/systemSettings';


import LoadingIndicator from '../shared/LoadingIndicator';
import PermissionHelpImage from './PermissionHelpImage';

const { width } = Dimensions.get('window');

// Definiamo il nostro tipo di PermissionStatus per evitare conflitti con quello importato
type AppPermissionStatus = 'undetermined' | 'granted' | 'denied' | 'unavailable';

interface PermissionState {
  location: AppPermissionStatus;
  backgroundLocation: AppPermissionStatus;
  notifications: AppPermissionStatus;
  notificationAccess: AppPermissionStatus;
}

export default function PermissionOnboardingScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showPermissionHelp, setShowPermissionHelp] = useState(false);
  const [currentPermissionHelp, setCurrentPermissionHelp] = useState<keyof PermissionState>('location');
  const [permissionState, setPermissionState] = useState<PermissionState>({
    location: 'undetermined',
    backgroundLocation: 'undetermined',
    notifications: 'undetermined',
    notificationAccess: 'undetermined',
  });

  // Riferimento per tenere traccia dello stato dell'app
  const appState = useRef(AppState.currentState);

  // Riferimento per tenere traccia della slide corrente quando l'app va in background
  const backgroundSlideRef = useRef<number | null>(null);

  // Check initial permission status
  useEffect(() => {
    const checkPermissions = async () => {
      try {
        // Otteniamo lo stato delle autorizzazioni
        const locationStatus = await checkPermission('location') as AppPermissionStatus;
        const backgroundLocationStatus = await checkPermission('backgroundLocation') as AppPermissionStatus;
        const notificationsStatus = await checkPermission('notifications') as AppPermissionStatus;

        // Inizializziamo anche le altre autorizzazioni
        const notificationAccessStatus: AppPermissionStatus = 'undetermined';

        setPermissionState({
          location: locationStatus,
          backgroundLocation: backgroundLocationStatus,
          notifications: notificationsStatus,
          notificationAccess: notificationAccessStatus,
        });
      } catch (error) {
        console.error('Error checking permissions:', error);
      }
    };

    checkPermissions();
  }, []);

  // Riferimento per tenere traccia del timeout di sicurezza
  const safetyTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Listener per lo stato dell'app (in background/foreground)
  useEffect(() => {
    // Funzione per verificare le autorizzazioni quando l'app torna in foreground
    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      // Se l'app torna in foreground da background
      if (appState.current === 'background' && nextAppState === 'active') {
        console.log('[PermissionOnboardingScreen] App returned to foreground, checking permissions');

        // Verifichiamo se l'app è in stato di caricamento
        console.log('[PermissionOnboardingScreen] Current loading state:', isLoading);

        // Se eravamo nella slide della posizione in background, verifichiamo se l'autorizzazione è stata concessa
        if (backgroundSlideRef.current === 2) { // Indice della slide backgroundLocation
          try {
            console.log('[PermissionOnboardingScreen] Checking background location permission');

            // Impostiamo un timeout di sicurezza per resettare lo stato di caricamento dopo 5 secondi
            // Questo garantisce che l'utente non rimanga bloccato in uno stato di caricamento infinito
            if (safetyTimeoutRef.current) {
              clearTimeout(safetyTimeoutRef.current);
            }

            safetyTimeoutRef.current = setTimeout(() => {
              console.log('[PermissionOnboardingScreen] Safety timeout triggered, resetting loading state');
              setIsLoading(false);
            }, 5000);

            // Aggiungiamo un ritardo per dare tempo al sistema di aggiornare lo stato dell'autorizzazione
            await new Promise(resolve => setTimeout(resolve, 1000));

            const newStatus = await checkPermission('backgroundLocation') as AppPermissionStatus;
            console.log('[PermissionOnboardingScreen] Background location status after returning from settings:', newStatus);

            // Puliamo il timeout di sicurezza poiché abbiamo completato la verifica
            if (safetyTimeoutRef.current) {
              clearTimeout(safetyTimeoutRef.current);
              safetyTimeoutRef.current = null;
            }

            if (newStatus === 'granted') {
              console.log('[PermissionOnboardingScreen] Background location permission granted, updating state');
              setPermissionState(prev => ({ ...prev, backgroundLocation: 'granted' }));
              // Passa alla slide dell'accessibilità (indice 3)
              scrollToSlide(3);
              // Assicuriamoci che lo stato di caricamento sia resettato
              setIsLoading(false);
            } else {
              // Se l'autorizzazione non è stata concessa, mostriamo un messaggio all'utente
              console.log('[PermissionOnboardingScreen] Background location permission not granted');
              setIsLoading(false);
            }
          } catch (error) {
            console.error('[PermissionOnboardingScreen] Error checking background location permission:', error);
            setIsLoading(false);
          }
        } else {
          // Se non eravamo nella slide della posizione in background ma l'app è in stato di caricamento,
          // resettiamo comunque lo stato di caricamento per evitare blocchi
          if (isLoading) {
            console.log('[PermissionOnboardingScreen] Resetting loading state on app return to foreground');
            setIsLoading(false);
          }
        }

        // Resetta il riferimento alla slide in background
        backgroundSlideRef.current = null;
      } else if (nextAppState === 'background') {
        // Salva la slide corrente quando l'app va in background
        backgroundSlideRef.current = currentSlide;
        console.log(`[PermissionOnboardingScreen] App went to background from slide ${currentSlide}`);
      }

      // Aggiorna lo stato corrente dell'app
      appState.current = nextAppState;
    };

    // Aggiungi il listener per lo stato dell'app
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Rimuovi il listener quando il componente viene smontato
    return () => {
      subscription.remove();
      // Puliamo anche il timeout di sicurezza quando il componente viene smontato
      if (safetyTimeoutRef.current) {
        clearTimeout(safetyTimeoutRef.current);
        safetyTimeoutRef.current = null;
      }
    };
  }, [currentSlide, isLoading]);

  // Get translations
  const { t } = useTranslations();

  // Slides configuration
  const slides = [
    {
      id: 'welcome',
      title: t.permissions?.welcome?.title || 'Benvenuto in KidSafety!',
      description: t.permissions?.welcome?.description || 'Questa app aiuta i tuoi genitori a sapere dove sei e a mantenerti al sicuro. Abbiamo bisogno di alcune autorizzazioni per funzionare correttamente. Segui le istruzioni nelle prossime schermate.',
      icon: 'shield-alt',
      buttonText: t.permissions?.welcome?.buttonText || 'Iniziamo',
      buttonAction: () => scrollToNextSlide(),
    },
    {
      id: 'location',
      title: t.permissions?.location?.title || 'Posizione',
      description: t.permissions?.location?.description || 'Quando premi il pulsante qui sotto, vedrai una finestra che chiede "Consenti a KidSafety di accedere alla posizione del dispositivo?". Premi "Consenti" o "Consenti mentre usi l\'app" per permettere ai tuoi genitori di vedere dove sei.',
      icon: 'map-marker-alt',
      permissionType: 'location',
      buttonText: permissionState.location === 'granted' ? 'Già autorizzato' : 'Autorizza posizione',
      buttonAction: () => requestLocationPermission(),
    },
    {
      id: 'backgroundLocation',
      title: t.permissions?.backgroundLocation?.title || 'Posizione in background',
      description: t.permissions?.backgroundLocation?.description || 'Quando premi il pulsante qui sotto, vedrai una finestra che chiede "Consenti a KidSafety di accedere alla tua posizione anche quando l\'app non è in uso?". Premi "Consenti sempre" per permettere ai tuoi genitori di sapere dove sei anche quando non stai usando l\'app. Questo è molto importante per la tua sicurezza!',
      icon: 'location-arrow',
      permissionType: 'backgroundLocation',
      buttonText: permissionState.backgroundLocation === 'granted' ? 'Già autorizzato' : 'Autorizza background',
      buttonAction: () => requestBackgroundLocationPermission(),
      disabled: permissionState.location !== 'granted',
    },

    {
      id: 'complete',
      title: t.permissions?.complete?.title || 'Tutto pronto!',
      description: t.permissions?.complete?.description || 'Grazie per aver configurato tutte le autorizzazioni necessarie! Ora puoi iniziare a usare KidSafety in sicurezza.',
      icon: 'check-circle',
      buttonText: t.permissions?.complete?.buttonText || 'Inizia',
      buttonAction: () => completeOnboarding(),
    },
  ];

  // Vai alla prossima slide
  const scrollToNextSlide = () => {
    if (currentSlide < slides.length - 1) {
      const nextSlide = currentSlide + 1;
      console.log(`[PermissionOnboardingScreen] Going to next slide ${nextSlide}: ${slides[nextSlide].id}`);
      setCurrentSlide(nextSlide);
    }
  };

  // Funzione per saltare direttamente a una slide specifica
  const scrollToSlide = (slideIndex: number) => {
    if (slideIndex >= 0 && slideIndex < slides.length) {
      console.log(`[PermissionOnboardingScreen] Going directly to slide ${slideIndex}: ${slides[slideIndex].id}`);
      setCurrentSlide(slideIndex);
    }
  };

  // Vai alla slide precedente
  const scrollToPreviousSlide = () => {
    if (currentSlide > 0) {
      const prevSlide = currentSlide - 1;
      console.log(`[PermissionOnboardingScreen] Going to previous slide ${prevSlide}: ${slides[prevSlide].id}`);
      setCurrentSlide(prevSlide);
    }
  };

  // Request location permission
  const requestLocationPermission = async () => {
    try {
      setIsLoading(true);
      const status = await requestPermission('location') as AppPermissionStatus;
      setPermissionState(prev => ({ ...prev, location: status }));

      if (status === 'granted') {
        // Passa alla slide successiva senza mostrare alcun alert
        console.log('[PermissionOnboardingScreen] Location permission granted, navigating to next slide');
        // Passa alla slide della posizione in background (indice 2)
        scrollToSlide(2);
      } else {
        Alert.alert(
          'Autorizzazione necessaria',
          'Senza questa autorizzazione, i tuoi genitori non potranno vedere dove sei. Vuoi riprovare?',
          [
            { text: 'No', style: 'cancel' },
            {
              text: 'Apri impostazioni',
              onPress: () => {
                setCurrentPermissionHelp('location');
                setShowPermissionHelp(true);
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      Alert.alert('Errore', 'Si è verificato un errore durante la richiesta dell\'autorizzazione.');
    } finally {
      setIsLoading(false);
    }
  };

  // Request background location permission
  const requestBackgroundLocationPermission = async () => {
    try {
      if (permissionState.location !== 'granted') {
        Alert.alert(
          'Autorizzazione necessaria',
          'Prima di richiedere la posizione in background, devi autorizzare la posizione normale.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Impostiamo un timeout di sicurezza per resettare lo stato di caricamento dopo 10 secondi
      // Questo garantisce che l'utente non rimanga bloccato in uno stato di caricamento infinito
      if (safetyTimeoutRef.current) {
        clearTimeout(safetyTimeoutRef.current);
      }

      safetyTimeoutRef.current = setTimeout(() => {
        console.log('[PermissionOnboardingScreen] Safety timeout triggered in requestBackgroundLocationPermission, resetting loading state');
        setIsLoading(false);
      }, 10000);

      setIsLoading(true);
      console.log('[PermissionOnboardingScreen] Setting loading state to true for background location permission');

      try {
        // Prima proviamo a richiedere l'autorizzazione normalmente
        const status = await requestPermission('backgroundLocation') as AppPermissionStatus;
        setPermissionState(prev => ({ ...prev, backgroundLocation: status }));

        if (status === 'granted') {
          console.log('[PermissionOnboardingScreen] Background location permission granted, navigating to final slide');
          // Passa alla slide finale senza mostrare alcun alert (indice 3)
          scrollToSlide(3);

          // Puliamo il timeout di sicurezza poiché abbiamo completato la richiesta
          if (safetyTimeoutRef.current) {
            clearTimeout(safetyTimeoutRef.current);
            safetyTimeoutRef.current = null;
          }

          setIsLoading(false);
        } else {
          console.log('[PermissionOnboardingScreen] Background location permission not granted, showing confirmation alert');

          // Puliamo il timeout di sicurezza e resettiamo lo stato di caricamento
          if (safetyTimeoutRef.current) {
            clearTimeout(safetyTimeoutRef.current);
            safetyTimeoutRef.current = null;
          }
          setIsLoading(false);

          // Mostriamo un alert di conferma prima di aprire le impostazioni
          Alert.alert(
            'Autorizzazione necessaria',
            'Per permettere ai tuoi genitori di vedere sempre dove sei, anche quando l\'app è chiusa, devi autorizzare la posizione in background. Vuoi aprire le impostazioni?',
            [
              {
                text: 'Annulla',
                style: 'cancel',
                onPress: () => {
                  console.log('[PermissionOnboardingScreen] User cancelled background location permission');
                }
              },
              {
                text: 'Apri impostazioni',
                onPress: async () => {
                  console.log('[PermissionOnboardingScreen] User confirmed, opening background location settings');

                  // Ora apriamo le impostazioni
                  const settingsOpened = await openBackgroundLocationSettings();

                  if (!settingsOpened) {
                    // Se non siamo riusciti ad aprire le impostazioni, mostriamo la schermata di aiuto
                    setCurrentPermissionHelp('backgroundLocation');
                    setShowPermissionHelp(true);
                    return;
                  }

                  // Salviamo la slide corrente per verificare l'autorizzazione quando l'app torna in foreground
                  backgroundSlideRef.current = currentSlide;
                  console.log('[PermissionOnboardingScreen] Opening background location settings, current slide:', currentSlide);
                }
              }
            ]
          );
        }
      } catch (permissionError) {
        console.error('Error requesting background location permission:', permissionError);

        // Puliamo il timeout di sicurezza
        if (safetyTimeoutRef.current) {
          clearTimeout(safetyTimeoutRef.current);
          safetyTimeoutRef.current = null;
        }
        setIsLoading(false);

        // Mostriamo un alert di errore con opzione per aprire le impostazioni
        Alert.alert(
          'Errore',
          'Si è verificato un errore durante la richiesta dell\'autorizzazione. Vuoi aprire manualmente le impostazioni?',
          [
            { text: 'Annulla', style: 'cancel' },
            {
              text: 'Apri impostazioni',
              onPress: async () => {
                try {
                  await openBackgroundLocationSettings();
                  backgroundSlideRef.current = currentSlide;
                } catch (settingsError) {
                  console.error('[PermissionOnboardingScreen] Error opening background location settings:', settingsError);
                  setCurrentPermissionHelp('backgroundLocation');
                  setShowPermissionHelp(true);
                }
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error in background location permission flow:', error);
      Alert.alert('Errore', 'Si è verificato un errore durante la richiesta dell\'autorizzazione.');

      // Puliamo il timeout di sicurezza
      if (safetyTimeoutRef.current) {
        clearTimeout(safetyTimeoutRef.current);
        safetyTimeoutRef.current = null;
      }

      setIsLoading(false);
    }
  };





  // Le notifiche sono automaticamente concesse su Android, quindi non abbiamo più bisogno di questa funzione

  // Complete onboarding and start location tracking
  const completeOnboarding = async () => {
    try {
      console.log('[PermissionOnboarding] Completing onboarding...');

      // Imposta un flag per evitare chiamate multiple
      if (isLoading) {
        console.log('[PermissionOnboarding] Already completing onboarding, ignoring duplicate call');
        return;
      }

      setIsLoading(true);

      // Verifichiamo nuovamente lo stato delle autorizzazioni
      try {
        // Otteniamo lo stato aggiornato delle autorizzazioni
        const locationStatus = await checkPermission('location') as AppPermissionStatus;
        const backgroundLocationStatus = await checkPermission('backgroundLocation') as AppPermissionStatus;

        // Aggiorniamo lo stato delle autorizzazioni
        setPermissionState(prev => ({
          ...prev,
          location: locationStatus,
          backgroundLocation: backgroundLocationStatus
        }));

        console.log('[PermissionOnboarding] Updated permission status:', {
          location: locationStatus,
          backgroundLocation: backgroundLocationStatus
        });
      } catch (checkError) {
        console.error('[PermissionOnboarding] Error checking permissions:', checkError);
      }

      // Per i test, forziamo le autorizzazioni come concesse
      // Questo è solo per i test, in un'app reale dovremmo verificare correttamente le autorizzazioni
      if (__DEV__) {
        console.log('[PermissionOnboarding] Development mode: forcing permissions to granted');
        setPermissionState(prev => ({
          ...prev,
          location: 'granted',
          backgroundLocation: 'granted',
          notifications: 'granted'
        }));

        // Procediamo direttamente all'app senza mostrare avvisi
        console.log('[PermissionOnboarding] Development mode: proceeding to app');
        proceedToApp();
        return;
      }

      // Check if the necessary permissions are granted
      const requiredPermissionsGranted =
        permissionState.location === 'granted' &&
        permissionState.backgroundLocation === 'granted';

      // All critical permissions granted?
      const allCriticalPermissionsGranted = requiredPermissionsGranted;

      // Impostiamo automaticamente le notifiche come concesse su Android
      if (Platform.OS === 'android') {
        setPermissionState(prev => ({ ...prev, notifications: 'granted' }));
      }

      console.log('[PermissionOnboarding] Critical permissions granted:', allCriticalPermissionsGranted);
      console.log('[PermissionOnboarding] Permission state:', JSON.stringify(permissionState));

      // If not all critical permissions are granted, show a warning
      if (!allCriticalPermissionsGranted) {
        Alert.alert(
          t.permissions?.missingPermissionsAlert?.title || 'Autorizzazioni mancanti',
          t.permissions?.missingPermissionsAlert?.message || 'Non hai concesso tutte le autorizzazioni richieste. Alcune funzioni dell\'app potrebbero non funzionare correttamente. Vuoi concedere tutte le autorizzazioni prima di continuare?',
          [
            {
              text: t.permissions?.missingPermissionsAlert?.button || 'Concedi autorizzazioni',
              onPress: () => {
                // Trova l'indice della slide per ogni tipo di autorizzazione
                const slideIndices = {
        location: slides.findIndex(slide => slide.id === 'location'),
        backgroundLocation: slides.findIndex(slide => slide.id === 'backgroundLocation')
      };

                console.log('[PermissionOnboarding] Slide indices:', slideIndices);

                // Go back to the first permission that's not granted
                if (permissionState.location !== 'granted' && slideIndices.location !== -1) {
                  console.log('[PermissionOnboarding] Navigating to location slide');
                  setCurrentSlide(slideIndices.location);
                } else if (permissionState.backgroundLocation !== 'granted' && slideIndices.backgroundLocation !== -1) {
                  console.log('[PermissionOnboarding] Navigating to background location slide');
                  setCurrentSlide(slideIndices.backgroundLocation);

                }
                setIsLoading(false);
              }
            },
            {
              text: 'Continua comunque',
              style: 'destructive',
              onPress: () => {
                console.log('[PermissionOnboarding] Continuing without all permissions');
                proceedToApp();
              }
            }
          ]
        );
        return;
      }

      // If all critical permissions are granted, proceed to the app
      console.log('[PermissionOnboarding] All critical permissions granted, proceeding to app');
      proceedToApp();
    } catch (error) {
      console.error('Error completing onboarding:', error);
      // In caso di errore, procediamo comunque all'app
      console.log('[PermissionOnboarding] Error during onboarding, proceeding to app anyway');
      proceedToApp();
    }
  };

  // Proceed to the app after onboarding
  const proceedToApp = async () => {
    try {
      console.log('[PermissionOnboarding] Proceeding to app...');

      // Imposta un flag per evitare chiamate multiple
      if (isLoading) {
        console.log('[PermissionOnboarding] Already proceeding to app, ignoring duplicate call');
        return;
      }

      setIsLoading(true);

      // Start location tracking if permissions are granted
      if (permissionState.location === 'granted' && permissionState.backgroundLocation === 'granted' && user) {
        console.log('[PermissionOnboarding] Starting location tracking for user:', user.id);
        try {
          await startLocationTracking(user.id);
          console.log('[PermissionOnboarding] Location tracking started successfully');
        } catch (err) {
          console.error('[PermissionOnboarding] Error starting location tracking:', err);
          // Continuiamo comunque, anche se il tracking non è partito
        }
      }

      // App monitoring è stato rimosso

      // Navigate to the child dashboard
      console.log('[PermissionOnboarding] Navigating to child dashboard...');

      try {
        // Forziamo la navigazione alla dashboard
        console.log('[PermissionOnboarding] Attempting to navigate to /(child-tabs)/dashboard');
        router.replace('/(child-tabs)/dashboard');
      } catch (navError) {
        console.error('[PermissionOnboarding] Navigation error:', navError);

        // Se la navigazione fallisce, mostriamo un messaggio all'utente
        Alert.alert(
          'Errore di navigazione',
          'Si è verificato un errore durante la navigazione alla dashboard. Riprova.',
          [
            {
              text: 'Riprova',
              onPress: () => {
                setIsLoading(false);
                proceedToApp();
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error proceeding to app:', error);
      Alert.alert('Errore', 'Si è verificato un errore durante l\'avvio dell\'app.');
      setIsLoading(false);
    }
  };

  // Render permission status indicator
  const renderPermissionStatus = (type: keyof PermissionState) => {
    const status = permissionState[type];

    if (status === 'granted') {
      return (
        <View style={styles.permissionGranted}>
          <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
          <Text style={styles.permissionGrantedText}>Autorizzato</Text>
        </View>
      );
    }

    if (status === 'denied') {
      return (
        <View style={styles.permissionDenied}>
          <Ionicons name="close-circle" size={24} color="#F44336" />
          <Text style={styles.permissionDeniedText}>Non autorizzato</Text>
        </View>
      );
    }

    return (
      <View style={styles.permissionPending}>
        <Ionicons name="ellipse-outline" size={24} color="#FFC107" />
        <Text style={styles.permissionPendingText}>In attesa</Text>
      </View>
    );
  };

  if (isLoading) {
    return <LoadingIndicator fullScreen text="Caricamento..." />;
  }

  return (
    <View style={styles.container}>
      {/* Modal di aiuto per le autorizzazioni */}
      <Modal
        visible={showPermissionHelp}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowPermissionHelp(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Come concedere l'autorizzazione</Text>
              <TouchableOpacity
                onPress={() => setShowPermissionHelp(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              <Text style={styles.modalText}>
                Segui questi passaggi per concedere l'autorizzazione richiesta:
              </Text>

              <PermissionHelpImage permissionType={currentPermissionHelp} />

              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => {
                  setShowPermissionHelp(false);
                  // Richiedi nuovamente l'autorizzazione dopo aver chiuso il modale
                  if (currentPermissionHelp === 'location') {
                    requestLocationPermission();
                  } else if (currentPermissionHelp === 'backgroundLocation') {
                    requestBackgroundLocationPermission();
                  } else if (currentPermissionHelp === 'usageStats') {
                    requestUsageStatsPermission();
                  }
                }}
              >
                <Text style={styles.modalButtonText}>Ho capito, riprova</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Utilizziamo un approccio diverso per mostrare solo la slide corrente */}
      <View style={styles.slideContainer}>
        {slides.map((slide, index) => (
          <View
            key={slide.id}
            style={[
              styles.slide,
              { width, display: index === currentSlide ? 'flex' : 'none' }
            ]}
          >
            {'customComponent' in slide && typeof slide.customComponent === 'function' ? (
              slide.customComponent()
            ) : (
              <View style={styles.slideContent}>
                <View style={styles.iconContainer}>
                  <FontAwesome5 name={slide.icon as any} size={60} color="#4630EB" />
                </View>

                <Text style={styles.slideTitle}>{slide.title}</Text>
                <Text style={styles.slideDescription}>{slide.description}</Text>

                {'permissionType' in slide && slide.permissionType && (
                  <View style={styles.permissionStatusContainer}>
                    {renderPermissionStatus(slide.permissionType as keyof PermissionState)}
                  </View>
                )}

                <TouchableOpacity
                  style={[
                    styles.slideButton,
                    ('disabled' in slide && slide.disabled) && styles.disabledButton,
                    ('permissionType' in slide && permissionState[slide.permissionType as keyof PermissionState] === 'granted') && styles.grantedButton
                  ]}
                  onPress={slide.buttonAction}
                  disabled={'disabled' in slide ? slide.disabled : false}
                >
                  <Text style={styles.slideButtonText}>{slide.buttonText}</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        ))}
      </View>

      <View style={styles.pagination}>
        {slides.map((slide, index) => (
          <TouchableOpacity
            key={slide.id}
            style={[
              styles.paginationDot,
              index === currentSlide && styles.paginationDotActive
            ]}
            onPress={() => scrollToSlide(index)}
          />
        ))}
      </View>

      <View style={styles.navigationButtons}>
        {currentSlide > 0 ? (
          <TouchableOpacity
            style={styles.navigationButton}
            onPress={scrollToPreviousSlide}
          >
            <FontAwesome5 name="chevron-left" size={20} color="#FFFFFF" />
            <Text style={styles.navigationButtonText}>{t.permissions?.back || 'Indietro'}</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.navigationButtonPlaceholder} />
        )}

        {currentSlide < slides.length - 1 &&
          (!('customComponent' in slides[currentSlide]) && slides[currentSlide].id !== 'welcome') && (
            <TouchableOpacity
              style={[styles.navigationButton, styles.navigationButtonNext]}
              onPress={scrollToNextSlide}
            >
              <Text style={styles.navigationButtonText}>{t.permissions?.next || 'Avanti'}</Text>
              <FontAwesome5 name="chevron-right" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          )
        }
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  slideContainer: {
    flex: 1,
    width: '100%',
  },
  slide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  slideContent: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f0f0ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  slideTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  slideDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  slideButton: {
    backgroundColor: '#4630EB',
    paddingVertical: 14,
    paddingHorizontal: 30,
    borderRadius: 30,
    marginTop: 20,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  grantedButton: {
    backgroundColor: '#4CAF50',
  },
  slideButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  paginationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#ccc',
    marginHorizontal: 6,
    padding: 5, // Aumenta l'area cliccabile
  },
  paginationDotActive: {
    backgroundColor: '#4630EB',
    width: 14,
    height: 14,
    borderRadius: 7,
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 30,
    width: '100%',
  },
  navigationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4630EB',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    minWidth: 120,
    justifyContent: 'center',
  },
  navigationButtonNext: {
    backgroundColor: '#4CAF50',
  },
  navigationButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 8,
  },
  navigationButtonPlaceholder: {
    width: 100,
  },
  permissionStatusContainer: {
    marginTop: 10,
    marginBottom: 10,
  },
  permissionGranted: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  permissionGrantedText: {
    color: '#4CAF50',
    marginLeft: 8,
    fontWeight: 'bold',
  },
  permissionDenied: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  permissionDeniedText: {
    color: '#F44336',
    marginLeft: 8,
    fontWeight: 'bold',
  },
  permissionPending: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  permissionPendingText: {
    color: '#FFC107',
    marginLeft: 8,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 20,
    alignItems: 'center',
  },
  modalText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalButton: {
    backgroundColor: '#4630EB',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 30,
    marginTop: 20,
    marginBottom: 10,
  },
  modalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
