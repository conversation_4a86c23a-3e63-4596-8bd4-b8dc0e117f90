import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
}

interface LanguageContextType {
  currentLanguage: Language;
  setLanguage: (language: Language) => Promise<boolean>;
  isLoading: boolean;
}

// Lista delle lingue disponibili
const languages: Language[] = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇬🇧' },
  { code: 'it', name: 'Italian', nativeName: 'Italiano', flag: '🇮🇹' },
  { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },
  { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷' },
  { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪' },
];

// Lingua predefinita
const defaultLanguage: Language = languages[0];

// Chiave per salvare la lingua in AsyncStorage
const LANGUAGE_STORAGE_KEY = 'kidguard_language';

// Creazione del contesto
const LanguageContext = createContext<LanguageContextType>({
  currentLanguage: defaultLanguage,
  setLanguage: async () => true,
  isLoading: true
});

// Hook personalizzato per utilizzare il contesto
export const useLanguage = () => useContext(LanguageContext);

// Provider del contesto
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>(defaultLanguage);
  const [isLoading, setIsLoading] = useState(true);

  // RIMUOVIAMO LA DIPENDENZA DA AuthContext per evitare blocchi circolari
  // const { user, userType } = useAuth();

  // Carica la lingua salvata all'avvio dell'app (SEMPLIFICATO per evitare dipendenze circolari)
  useEffect(() => {
    const loadSavedLanguage = async () => {
      try {
        console.log('🔄 LanguageContext: Starting simplified language initialization...');

        // Carica solo da AsyncStorage per evitare dipendenze circolari con AuthContext
        const savedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);

        if (savedLanguage) {
          try {
            const parsedLanguage = JSON.parse(savedLanguage);
            console.log('LanguageContext: Loaded saved language from AsyncStorage:', parsedLanguage.code);

            // Verifica che la lingua sia valida
            const languageObj = languages.find(lang => lang.code === parsedLanguage.code);
            if (languageObj) {
              setCurrentLanguage(languageObj);
            } else {
              console.warn('LanguageContext: Invalid saved language, using default');
              setCurrentLanguage(defaultLanguage);
            }
          } catch (parseError) {
            console.warn('LanguageContext: Error parsing saved language, using default:', parseError);
            setCurrentLanguage(defaultLanguage);
          }
        } else {
          console.log('LanguageContext: No saved language found, using default:', defaultLanguage.code);
          setCurrentLanguage(defaultLanguage);
        }
      } catch (error) {
        console.error('❌ LanguageContext: Error loading saved language:', error);
        setCurrentLanguage(defaultLanguage);
      } finally {
        console.log('✅ LanguageContext: Language initialization completed');
        setIsLoading(false);
      }
    };

    loadSavedLanguage();
  }, []); // Rimuoviamo le dipendenze da user e userType per evitare blocchi circolari

  // Funzione per cambiare la lingua
  const setLanguage = async (language: Language) => {
    try {
      console.log('Setting language to:', language.code);
      setCurrentLanguage(language);
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, JSON.stringify(language));

      // TODO: Salvataggio nel database rimosso per evitare dipendenze circolari
      // Sarà gestito separatamente quando necessario
      console.log('LanguageContext: Language saved to AsyncStorage:', language.code);

      return true;
    } catch (error) {
      console.error('Error saving language:', error);
      return false;
    }
  };

  return (
    <LanguageContext.Provider
      value={{
        currentLanguage,
        setLanguage,
        isLoading
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageContext;
