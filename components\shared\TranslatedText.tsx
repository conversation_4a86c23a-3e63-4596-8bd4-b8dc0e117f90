import React from 'react';
import { Text, TextProps } from 'react-native';
import { useTranslation } from '../../hooks/useTranslation';

interface TranslatedTextProps extends TextProps {
  translationKey: string; // Chiave di traduzione nel formato "section.key" o "section.subsection.key"
  values?: Record<string, string | number>; // Valori da sostituire nel testo tradotto
}

/**
 * Componente che renderizza un testo tradotto in base alla lingua corrente
 */
const TranslatedText: React.FC<TranslatedTextProps> = ({ translationKey, values, ...props }) => {
  const translations = useTranslation();
  
  // Divide la chiave di traduzione in sezioni (es. "settings.title" -> ["settings", "title"])
  const keys = translationKey.split('.');
  
  // Ottiene il testo tradotto navigando nell'oggetto delle traduzioni
  let translatedText: any = translations;
  for (const key of keys) {
    if (translatedText && translatedText[key]) {
      translatedText = translatedText[key];
    } else {
      // Se la chiave non esiste, mostra la chiave stessa
      translatedText = translationKey;
      break;
    }
  }
  
  // Se il testo tradotto è una stringa, sostituisce i valori
  if (typeof translatedText === 'string' && values) {
    Object.entries(values).forEach(([key, value]) => {
      translatedText = translatedText.replace(`{${key}}`, String(value));
    });
  }
  
  return <Text {...props}>{translatedText}</Text>;
};

export default TranslatedText;
