import * as Notifications from 'expo-notifications';

/**
 * Crea una notifica di geofencing
 * @param childName Nome del bambino
 * @param zoneName Nome della zona
 * @param isEntering True se il bambino sta entrando nella zona, false se sta uscendo
 */
export async function createGeofenceNotification(
  childName: string,
  zoneName: string,
  isEntering: boolean
) {
  const title = isEntering
    ? `${childName} has entered a safe zone`
    : `${childName} has left a safe zone`;

  const body = isEntering
    ? `${childName} has entered the "${zoneName}" safe zone`
    : `${childName} has left the "${zoneName}" safe zone`;

  try {
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: {
          type: 'geofence',
          childName,
          zoneName,
          isEntering,
          timestamp: new Date().toISOString(),
        },
      },
      trigger: null, // null significa inviare immediatamente
    });

    console.log(`Geofence notification sent: ${title}`);
    return notificationId;
  } catch (error) {
    console.error('Error sending geofence notification:', error);
    return null;
  }
}

export default createGeofenceNotification;
