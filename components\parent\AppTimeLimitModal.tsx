import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { setAppTimeLimit, removeAppTimeLimit } from '../../utils/supabase';
import {
  AppUsageData,
  AppTimeRestriction,
  saveTimeRestriction,
  getTimeRestrictionsForChild,
} from '../../utils/appUsageService';

interface AppTimeLimitModalProps {
  visible: boolean;
  onClose: () => void;
  childId: string;
  app?: AppUsageData;
  appInfo?: {
    displayName: string;
    packageName: string;
  };
  currentLimit?: number;
  onLimitSet?: (appInfo: { displayName: string, packageName: string }, limitMinutes: number | null) => void;
  onSave?: () => void;
}

export default function AppTimeLimitModal({
  visible,
  onClose,
  childId,
  app,
  appInfo,
  currentLimit,
  onLimitSet,
  onSave,
}: AppTimeLimitModalProps) {
  const [limitMinutes, setLimitMinutes] = useState(currentLimit?.toString() || '');
  const [isLoading, setIsLoading] = useState(false);

  // Determina quale app stiamo gestendo
  const currentApp = app ? {
    displayName: app.appName,
    packageName: app.packageName,
  } : appInfo;

  useEffect(() => {
    // Update the input field when the current limit changes
    setLimitMinutes(currentLimit?.toString() || '');
  }, [currentLimit]);

  const handleSave = async () => {
    if (!currentApp) {
      Alert.alert('Errore', 'Informazioni app non disponibili');
      return;
    }

    if (!limitMinutes.trim()) {
      Alert.alert('Errore', 'Inserisci un limite di tempo');
      return;
    }

    const minutes = parseInt(limitMinutes.trim(), 10);
    if (isNaN(minutes) || minutes <= 0) {
      Alert.alert('Errore', 'Inserisci un limite di tempo valido (maggiore di 0)');
      return;
    }

    setIsLoading(true);
    try {
      // Usa il nome del pacchetto originale per il database
      await setAppTimeLimit(childId, currentApp.packageName, minutes);

      if (onLimitSet) {
        onLimitSet(currentApp, minutes);
      }

      if (onSave) {
        onSave();
      }

      onClose();
    } catch (error) {
      console.error('Error setting app time limit:', error);
      Alert.alert('Errore', 'Impossibile impostare il limite di tempo');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemove = async () => {
    if (!currentLimit || !currentApp) {
      onClose();
      return;
    }

    Alert.alert(
      'Rimuovi limite di tempo',
      `Sei sicuro di voler rimuovere il limite di tempo per ${currentApp.displayName}?`,
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Rimuovi',
          style: 'destructive',
          onPress: async () => {
            setIsLoading(true);
            try {
              // Usa il nome del pacchetto originale per il database
              await removeAppTimeLimit(childId, currentApp.packageName);

              if (onLimitSet) {
                onLimitSet(currentApp, null);
              }

              if (onSave) {
                onSave();
              }

              onClose();
            } catch (error) {
              console.error('Error removing app time limit:', error);
              Alert.alert('Errore', 'Impossibile rimuovere il limite di tempo');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Imposta limite di tempo</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <FontAwesome5 name="times" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <Text style={styles.appName}>{currentApp?.displayName || 'App'}</Text>

            <View style={styles.appIconContainer}>
              <View style={styles.appIconBackground}>
                <FontAwesome5 name="clock" size={24} color="#4630EB" />
              </View>
            </View>

            <Text style={styles.label}>Limite di tempo giornaliero</Text>

            <View style={styles.timeInputContainer}>
              <TextInput
                style={styles.input}
                value={limitMinutes}
                onChangeText={setLimitMinutes}
                keyboardType="number-pad"
                placeholder="Inserisci minuti"
                placeholderTextColor="#999"
              />
              <Text style={styles.minutesLabel}>minuti</Text>
            </View>

            <View style={styles.quickTimeButtons}>
              {[30, 60, 90, 120].map(minutes => (
                <TouchableOpacity
                  key={minutes}
                  style={[
                    styles.quickTimeButton,
                    parseInt(limitMinutes) === minutes ? styles.quickTimeButtonSelected : {}
                  ]}
                  onPress={() => setLimitMinutes(minutes.toString())}
                >
                  <Text
                    style={[
                      styles.quickTimeButtonText,
                      parseInt(limitMinutes) === minutes ? styles.quickTimeButtonTextSelected : {}
                    ]}
                  >
                    {minutes} min
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.helpText}>
              L'app verrà automaticamente bloccata quando viene raggiunto il limite di tempo giornaliero.
            </Text>

            {currentLimit && (
              <View style={styles.currentLimitContainer}>
                <FontAwesome5 name="info-circle" size={16} color="#4630EB" style={styles.infoIcon} />
                <Text style={styles.currentLimitText}>
                  Limite attuale: <Text style={styles.currentLimitValue}>{currentLimit} minuti</Text>
                </Text>
              </View>
            )}
          </View>

          <View style={styles.modalFooter}>
            {currentLimit ? (
              <TouchableOpacity
                style={styles.removeButton}
                onPress={handleRemove}
                disabled={isLoading}
              >
                <FontAwesome5 name="trash-alt" size={16} color="#E74C3C" style={{ marginRight: 8 }} />
                <Text style={styles.removeButtonText}>Rimuovi limite</Text>
              </TouchableOpacity>
            ) : (
              <View style={{ width: 100 }} />
            )}

            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSave}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <>
                  <FontAwesome5 name="check" size={16} color="#FFFFFF" style={{ marginRight: 8 }} />
                  <Text style={styles.saveButtonText}>Applica</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '85%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    padding: 16,
  },
  appName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4630EB',
    marginBottom: 16,
    textAlign: 'center',
  },
  appIconContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  appIconBackground: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 12,
    textAlign: 'center',
  },
  timeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  input: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: '#DDDDDD',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    color: '#333333',
  },
  minutesLabel: {
    fontSize: 16,
    color: '#666666',
    marginLeft: 8,
    width: 60,
  },
  quickTimeButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  quickTimeButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#DDDDDD',
    backgroundColor: '#F5F5F5',
  },
  quickTimeButtonSelected: {
    backgroundColor: '#4630EB',
    borderColor: '#4630EB',
  },
  quickTimeButtonText: {
    fontSize: 14,
    color: '#666666',
  },
  quickTimeButtonTextSelected: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  helpText: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 16,
    textAlign: 'center',
  },
  currentLimitContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F0F0',
    padding: 12,
    borderRadius: 8,
  },
  infoIcon: {
    marginRight: 8,
  },
  currentLimitText: {
    fontSize: 14,
    color: '#666666',
  },
  currentLimitValue: {
    fontWeight: 'bold',
    color: '#4630EB',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  saveButton: {
    backgroundColor: '#4630EB',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  removeButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E74C3C',
  },
  removeButtonText: {
    color: '#E74C3C',
    fontWeight: 'bold',
    fontSize: 16,
  },
});
