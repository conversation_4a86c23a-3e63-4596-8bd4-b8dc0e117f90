import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Platform, Modal, ActivityIndicator, AppState, AppStateStatus } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { openBatteryOptimizationSettings } from '../../utils/systemSettings';
import { isBatteryOptimizationDisabled } from '../../utils/battery';
import DeviceSpecificPermissionHelp from './DeviceSpecificPermissionHelp';

interface BatteryOptimizationPermissionSlideProps {
  onPermissionGranted: () => void;
}

const BatteryOptimizationPermissionSlide: React.FC<BatteryOptimizationPermissionSlideProps> = ({
  onPermissionGranted
}) => {
  const [checking, setChecking] = useState(false);
  const [batteryOptimizationDisabled, setBatteryOptimizationDisabled] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [checkAttempts, setCheckAttempts] = useState(0);

  // Riferimento per tenere traccia dello stato dell'app
  const appState = useRef(AppState.currentState);
  // Flag per tenere traccia se le impostazioni sono state aperte
  const settingsOpened = useRef(false);

  // Controlla periodicamente lo stato dell'autorizzazione
  useEffect(() => {
    // Verifica iniziale
    checkPermission();

    // Imposta un intervallo per verificare periodicamente
    const checkInterval = setInterval(() => {
      if (!batteryOptimizationDisabled && checkAttempts < 10) {
        checkPermission();
        setCheckAttempts(prev => prev + 1);
      } else {
        clearInterval(checkInterval);
      }
    }, 2000); // Controlla ogni 2 secondi

    // Pulizia dell'intervallo
    return () => clearInterval(checkInterval);
  }, [batteryOptimizationDisabled, checkAttempts]);

  // Controlla se l'autorizzazione è stata concessa
  useEffect(() => {
    if (batteryOptimizationDisabled) {
      // NON mostriamo un alert automatico, ma lasciamo che l'utente
      // veda l'indicazione visiva e prema il pulsante per continuare
      console.log('[BatteryOptimizationPermissionSlide] Permission granted, ready to continue');
    }
  }, [batteryOptimizationDisabled]);

  // Listener per lo stato dell'app (in background/foreground)
  useEffect(() => {
    // Funzione per verificare le autorizzazioni quando l'app torna in foreground
    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      // Se l'app torna in foreground da background
      if (appState.current === 'background' && nextAppState === 'active') {
        console.log('[BatteryOptimizationPermissionSlide] App returned to foreground, checking permission');

        // Se le impostazioni sono state aperte, verifichiamo se l'autorizzazione è stata concessa
        if (settingsOpened.current) {
          try {
            console.log('[BatteryOptimizationPermissionSlide] Settings were opened, checking permission');
            setChecking(true);

            // Attendiamo un breve momento per dare tempo al sistema di aggiornare lo stato dell'autorizzazione
            setTimeout(async () => {
              try {
                const isDisabled = await isBatteryOptimizationDisabled();
                console.log('[BatteryOptimizationPermissionSlide] Battery optimization disabled after returning from settings:', isDisabled);

                if (isDisabled) {
                  console.log('[BatteryOptimizationPermissionSlide] Permission granted, updating state');
                  setBatteryOptimizationDisabled(true);
                }
              } catch (error) {
                console.error('[BatteryOptimizationPermissionSlide] Error checking permission after returning from settings:', error);
              } finally {
                setChecking(false);
                // Resettiamo il flag
                settingsOpened.current = false;
              }
            }, 500);
          } catch (error) {
            console.error('[BatteryOptimizationPermissionSlide] Error in app state change handler:', error);
            setChecking(false);
            // Resettiamo il flag
            settingsOpened.current = false;
          }
        }
      } else if (nextAppState === 'background') {
        console.log('[BatteryOptimizationPermissionSlide] App went to background');
      }

      // Aggiorna lo stato corrente dell'app
      appState.current = nextAppState;
    };

    // Aggiungi il listener per lo stato dell'app
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Rimuovi il listener quando il componente viene smontato
    return () => {
      subscription.remove();
    };
  }, []);

  // Verifica lo stato dell'ottimizzazione della batteria
  const checkPermission = async () => {
    try {
      setChecking(true);
      // Su iOS non è necessario disattivare l'ottimizzazione della batteria
      if (Platform.OS === 'ios') {
        setBatteryOptimizationDisabled(true);
        return;
      }

      const isDisabled = await isBatteryOptimizationDisabled();
      console.log('[BatteryOptimizationPermissionSlide] Battery optimization disabled:', isDisabled);

      // Aggiorna lo stato solo se è cambiato
      if (isDisabled !== batteryOptimizationDisabled) {
        setBatteryOptimizationDisabled(isDisabled);
      }
    } catch (error) {
      console.error('[BatteryOptimizationPermissionSlide] Error checking battery optimization:', error);
    } finally {
      setChecking(false);
    }
  };

  // Apre le impostazioni di ottimizzazione della batteria
  const openSettings = async () => {
    try {
      // Impostiamo il flag per indicare che le impostazioni sono state aperte
      settingsOpened.current = true;
      console.log('[BatteryOptimizationPermissionSlide] Opening settings, setting flag');

      const result = await openBatteryOptimizationSettings();

      if (!result) {
        // Se non siamo riusciti ad aprire le impostazioni, resettiamo il flag
        settingsOpened.current = false;
        throw new Error('Failed to open battery optimization settings');
      }

      // Mostriamo un messaggio all'utente
      Alert.alert(
        'Disattiva ottimizzazione',
        'Nelle impostazioni che si apriranno, trova KidSafety nell\'elenco e disattiva l\'ottimizzazione della batteria. Poi torna all\'app.',
        [{ text: 'Ho capito' }]
      );
    } catch (error) {
      console.error('[BatteryOptimizationPermissionSlide] Error opening settings:', error);
      // Resettiamo il flag
      settingsOpened.current = false;
      Alert.alert(
        'Errore',
        'Non è stato possibile aprire le impostazioni. Prova ad aprirle manualmente dalle impostazioni del dispositivo.'
      );
    }
  };

  if (Platform.OS === 'ios') {
    return (
      <View style={styles.container}>
        <View style={styles.statusContainer}>
          <Text style={styles.statusLabel}>Stato autorizzazione:</Text>
          <View style={[styles.statusIndicator, styles.statusGranted]}>
            <Text style={styles.statusText}>Non necessaria su iOS</Text>
          </View>
        </View>

        <Text style={styles.iosMessage}>
          Su iOS non è necessario disattivare l'ottimizzazione della batteria.
        </Text>

        <TouchableOpacity
          style={[styles.button, styles.continueButton]}
          onPress={onPermissionGranted}
        >
          <FontAwesome5 name="check" size={16} color="#FFFFFF" style={styles.buttonIcon} />
          <Text style={styles.buttonText}>Continua</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Stato corrente dell'autorizzazione */}
      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Stato autorizzazione:</Text>
        <View style={[
          styles.statusIndicator,
          batteryOptimizationDisabled ? styles.statusGranted : styles.statusDenied
        ]}>
          <Text style={styles.statusText}>
            {checking ? 'Verifica in corso...' :
              batteryOptimizationDisabled ? 'Concessa' : 'Non concessa'}
          </Text>
          {checking && <ActivityIndicator size="small" color="#FFFFFF" style={styles.statusLoader} />}
        </View>
      </View>

      {/* Pulsante per aprire le impostazioni di sistema */}
      {!batteryOptimizationDisabled && (
        <TouchableOpacity
          style={styles.button}
          onPress={openSettings}
        >
          <FontAwesome5 name="cog" size={16} color="#FFFFFF" style={styles.buttonIcon} />
          <Text style={styles.buttonText}>
            Apri impostazioni di sistema
          </Text>
        </TouchableOpacity>
      )}

      {/* Pulsante per continuare (visibile solo quando l'autorizzazione è concessa) */}
      {batteryOptimizationDisabled && (
        <TouchableOpacity
          style={[styles.button, styles.continueButton]}
          onPress={onPermissionGranted}
        >
          <FontAwesome5 name="check" size={16} color="#FFFFFF" style={styles.buttonIcon} />
          <Text style={styles.buttonText}>Continua</Text>
        </TouchableOpacity>
      )}

      {/* Pulsante per mostrare aiuto */}
      <TouchableOpacity
        style={styles.helpButton}
        onPress={() => setShowHelp(true)}
      >
        <FontAwesome5 name="question-circle" size={16} color="#4630EB" style={styles.buttonIcon} />
        <Text style={styles.helpButtonText}>Aiuto</Text>
      </TouchableOpacity>

      {/* Pulsante per verificare l'autorizzazione */}
      <TouchableOpacity
        style={styles.checkButton}
        onPress={checkPermission}
        disabled={checking}
      >
        <FontAwesome5 name="sync" size={16} color="#FFFFFF" style={[styles.buttonIcon, checking && styles.spinning]} />
        <Text style={styles.buttonText}>Verifica autorizzazione</Text>
      </TouchableOpacity>

      {/* Modal per le istruzioni di aiuto */}
      <Modal
        visible={showHelp}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowHelp(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Come disattivare l'ottimizzazione della batteria</Text>
              <TouchableOpacity
                onPress={() => setShowHelp(false)}
                style={styles.closeButton}
              >
                <FontAwesome5 name="times" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              <DeviceSpecificPermissionHelp permissionType="batteryOptimization" />

              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => {
                  setShowHelp(false); // Chiudiamo il modal prima di aprire le impostazioni
                  openSettings(); // Apriamo le impostazioni
                }}
              >
                <Text style={styles.modalButtonText}>Apri Impostazioni</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    width: '90%',
  },
  statusLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 10,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  statusGranted: {
    backgroundColor: '#4CAF50',
  },
  statusDenied: {
    backgroundColor: '#FF5722',
  },
  statusText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  statusLoader: {
    marginLeft: 5,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4630EB',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginBottom: 12,
    width: '90%',
    justifyContent: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#b0bec5',
  },
  buttonIcon: {
    marginRight: 8,
  },
  spinning: {
    transform: [{ rotate: '45deg' }],
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  helpButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginBottom: 12,
  },
  helpButtonText: {
    color: '#4630EB',
    fontSize: 16,
    fontWeight: 'bold',
  },
  checkButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2196F3',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    width: '90%',
    justifyContent: 'center',
  },
  iosMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  modalContent: {
    padding: 16,
  },
  modalButton: {
    backgroundColor: '#4630EB',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 16,
    alignItems: 'center',
  },
  modalButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  continueButton: {
    backgroundColor: '#4CAF50',
  },
});

export default BatteryOptimizationPermissionSlide;
