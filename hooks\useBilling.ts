import { useState, useEffect, useCallback } from 'react';
import { nativeBillingService, SubscriptionProduct, Purchase } from '../services/nativeBillingService';
import { getSubscriptionSkus, SUBSCRIPTION_SKUS } from '../config/billing';

export interface BillingState {
  isInitialized: boolean;
  isReady: boolean;
  isLoading: boolean;
  products: SubscriptionProduct[];
  purchases: Purchase[];
  error: string | null;
  hasActiveSubscription: boolean;
}

export const useBilling = () => {
  const [state, setState] = useState<BillingState>({
    isInitialized: false,
    isReady: false,
    isLoading: false,
    products: [],
    purchases: [],
    error: null,
    hasActiveSubscription: false,
  });

  // Initialize billing service
  const initialize = useCallback(async () => {
    if (!nativeBillingService.isAvailable()) {
      setState(prev => ({
        ...prev,
        error: 'Billing service not available on this platform'
      }));
      return false;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const success = await nativeBillingService.initialize();

      if (success) {
        setState(prev => ({
          ...prev,
          isInitialized: true,
          isReady: true,
          isLoading: false,
        }));
        return true;
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Failed to initialize billing service'
        }));
        return false;
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
      return false;
    }
  }, []);

  // Load subscription products
  const loadProducts = useCallback(async (productIds: string[] = []) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const skus = productIds.length > 0 ? productIds : getSubscriptionSkus();
      const products = await nativeBillingService.getSubscriptions(skus);
      
      setState(prev => ({
        ...prev,
        products,
        isLoading: false,
      }));
      return products;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load products'
      }));
      return [];
    }
  }, []);

  // Load user purchases
  const loadPurchases = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const purchases = await nativeBillingService.getPurchases();
      const hasActive = purchases.some(p => 
        p.isActive && getSubscriptionSkus().includes(p.productId)
      );

      setState(prev => ({
        ...prev,
        purchases: purchases || [],
        hasActiveSubscription: hasActive,
        isLoading: false,
      }));
      return purchases;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load purchases'
      }));
      return [];
    }
  }, []);

  // Purchase a subscription
  const purchaseSubscription = useCallback(async (productId: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const success = await nativeBillingService.purchaseSubscription(productId);
      setState(prev => ({ ...prev, isLoading: false }));

      // Reload purchases after successful purchase
      if (success) {
        await loadPurchases();
      }

      return { success, message: success ? 'Purchase successful' : 'Purchase failed' };
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Purchase failed'
      }));
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Purchase failed'
      };
    }
  }, [loadPurchases]);

  // Restore purchases
  const restorePurchases = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const success = await nativeBillingService.restorePurchases();
      
      // Reload purchases after restore attempt
      await loadPurchases();
      
      setState(prev => ({ ...prev, isLoading: false }));
      return { success, message: success ? 'Purchases restored' : 'No purchases found' };
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to restore purchases'
      }));
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to restore purchases'
      };
    }
  }, [loadPurchases]);

  // Check if user has active subscription
  const checkHasActiveSubscription = useCallback(() => {
    return state.purchases.some(purchase => 
      purchase.isActive && 
      getSubscriptionSkus().includes(purchase.productId)
    );
  }, [state.purchases]);

  // Get active subscription
  const getActiveSubscription = useCallback(() => {
    return state.purchases.find(purchase => 
      purchase.isActive && 
      getSubscriptionSkus().includes(purchase.productId)
    );
  }, [state.purchases]);

  // Check if a specific product is active
  const isProductActive = useCallback((productId: string) => {
    return state.purchases.some(purchase => 
      purchase.isActive && 
      purchase.productId === productId
    );
  }, [state.purchases]);

  // Check if a product has a free trial period
  const hasFreeTrial = useCallback((productId: string) => {
    const product = state.products.find(p => p.productId === productId);
    return !!product?.freeTrialPeriod;
  }, [state.products]);

  // Get free trial period formatted for display
  const getFreeTrialPeriod = useCallback((productId: string) => {
    const product = state.products.find(p => p.productId === productId);
    if (!product?.freeTrialPeriod) return null;
    
    // Free trial period comes in format like "P3D" (3 days)
    const match = product.freeTrialPeriod.match(/P(\d+)([DWM])/);
    if (!match) return null;
    
    const [_, amount, unit] = match;
    
    switch (unit) {
      case 'D':
        return `${amount} ${Number(amount) === 1 ? 'giorno' : 'giorni'} gratis`;
      case 'W':
        return `${amount} ${Number(amount) === 1 ? 'settimana' : 'settimane'} gratis`;
      case 'M':
        return `${amount} ${Number(amount) === 1 ? 'mese' : 'mesi'} gratis`;
      default:
        return `${amount} ${unit} gratis`;
    }
  }, [state.products]);

  // Initialize on mount
  useEffect(() => {
    const initAsync = async () => {
      const initialized = await initialize();
      if (initialized) {
        await loadProducts();
        await loadPurchases();
      }
    };

    initAsync();

    // Clean up on unmount
    return () => {
      nativeBillingService.disconnect();
    };
  }, [initialize, loadProducts, loadPurchases]);

  return {
    ...state,
    initialize,
    loadProducts,
    loadPurchases,
    purchaseSubscription,
    restorePurchases,
    checkHasActiveSubscription,
    getActiveSubscription,
    isProductActive,
    hasFreeTrial,
    getFreeTrialPeriod,
    // Convenience getters for subscription types
    isMonthlyActive: isProductActive(SUBSCRIPTION_SKUS.MONTHLY),
    isYearlyActive: isProductActive(SUBSCRIPTION_SKUS.YEARLY),
    monthlyProduct: state.products.find(p => p.productId === SUBSCRIPTION_SKUS.MONTHLY),
    yearlyProduct: state.products.find(p => p.productId === SUBSCRIPTION_SKUS.YEARLY),
  };
};
