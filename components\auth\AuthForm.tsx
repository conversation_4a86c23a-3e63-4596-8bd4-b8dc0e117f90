import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import Button from '../shared/Button';

interface FormField {
  key: string;
  label: string;
  placeholder: string;
  secureTextEntry?: boolean;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCorrect?: boolean;
}

interface AuthFormProps {
  formType: 'login' | 'signup' | 'childLogin';
  onSubmit: (values: Record<string, string>) => void;
  isLoading?: boolean;
  additionalFields?: FormField[];
  error?: string | null;
  onToggleForm?: () => void;
}

const AuthForm: React.FC<AuthFormProps> = ({
  formType,
  onSubmit,
  isLoading = false,
  additionalFields = [],
  error,
  onToggleForm,
}) => {
  // Define default fields based on form type
  const getDefaultFields = (): FormField[] => {
    switch (formType) {
      case 'login':
        return [
          {
            key: 'email',
            label: 'Email',
            placeholder: 'Enter your email',
            autoCapitalize: 'none',
            keyboardType: 'email-address',
            autoCorrect: false,
          },
          {
            key: 'password',
            label: 'Password',
            placeholder: 'Enter your password',
            secureTextEntry: true,
            autoCapitalize: 'none',
            autoCorrect: false,
          },
        ];
      case 'signup':
        return [
          {
            key: 'name',
            label: 'Name',
            placeholder: 'Enter your name',
            autoCorrect: false,
          },
          {
            key: 'email',
            label: 'Email',
            placeholder: 'Enter your email',
            autoCapitalize: 'none',
            keyboardType: 'email-address',
            autoCorrect: false,
          },
          {
            key: 'password',
            label: 'Password',
            placeholder: 'Create a strong password',
            secureTextEntry: true,
            autoCapitalize: 'none',
            autoCorrect: false,
          },
          {
            key: 'confirmPassword',
            label: 'Confirm Password',
            placeholder: 'Confirm your password',
            secureTextEntry: true,
            autoCapitalize: 'none',
            autoCorrect: false,
          },
        ];
      case 'childLogin':
        return [
          {
            key: 'token',
            label: 'Access Code',
            placeholder: 'Enter your token (e.g., ABC123)',
            autoCapitalize: 'characters',
            autoCorrect: false,
          },
        ];
      default:
        return [];
    }
  };

  const fields = [...getDefaultFields(), ...additionalFields];

  // State to manage form values
  const [values, setValues] = useState<Record<string, string>>(
    fields.reduce((acc, field) => ({ ...acc, [field.key]: '' }), {})
  );

  // Handle input changes
  const handleChange = (key: string, value: string) => {
    setValues((prev) => ({ ...prev, [key]: value }));
  };

  // Handle form submission
  const handleSubmit = () => {
    // Basic validation
    for (const field of fields) {
      if (!values[field.key]) {
        Alert.alert('Validation Error', `Please enter your ${field.label.toLowerCase()}`);
        return;
      }
    }

    // Email validation for login and signup
    if ((formType === 'login' || formType === 'signup') && values.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(values.email)) {
        Alert.alert('Validation Error', 'Please enter a valid email address');
        return;
      }
    }

    // Password validation for login and signup
    if ((formType === 'login' || formType === 'signup') && values.password) {
      if (values.password.length < 6) {
        Alert.alert('Validation Error', 'Password must be at least 6 characters long');
        return;
      }
    }

    // Confirm password validation for signup
    if (formType === 'signup' && values.password !== values.confirmPassword) {
      Alert.alert('Validation Error', 'Passwords do not match');
      return;
    }

    // Create a copy of values without confirmPassword for submission
    const submissionValues = { ...values };
    if (formType === 'signup') {
      delete submissionValues.confirmPassword;
    }

    // Submit form
    onSubmit(submissionValues);
  };

  // Helper functions for form texts and labels
  const getFormTitle = () => {
    switch (formType) {
      case 'login':
        return 'Parent Login';
      case 'signup':
        return 'Create Parent Account';
      case 'childLogin':
        return 'Child Login';
      default:
        return '';
    }
  };

  const getButtonTitle = () => {
    switch (formType) {
      case 'login':
        return 'Login';
      case 'signup':
        return 'Sign Up';
      case 'childLogin':
        return 'Login';
      default:
        return '';
    }
  };

  const getToggleText = () => {
    switch (formType) {
      case 'login':
        return 'Don\'t have an account? Sign Up';
      case 'signup':
        return 'Already have an account? Login';
      default:
        return '';
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.title}>{getFormTitle()}</Text>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {fields.map((field) => (
          <View key={field.key} style={styles.inputContainer}>
            <Text style={styles.label}>{field.label}</Text>
            <TextInput
              style={styles.input}
              placeholder={field.placeholder}
              value={values[field.key]}
              onChangeText={(text) => handleChange(field.key, text)}
              secureTextEntry={field.secureTextEntry}
              autoCapitalize={field.autoCapitalize}
              keyboardType={field.keyboardType}
              autoCorrect={field.autoCorrect}
            />
          </View>
        ))}

        <View style={styles.buttonContainer}>
          <Button
            title={getButtonTitle()}
            onPress={handleSubmit}
            isLoading={isLoading}
            fullWidth
            style={styles.button}
          />
        </View>

        {onToggleForm && formType !== 'childLogin' && (
          <TouchableOpacity onPress={onToggleForm} style={styles.toggleContainer}>
            <Text style={styles.toggleText}>{getToggleText()}</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  scrollContent: {
    padding: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 24,
    color: '#333',
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#555',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#DDDDDD',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
  buttonContainer: {
    marginTop: 8,
    marginBottom: 24,
  },
  button: {
    height: 54,
  },
  toggleContainer: {
    alignItems: 'center',
  },
  toggleText: {
    fontSize: 16,
    color: '#4630EB',
  },
  errorContainer: {
    backgroundColor: '#FFEEEE',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#FFCCCC',
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 14,
  },
});

export default AuthForm;