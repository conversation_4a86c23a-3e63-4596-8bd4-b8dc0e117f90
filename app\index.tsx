import React, { useEffect } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../contexts/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function Index() {
  console.log('🔥 Index page loaded - FULL VERSION WITH AUTH PROVIDER');
  const router = useRouter();
  const { user, userType, isLoading } = useAuth();

  useEffect(() => {
    const handleNavigation = async () => {
      // Aspetta che l'auth sia pronto
      if (isLoading) return;

      try {
        // Controlla se l'utente ha visto l'onboarding
        const hasSeenOnboarding = await AsyncStorage.getItem('hasSeenOnboarding');
        console.log('🧭 Index: Navigation check', {
          hasSeenOnboarding: hasSeenOnboarding === 'true',
          user: !!user,
          userType
        });

        // Se non ha visto l'onboarding
        if (hasSeenOnboarding !== 'true') {
          console.log('🚀 Index: Navigating to onboarding');
          router.replace('/auth/onboarding');
          return;
        }

        // Se ha visto l'onboarding ma non è loggato
        if (!user) {
          console.log('🚀 Index: Navigating to parent login');
          router.replace('/auth/parent-login');
          return;
        }

        // Se è loggato, vai alla home appropriata
        if (userType === 'parent') {
          console.log('🚀 Index: Navigating to parent home');
          router.replace('/(parent-tabs)/home');
        } else if (userType === 'child') {
          console.log('🚀 Index: Navigating to child dashboard');
          router.replace('/(child-tabs)/dashboard');
        }
      } catch (error) {
        console.error('❌ Index: Navigation error:', error);
        router.replace('/auth/onboarding');
      }
    };

    handleNavigation();
  }, [isLoading, user, userType, router]);

  // Mostra una schermata di caricamento minima
  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#FFFFFF'
    }}>
      <ActivityIndicator size="large" color="#4630EB" />
    </View>
  );
}
