import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, Text } from 'react-native';
import { useRouter, useSegments } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../../contexts/AuthContext';

interface NavigationHandlerProps {
  children: React.ReactNode;
}

export default function NavigationHandler({ children }: NavigationHandlerProps) {
  const [hasSeenOnboarding, setHasSeenOnboarding] = useState<boolean | null>(null);
  const [isNavigationReady, setIsNavigationReady] = useState(false);
  const { user, userType, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const segments = useSegments();

  // Check onboarding status
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        console.log('🔍 NavigationHandler: Checking onboarding status...');
        const onboardingStatus = await AsyncStorage.getItem('hasSeenOnboarding');
        const hasSeenIt = onboardingStatus === 'true';
        console.log('📋 NavigationHandler: Onboarding status:', hasSeenIt);
        setHasSeenOnboarding(hasSeenIt);
      } catch (error) {
        console.error('❌ NavigationHandler: Error checking onboarding status:', error);
        setHasSeenOnboarding(false);
      }
    };

    checkOnboardingStatus();
  }, []);

  // Handle navigation logic
  useEffect(() => {
    if (authLoading || hasSeenOnboarding === null) {
      console.log('⏳ NavigationHandler: Still loading...', { authLoading, hasSeenOnboarding });
      return;
    }

    console.log('🧭 NavigationHandler: Navigation logic triggered', {
      hasSeenOnboarding,
      user: !!user,
      userType,
      currentSegments: segments,
    });

    // Delay navigation to ensure layout is mounted
    const timer = setTimeout(() => {
      try {
        // If user hasn't seen onboarding and not already on onboarding page
        if (!hasSeenOnboarding && segments[0] !== 'onboarding') {
          console.log('🚀 NavigationHandler: Navigating to onboarding');
          router.replace('/onboarding');
          setIsNavigationReady(true);
          return;
        }

        // If user has seen onboarding but no user is logged in
        if (hasSeenOnboarding && !user) {
          // If not already on auth pages
          if (segments[0] !== 'auth') {
            console.log('🚀 NavigationHandler: Navigating to parent login');
            router.replace('/auth/parent-login');
            setIsNavigationReady(true);
            return;
          }
        }

        // If user is logged in
        if (user && userType) {
          // If on auth pages or index, redirect to appropriate home
          if (segments[0] === 'auth' || segments[0] === 'index' || segments.length === 0) {
            if (userType === 'parent') {
              console.log('🚀 NavigationHandler: Navigating to parent home');
              router.replace('/(parent-tabs)/home');
            } else if (userType === 'child') {
              console.log('🚀 NavigationHandler: Navigating to child dashboard');
              router.replace('/(child-tabs)/dashboard');
            }
            setIsNavigationReady(true);
            return;
          }
        }

        // If we reach here, navigation is ready
        setIsNavigationReady(true);
      } catch (error) {
        console.error('❌ NavigationHandler: Navigation error:', error);
        setIsNavigationReady(true);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [hasSeenOnboarding, user, userType, authLoading, segments, router]);

  // Show loading screen while determining navigation
  if (authLoading || hasSeenOnboarding === null || !isNavigationReady) {
    return (
      <View style={{ 
        flex: 1, 
        justifyContent: 'center', 
        alignItems: 'center', 
        backgroundColor: '#FFFFFF'
      }}>
        <ActivityIndicator size="large" color="#4630EB" />
        <Text style={{
          marginTop: 16,
          fontSize: 16,
          color: '#666',
          fontWeight: '500'
        }}>
          Inizializzazione...
        </Text>
      </View>
    );
  }

  return <>{children}</>;
}
