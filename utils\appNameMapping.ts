/**
 * Utility per mappare i nomi dei pacchetti Android ai nomi leggibili delle app
 */

// Mappa dei nomi dei pacchetti comuni alle app
export const packageNameToAppName: { [key: string]: string } = {
  // Launcher e sistema
  'com.zte.mifavor.launcher': 'Home Screen',
  'com.android.launcher': 'Home Screen',
  'com.android.launcher2': 'Home Screen',
  'com.android.launcher3': 'Home Screen',
  'com.google.android.apps.nexuslauncher': 'Home Screen',
  'com.miui.home': 'Home Screen (MIUI)',
  'com.sec.android.app.launcher': 'Home Screen (Samsung)',
  'com.huawei.android.launcher': 'Home Screen (Huawei)',
  'com.oneplus.launcher': 'Home Screen (OnePlus)',
  'com.android.systemui': 'System UI',
  
  // Google apps
  'com.google.android.youtube': 'YouTube',
  'com.google.android.apps.youtube.music': 'YouTube Music',
  'com.google.android.gm': 'Gmail',
  'com.google.android.googlequicksearchbox': 'Google',
  'com.google.android.apps.photos': 'Google Photos',
  'com.google.android.apps.maps': 'Google Maps',
  'com.google.android.apps.docs': 'Google Drive',
  'com.google.android.apps.docs.editors.docs': 'Google Docs',
  'com.google.android.apps.docs.editors.sheets': 'Google Sheets',
  'com.google.android.apps.docs.editors.slides': 'Google Slides',
  'com.google.android.apps.translate': 'Google Translate',
  'com.google.android.apps.messaging': 'Google Messages',
  'com.google.android.apps.tachyon': 'Google Meet',
  'com.google.android.apps.nbu.files': 'Files by Google',
  'com.google.android.apps.wellbeing': 'Digital Wellbeing',
  'com.google.android.calendar': 'Google Calendar',
  'com.google.android.deskclock': 'Clock',
  'com.google.android.apps.googleassistant': 'Google Assistant',
  'com.google.android.apps.subscriptions.red': 'Google One',
  'com.google.android.inputmethod.latin': 'Gboard',
  
  // Social media
  'com.facebook.katana': 'Facebook',
  'com.facebook.lite': 'Facebook Lite',
  'com.facebook.orca': 'Messenger',
  'com.facebook.mlite': 'Messenger Lite',
  'com.instagram.android': 'Instagram',
  'com.zhiliaoapp.musically': 'TikTok',
  'com.ss.android.ugc.trill': 'TikTok',
  'com.twitter.android': 'Twitter',
  'com.snapchat.android': 'Snapchat',
  'com.pinterest': 'Pinterest',
  'com.linkedin.android': 'LinkedIn',
  'com.reddit.frontpage': 'Reddit',
  'com.discord': 'Discord',
  
  // Messaging
  'com.whatsapp': 'WhatsApp',
  'com.whatsapp.w4b': 'WhatsApp Business',
  'org.telegram.messenger': 'Telegram',
  'org.thoughtcrime.securesms': 'Signal',
  'com.viber.voip': 'Viber',
  'kik.android': 'Kik',
  'jp.naver.line.android': 'LINE',
  'com.skype.raider': 'Skype',
  
  // Streaming e intrattenimento
  'com.spotify.music': 'Spotify',
  'com.netflix.mediaclient': 'Netflix',
  'com.amazon.avod.thirdpartyclient': 'Prime Video',
  'com.disney.disneyplus': 'Disney+',
  'com.hbo.hbonow': 'HBO Max',
  'com.hulu.plus': 'Hulu',
  'tv.twitch.android.app': 'Twitch',
  'com.vimeo.android.videoapp': 'Vimeo',
  'com.pandora.android': 'Pandora',
  'com.apple.android.music': 'Apple Music',
  'deezer.android.app': 'Deezer',
  'com.tidal.wave': 'Tidal',
  
  // Browser
  'com.android.chrome': 'Chrome',
  'org.mozilla.firefox': 'Firefox',
  'com.opera.browser': 'Opera',
  'com.opera.mini.native': 'Opera Mini',
  'com.microsoft.emmx': 'Edge',
  'com.brave.browser': 'Brave',
  'com.duckduckgo.mobile.android': 'DuckDuckGo',
  
  // Giochi
  'com.mojang.minecraftpe': 'Minecraft',
  'com.roblox.client': 'Roblox',
  'com.epicgames.fortnite': 'Fortnite',
  'com.supercell.clashofclans': 'Clash of Clans',
  'com.supercell.clashroyale': 'Clash Royale',
  'com.king.candycrushsaga': 'Candy Crush Saga',
  'com.rovio.angrybirds': 'Angry Birds',
  'com.kiloo.subwaysurf': 'Subway Surfers',
  'com.imangi.templerun2': 'Temple Run 2',
  'com.halfbrick.fruitninja': 'Fruit Ninja',
  
  // App di sistema
  'com.android.settings': 'Impostazioni',
  'com.android.vending': 'Google Play Store',
  'com.android.camera': 'Fotocamera',
  'com.android.gallery3d': 'Galleria',
  'com.google.android.apps.photos': 'Google Foto',
  'com.sec.android.app.camera': 'Fotocamera (Samsung)',
  'com.sec.android.gallery3d': 'Galleria (Samsung)',
  'com.android.contacts': 'Contatti',
  'com.android.phone': 'Telefono',
  'com.android.mms': 'Messaggi',
  'com.android.email': 'Email',
  'com.android.calculator2': 'Calcolatrice',
  'com.android.calendar': 'Calendario',
  'com.android.browser': 'Browser',
  'com.android.documentsui': 'File',
  'com.android.music': 'Musica',
  'com.android.videos': 'Video',
  
  // App di test
  'TEST_APP': 'App di Test',
  'FALLBACK_TEST_APP': 'App di Test (Fallback)'
};

/**
 * Ottiene il nome leggibile di un'app dal nome del pacchetto
 * @param packageName - Il nome del pacchetto Android
 * @returns Il nome leggibile dell'app o il nome del pacchetto se non trovato
 */
export const getReadableAppName = (packageName: string): string => {
  // Controlla se abbiamo una mappatura diretta
  if (packageNameToAppName[packageName]) {
    return packageNameToAppName[packageName];
  }
  
  // Controlla se il nome del pacchetto inizia con uno dei prefissi noti
  for (const [key, value] of Object.entries(packageNameToAppName)) {
    if (packageName.startsWith(key + '.')) {
      return value;
    }
  }
  
  // Se non troviamo una corrispondenza, proviamo a rendere il nome del pacchetto più leggibile
  if (packageName.includes('.')) {
    // Prendi l'ultima parte del nome del pacchetto
    const parts = packageName.split('.');
    let appName = parts[parts.length - 1];
    
    // Capitalizza la prima lettera e sostituisci i caratteri underscore con spazi
    appName = appName.charAt(0).toUpperCase() + appName.slice(1).replace(/_/g, ' ');
    
    return appName;
  }
  
  // Se tutto fallisce, restituisci il nome del pacchetto originale
  return packageName;
};
