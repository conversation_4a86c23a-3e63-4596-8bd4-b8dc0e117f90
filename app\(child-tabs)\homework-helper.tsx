import React, { useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, Alert } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslations } from '../../contexts/TranslationContext';
import Header from '../../components/shared/Header';
import HomeworkSession from '../../components/homework/HomeworkSession';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import config from '../../config';

export default function HomeworkHelperScreen() {
  const { user, isLoading } = useAuth();
  const { t } = useTranslations();

  // Verifica la configurazione all'avvio del componente
  useEffect(() => {
    // Controlla se la chiave API è presente
    if (!config.anthropicApiKey) {
      console.warn('Anthropic API key is missing in config');
    } else {
      console.log('Anthropic API key is configured:', config.anthropicApiKey.substring(0, 10) + '...');
    }
  }, []);

  if (isLoading) {
    return <LoadingIndicator fullScreen text={t.homework?.loading || "Caricamento..."} />;
  }

  if (!user) {
    return (
      <View style={styles.container}>
        <Header title={t.homework?.title || "Assistente Compiti"} />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {t.homework?.loginRequired || "Devi effettuare l'accesso per utilizzare questa funzionalità."}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header title={t.homework?.title || "Assistente Compiti"} />
      <HomeworkSession childId={user.id} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
  },
});
