import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Mission } from '../../contexts/MissionsContext';

type MissionItemProps = {
  mission: Mission;
  childName?: string;
  status?: 'assigned' | 'in_progress' | 'completed' | 'verified';
  onPress?: () => void;
};

export default function MissionItem({ mission, childName, status, onPress }: MissionItemProps) {
  // Format due date
  const formatDueDate = (dateString?: string) => {
    if (!dateString) return 'No due date';
    
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Get status icon and color
  const getStatusInfo = () => {
    switch (status) {
      case 'assigned':
        return { icon: 'time-outline', color: '#FFA500' };
      case 'in_progress':
        return { icon: 'play-circle-outline', color: '#4630EB' };
      case 'completed':
        return { icon: 'checkmark-circle-outline', color: '#28A745' };
      case 'verified':
        return { icon: 'shield-checkmark-outline', color: '#28A745' };
      default:
        return { icon: 'help-circle-outline', color: '#6C757D' };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.iconContainer}>
        <Ionicons 
          name={statusInfo.icon as any} 
          size={24} 
          color={statusInfo.color} 
        />
      </View>
      
      <View style={styles.contentContainer}>
        <Text style={styles.title}>{mission.title}</Text>
        
        {mission.description && (
          <Text style={styles.description} numberOfLines={2}>
            {mission.description}
          </Text>
        )}
        
        <View style={styles.detailsContainer}>
          {mission.due_date && (
            <View style={styles.detailItem}>
              <Ionicons name="calendar-outline" size={14} color="#6C757D" />
              <Text style={styles.detailText}>
                Due: {formatDueDate(mission.due_date)}
              </Text>
            </View>
          )}
          
          {mission.reward && (
            <View style={styles.detailItem}>
              <Ionicons name="gift-outline" size={14} color="#6C757D" />
              <Text style={styles.detailText}>
                Reward: {mission.reward}
              </Text>
            </View>
          )}

          {childName && (
            <View style={styles.detailItem}>
              <Ionicons name="person-outline" size={14} color="#6C757D" />
              <Text style={styles.detailText}>
                Assigned to: {childName}
              </Text>
            </View>
          )}

          {status && (
            <View style={[styles.statusBadge, { backgroundColor: statusInfo.color + '20' }]}>
              <Text style={[styles.statusText, { color: statusInfo.color }]}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </Text>
            </View>
          )}
        </View>
      </View>
      
      <Ionicons name="chevron-forward" size={20} color="#A0A0A0" />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  detailsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
    marginBottom: 4,
  },
  detailText: {
    fontSize: 12,
    color: '#6C757D',
    marginLeft: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginLeft: 'auto',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
}); 