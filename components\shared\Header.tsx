import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTranslations } from '../../contexts/TranslationContext';

interface HeaderProps {
  title?: string;
  titleKey?: string; // Chiave di traduzione per il titolo
  showBackButton?: boolean;
  rightComponent?: React.ReactNode;
  onBackPress?: () => void;
  style?: ViewStyle;
}

const Header: React.FC<HeaderProps> = ({
  title,
  titleKey,
  showBackButton = false,
  rightComponent,
  onBackPress,
  style,
}) => {
  const router = useRouter();
  const { t } = useTranslations();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.leftContainer}>
        {showBackButton && (
          <TouchableOpacity
            onPress={handleBackPress}
            style={styles.backButton}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="chevron-back" size={28} color="#333" />
          </TouchableOpacity>
        )}
      </View>

      <Text style={styles.title} numberOfLines={1}>
        {titleKey ? (
          (() => {
            try {
              const parts = titleKey.split('.');
              if (parts.length === 2 && t[parts[0]] && t[parts[0]][parts[1]]) {
                return t[parts[0]][parts[1]];
              } else {
                console.warn(`Translation key not found: ${titleKey}`);
                return title || titleKey || '';
              }
            } catch (error) {
              console.error(`Error getting translation for key ${titleKey}:`, error);
              return title || titleKey || '';
            }
          })()
        ) : (title || '')}
      </Text>

      <View style={styles.rightContainer}>
        {rightComponent}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  leftContainer: {
    width: 40,
    alignItems: 'flex-start',
  },
  rightContainer: {
    width: 40,
    alignItems: 'flex-end',
  },
  title: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    textAlign: 'center',
  },
  backButton: {
    padding: 3,
  },
});

export default Header;