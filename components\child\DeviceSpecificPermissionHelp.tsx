import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions, Modal, TouchableOpacity } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { getDeviceUIType } from '../../utils/deviceInfo';
import { getPermissionInstructions, PermissionInstructions } from '../../utils/permissionInstructions';

const { width } = Dimensions.get('window');

interface DeviceSpecificPermissionHelpProps {
  permissionType: 'usageStats' | 'accessibility' | 'batteryOptimization';
  visible?: boolean;
  onClose?: () => void;
  onOpenSettings?: () => void;
}

const DeviceSpecificPermissionHelp: React.FC<DeviceSpecificPermissionHelpProps> = ({
  permissionType,
  visible = false,
  onClose,
  onOpenSettings
}) => {
  const [deviceType, setDeviceType] = useState<'samsung' | 'xiaomi' | 'huawei' | 'oppo' | 'oneplus' | 'google' | 'motorola' | 'generic'>('generic');
  const [instructions, setInstructions] = useState<PermissionInstructions | null>(null);

  // Rileva il tipo di dispositivo all'avvio
  useEffect(() => {
    try {
      const detectedDeviceType = getDeviceUIType();
      setDeviceType(detectedDeviceType);

      // Ottieni le istruzioni specifiche per questo dispositivo
      setInstructions(getPermissionInstructions(permissionType, detectedDeviceType));
    } catch (error) {
      console.error('Error detecting device type:', error);
      setDeviceType('generic');
      setInstructions(getPermissionInstructions(permissionType, 'generic'));
    }
  }, [permissionType]);

  // Contenuto delle istruzioni
  const renderInstructions = () => {
    if (!instructions) {
      return (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Caricamento istruzioni...</Text>
        </View>
      );
    }

    return (
      <View style={styles.instructionsContainer}>
        <Text style={styles.deviceType}>Istruzioni per {deviceType}</Text>
        <Text style={styles.title}>{instructions.title}</Text>
        <Text style={styles.description}>{instructions.description}</Text>

        <ScrollView style={styles.stepsScrollView}>
          {instructions.steps.map((step, index) => (
            <View key={index} style={styles.instructionStep}>
              <View style={styles.stepNumberContainer}>
                <Text style={styles.stepNumber}>{index + 1}</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>{step.title}</Text>
                {step.description && (
                  <Text style={styles.stepDescription}>{step.description}</Text>
                )}
                <View style={styles.menuItemsContainer}>
                  {step.menuItems.map((item, itemIndex) => (
                    <View key={itemIndex} style={styles.menuItem}>
                      <Text style={styles.menuItemText}>{item}</Text>
                      {itemIndex < step.menuItems.length - 1 && (
                        <Text style={styles.menuItemOr}>oppure</Text>
                      )}
                    </View>
                  ))}
                </View>
                {step.finalAction && (
                  <View style={styles.finalActionContainer}>
                    <FontAwesome5 name="hand-point-right" size={16} color="#FF9800" />
                    <Text style={styles.finalActionText}>{step.finalAction}</Text>
                  </View>
                )}
              </View>
            </View>
          ))}
        </ScrollView>

        {onOpenSettings && (
          <TouchableOpacity style={styles.openSettingsButton} onPress={onOpenSettings}>
            <FontAwesome5 name="cog" size={16} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.openSettingsButtonText}>Apri Impostazioni</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // Se non è in modalità modale, mostra direttamente le istruzioni
  if (!visible || !onClose) {
    return (
      <View style={styles.container}>
        {renderInstructions()}
      </View>
    );
  }

  // Altrimenti, mostra le istruzioni in un modale
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Istruzioni</Text>
            <TouchableOpacity
              onPress={onClose}
              style={styles.closeButton}
            >
              <FontAwesome5 name="times" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            {renderInstructions()}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    width: width - 40,
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginVertical: 20,
  },
  instructionsContainer: {
    width: '100%',
  },
  loadingContainer: {
    width: width - 40,
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    marginVertical: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  deviceType: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginBottom: 8,
    fontStyle: 'italic',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  stepsScrollView: {
    maxHeight: 350,
  },
  instructionStep: {
    flexDirection: 'row',
    marginBottom: 16,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#eee',
  },
  stepNumberContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#4630EB',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumber: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  menuItemsContainer: {
    marginTop: 8,
  },
  menuItem: {
    marginBottom: 4,
  },
  menuItemText: {
    fontSize: 14,
    color: '#4630EB',
    fontWeight: '500',
  },
  menuItemOr: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
    marginLeft: 8,
    marginTop: 2,
  },
  finalActionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    backgroundColor: '#FFF9C4',
    padding: 8,
    borderRadius: 4,
  },
  finalActionText: {
    fontSize: 14,
    color: '#FF9800',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  openSettingsButton: {
    backgroundColor: '#4630EB',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    alignSelf: 'center',
  },
  openSettingsButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  modalContent: {
    flex: 1,
  },
});

export default DeviceSpecificPermissionHelp;
