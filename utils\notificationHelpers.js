import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';

// Format a notification message
export const formatNotificationMessage = (type, data) => {
  switch (type) {
    case 'sos':
      return {
        title: 'SOS Alert!',
        body: `${data.childName} has triggered an SOS alert!`,
      };
    case 'geofence_enter':
      return {
        title: `${data.childName} has entered a safe zone`,
        body: `${data.childName} has entered the "${data.zoneName}" safe zone`,
      };
    case 'geofence_exit':
      return {
        title: `${data.childName} has left a safe zone`,
        body: `${data.childName} has left the "${data.zoneName}" safe zone`,
      };
    case 'mission_assigned':
      return {
        title: `New mission assigned${data.assignedTo ? ` to ${data.assignedTo}` : ''}`,
        body: `"${data.missionTitle}" has been assigned. Tap to see details.`,
      };
    case 'mission_completed':
      return {
        title: `Mission completed by ${data.childName}`,
        body: `${data.childN<PERSON>} has completed "${data.missionTitle}"! Tap to verify.`,
      };
    case 'battery_low':
      return {
        title: `${data.childName}'s device battery is low`,
        body: `${data.childName}'s device battery is at ${data.batteryLevel}%. Please remind them to charge their device.`,
      };
    default:
      return {
        title: data.title || 'Notification',
        body: data.body || 'You have a new notification',
      };
  }
};

// Check if the device is a physical device
export const isPhysicalDevice = async () => {
  return Device.isDevice;
};

// Check if notifications are permitted
export const checkNotificationPermissions = async () => {
  if (Platform.OS === 'android') {
    return true; // Android always returns true for this check
  }
  
  const { status } = await Notifications.getPermissionsAsync();
  return status === 'granted';
};

// Request notification permissions
export const requestNotificationPermissions = async () => {
  if (Platform.OS === 'android') {
    return true; // Android always returns true for this request
  }
  
  const { status } = await Notifications.requestPermissionsAsync();
  return status === 'granted';
};

// Parse notification data
export const parseNotificationData = (notification) => {
  if (!notification || !notification.request) {
    return null;
  }
  
  const { data } = notification.request.content;
  return data;
};

// Handle notification response
export const handleNotificationResponse = (response) => {
  if (!response || !response.notification) {
    return null;
  }
  
  const data = parseNotificationData(response.notification);
  return {
    data,
    actionIdentifier: response.actionIdentifier,
  };
};

// Default export to satisfy Expo Router
export default {
  formatNotificationMessage,
  isPhysicalDevice,
  checkNotificationPermissions,
  requestNotificationPermissions,
  parseNotificationData,
  handleNotificationResponse
};
