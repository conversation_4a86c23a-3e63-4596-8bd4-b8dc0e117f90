import React, { ReactNode } from 'react';
import { useLanguage } from './LanguageContext';
import { getTranslations } from '../utils/translations';
import TranslationContext from './TranslationContext';

// Props per il provider
interface TranslationProviderProps {
  children: ReactNode;
}

// Provider delle traduzioni
export const TranslationProvider: React.FC<TranslationProviderProps> = ({ children }) => {
  const { currentLanguage } = useLanguage();

  // Ottieni le traduzioni direttamente senza useState per evitare problemi di aggiornamento
  const translations = getTranslations(currentLanguage.code);

  // Log per debug
  console.log('TranslationProvider rendering with language:', currentLanguage.code);
  console.log('TranslationProvider has translations for settings.title:', translations?.settings?.title || 'missing');

  return (
    <TranslationContext.Provider value={{ t: translations, currentLanguage }}>
      {children}
    </TranslationContext.Provider>
  );
};

export default TranslationProvider;
