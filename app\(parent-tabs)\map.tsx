import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Modal } from 'react-native';
import { useRouter } from 'expo-router';
import { FontAwesome5 } from '@expo/vector-icons';
import { Mark<PERSON>, Circle } from 'react-native-maps';
import GoogleMapView from '../../components/shared/GoogleMapView';
import { useAuth } from '../../contexts/AuthContext';
import { getChildrenForParent, getChildLocation } from '../../utils/supabase';
import Header from '../../components/shared/Header';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import ChildRouteMap from '../../components/parent/ChildRouteMap';

interface Child {
  child_id: string;
  users: {
    name: string;
  };
  location?: {
    latitude: number;
    longitude: number;
    timestamp: string;
  };
}

function MapScreen() {
  const { user } = useAuth();
  const router = useRouter();
  const [children, setChildren] = useState<Child[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const mapRef = useRef<any>(null);
  const [region, setRegion] = useState({
    latitude: 41.9028, // Default to Rome, Italy
    longitude: 12.4964,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  // Stato per la visualizzazione dei tragitti
  const [showRoutes, setShowRoutes] = useState(false);
  const [selectedChild, setSelectedChild] = useState<Child | null>(null);

  // Riferimento per l'intervallo di aggiornamento
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    fetchData();

    // Imposta un intervallo per aggiornare i dati ogni 5 secondi
    refreshIntervalRef.current = setInterval(() => {
      fetchData(false); // Passa false per non mostrare il loading indicator
    }, 5000);

    // Pulisci l'intervallo quando il componente viene smontato
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, []);

  const fetchData = async (showLoading = true) => {
    if (!user) return;

    try {
      if (showLoading) {
        setIsLoading(true);
      }

      // Get children data
      const childrenData = await getChildrenForParent(user.id);

      // Get location for each child
      const childrenWithLocations = await Promise.all(
        childrenData.map(async (child: Child) => {
          try {
            const locationData = await getChildLocation(child.child_id);
            if (locationData) {
              return {
                ...child,
                location: locationData
              };
            }

            // If no location data, return child without location
            return child;
          } catch (err) {
            console.error(`Error getting location for child ${child.child_id}:`, err);
            return child;
          }
        })
      );

      setChildren(childrenWithLocations);

      // Center map on children's locations if available
      if (childrenWithLocations.length > 0 && childrenWithLocations.some(child => child.location)) {
        fitMapToChildren(childrenWithLocations);
      }
    } catch (error) {
      console.error('Error fetching map data:', error);
      Alert.alert('Error', 'Failed to load map data');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fit map to show all children
  const fitMapToChildren = (childrenData: Child[]) => {
    if (!mapRef.current || childrenData.length === 0) return;

    const childrenWithLocation = childrenData.filter(child => child.location);
    if (childrenWithLocation.length === 0) {
      console.log('No children with location data found');
      return;
    }

    const validLocations = childrenWithLocation.map(child => ({
      latitude: child.location!.latitude,
      longitude: child.location!.longitude,
    }));

    if (validLocations.length === 0) return;

    // If only one child, center on that child with some padding
    if (validLocations.length === 1) {
      setRegion({
        latitude: validLocations[0].latitude,
        longitude: validLocations[0].longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
      return;
    }

    // Fit map to show all children
    mapRef.current.fitToCoordinates(validLocations, {
      edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
      animated: true,
    });
  };

  // Funzione per visualizzare i tragitti di un bambino
  const showChildRoutes = (child: Child) => {
    setSelectedChild(child);
    setShowRoutes(true);
  };

  // Funzione per chiudere la visualizzazione dei tragitti
  const closeRoutes = () => {
    setShowRoutes(false);
    setSelectedChild(null);
  };

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <View style={styles.container}>
      <Header title="Location Map" />

      {children.length === 0 ? (
        <View style={styles.mapPlaceholder}>
          <FontAwesome5 name="map-marked-alt" size={50} color="#4630EB" />
          <Text style={styles.placeholderText}>No Children Added</Text>
          <Text style={styles.placeholderSubtext}>
            Add children in the Children tab to see their locations on the map.
          </Text>

          <TouchableOpacity
            style={styles.refreshButton}
            onPress={fetchData}
          >
            <FontAwesome5 name="sync" size={16} color="#fff" />
            <Text style={styles.refreshText}>Refresh</Text>
          </TouchableOpacity>
        </View>
      ) : children.every(child => !child.location) ? (
        <View style={styles.mapPlaceholder}>
          <FontAwesome5 name="map-marked-alt" size={50} color="#4630EB" />
          <Text style={styles.placeholderText}>No Location Data</Text>
          <Text style={styles.placeholderSubtext}>
            None of your children have shared their location yet. Make sure the child app is running and has location permissions.
          </Text>

          <TouchableOpacity
            style={styles.refreshButton}
            onPress={fetchData}
          >
            <FontAwesome5 name="sync" size={16} color="#fff" />
            <Text style={styles.refreshText}>Refresh</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.mapContainer}>
          <GoogleMapView
            ref={mapRef}
            style={styles.map}
            initialRegion={region}
          >
            {children.map((child) => (
              <React.Fragment key={child.child_id}>
                {child.location ? (
                  <>
                    <Marker
                      coordinate={{
                        latitude: child.location.latitude,
                        longitude: child.location.longitude,
                      }}
                      title={child.users?.name || 'Child'}
                      description={`Last updated: ${new Date(child.location.timestamp).toLocaleTimeString()}`}
                      onCalloutPress={() => showChildRoutes(child)}
                    >
                      <View style={styles.markerContainer}>
                        <FontAwesome5 name="child" size={20} color="#4630EB" />
                      </View>
                    </Marker>

                    <Circle
                      center={{
                        latitude: child.location.latitude,
                        longitude: child.location.longitude,
                      }}
                      radius={100}
                      fillColor="rgba(70, 48, 235, 0.1)"
                      strokeColor="rgba(70, 48, 235, 0.5)"
                    />
                  </>
                ) : null}
              </React.Fragment>
            ))}
          </GoogleMapView>

          <TouchableOpacity
            style={styles.refreshButtonOverlay}
            onPress={fetchData}
          >
            <FontAwesome5 name="sync" size={16} color="#fff" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.fitToChildrenButton}
            onPress={() => fitMapToChildren(children)}
          >
            <FontAwesome5 name="compress-arrows-alt" size={16} color="#fff" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.routesButton}
            onPress={() => router.push('/(parent-tabs)/routes')}
          >
            <FontAwesome5 name="route" size={16} color="#fff" />
          </TouchableOpacity>
        </View>
      )}

      {/* Modal per visualizzare i tragitti */}
      <Modal
        visible={showRoutes}
        animationType="slide"
        onRequestClose={closeRoutes}
      >
        {selectedChild && (
          <ChildRouteMap
            childId={selectedChild.child_id}
            childName={selectedChild.users?.name || 'Child'}
            onClose={closeRoutes}
          />
        )}
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  mapPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    color: '#333',
  },
  placeholderSubtext: {
    fontSize: 16,
    color: '#666',
    marginTop: 10,
    textAlign: 'center',
    maxWidth: '80%',
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4630EB',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    marginTop: 30,
  },
  refreshText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  markerContainer: {
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#4630EB',
  },
  refreshButtonOverlay: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#4630EB',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  fitToChildrenButton: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    backgroundColor: '#4630EB',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  routesButton: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    backgroundColor: '#FF9800',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
});

export default MapScreen;
