import React from 'react';
import { StyleSheet } from 'react-native';
import { Marker, Circle } from 'react-native-maps';
import { Ionicons } from '@expo/vector-icons';

type SafeZoneMarkerProps = {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  radius: number;
  color?: string;
  onPress?: () => void;
};

export default function SafeZoneMarker({
  id,
  name,
  latitude,
  longitude,
  radius,
  color = '#4630EB',
  onPress,
}: SafeZoneMarkerProps) {
  // Default values for zone visualization
  const fillOpacity = 0.15;
  const strokeWidth = 1;
  const strokeOpacity = 0.5;

  return (
    <>
      {/* Circle showing the safe zone area */}
      <Circle
        center={{
          latitude,
          longitude,
        }}
        radius={radius}
        fillColor={`${color}${Math.round(fillOpacity * 255).toString(16)}`}
        strokeColor={color}
        strokeWidth={strokeWidth}
        zIndex={1}
      />

      {/* Central marker for the safe zone */}
      <Marker
        identifier={`safe-zone-${id}`}
        coordinate={{
          latitude,
          longitude,
        }}
        title={name}
        description={`Safe zone with ${radius}m radius`}
        onPress={onPress}
        zIndex={2}
      >
        <Ionicons
          name="shield-checkmark"
          size={24}
          color={color}
          style={styles.markerIcon}
        />
      </Marker>
    </>
  );
}

const styles = StyleSheet.create({
  markerIcon: {
    backgroundColor: 'white',
    padding: 4,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#DDD',
  },
}); 