import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { FontAwesome5 } from '@expo/vector-icons';
import Header from '../../../components/shared/Header';
import { getChildById, updateChildDetails } from '../../../utils/supabase';
import { useAuth } from '../../../contexts/AuthContext';

interface ChildDetails {
  id: string;
  name: string;
  age?: number;
  email?: string;
  user_type: string;
  created_at: string;
}

export default function ChildDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [childDetails, setChildDetails] = useState<ChildDetails | null>(null);
  const [editMode, setEditMode] = useState(false);

  // Editable fields
  const [name, setName] = useState('');
  const [age, setAge] = useState('');

  useEffect(() => {
    if (id) {
      fetchChildDetails();
    }
  }, [id]);

  const fetchChildDetails = async () => {
    try {
      setIsLoading(true);
      const childData = await getChildById(id as string);

      if (childData) {
        setChildDetails(childData);
        setName(childData.name || '');
        setAge(childData.age ? childData.age.toString() : '');
      } else {
        Alert.alert('Error', 'Child not found');
        router.back();
      }
    } catch (error) {
      console.error('Error fetching child details:', error);
      Alert.alert('Error', 'Failed to load child details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!childDetails) return;

    try {
      setIsSaving(true);

      // Validate inputs
      if (!name.trim()) {
        Alert.alert('Error', 'Name cannot be empty');
        setIsSaving(false);
        return;
      }

      const ageNumber = age ? parseInt(age) : undefined;
      if (age && (isNaN(ageNumber!) || ageNumber! <= 0 || ageNumber! > 18)) {
        Alert.alert('Error', 'Age must be a number between 1 and 18');
        setIsSaving(false);
        return;
      }

      // Update child details
      const updatedDetails = {
        name: name.trim(),
        age: ageNumber
      };

      await updateChildDetails(childDetails.id, updatedDetails);

      // Update local state
      setChildDetails({
        ...childDetails,
        name: name.trim(),
        age: ageNumber
      });

      setEditMode(false);
      Alert.alert('Success', 'Child details updated successfully');
    } catch (error) {
      console.error('Error updating child details:', error);
      Alert.alert('Error', 'Failed to update child details');
    } finally {
      setIsSaving(false);
    }
  };

  const toggleEditMode = () => {
    if (editMode) {
      // Cancel edit - reset values
      if (childDetails) {
        setName(childDetails.name || '');
        setAge(childDetails.age ? childDetails.age.toString() : '');
      }
    }
    setEditMode(!editMode);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('it-IT', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Header title="Child Details" showBackButton onBackPress={() => router.back()} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4630EB" />
          <Text style={styles.loadingText}>Loading child details...</Text>
        </View>
      </View>
    );
  }

  if (!childDetails) {
    return (
      <View style={styles.container}>
        <Header title="Child Details" showBackButton onBackPress={() => router.back()} />
        <View style={styles.errorContainer}>
          <FontAwesome5 name="exclamation-circle" size={50} color="#FF3B30" />
          <Text style={styles.errorText}>Child not found</Text>
          <TouchableOpacity style={styles.button} onPress={() => router.back()}>
            <Text style={styles.buttonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <Header
        title={editMode ? "Edit Child" : "Child Details"}
        showBackButton
        onBackPress={() => router.back()}
      />

      <ScrollView style={styles.content}>
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <FontAwesome5 name="child" size={40} color="#4630EB" />
          </View>

          {editMode ? (
            <TextInput
              style={styles.nameInput}
              value={name}
              onChangeText={setName}
              placeholder="Child's name"
              maxLength={30}
            />
          ) : (
            <Text style={styles.childName}>{childDetails.name}</Text>
          )}

          <Text style={styles.childType}>Child Account</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Personal Information</Text>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Name</Text>
            {editMode ? (
              <TextInput
                style={styles.infoInput}
                value={name}
                onChangeText={setName}
                placeholder="Enter name"
              />
            ) : (
              <Text style={styles.infoValue}>{childDetails.name}</Text>
            )}
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Age</Text>
            {editMode ? (
              <TextInput
                style={styles.infoInput}
                value={age}
                onChangeText={setAge}
                placeholder="Enter age"
                keyboardType="number-pad"
                maxLength={2}
              />
            ) : (
              <Text style={styles.infoValue}>
                {childDetails.age ? `${childDetails.age} years` : 'Not specified'}
              </Text>
            )}
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Account Type</Text>
            <Text style={styles.infoValue}>Child</Text>
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Added On</Text>
            <Text style={styles.infoValue}>{formatDate(childDetails.created_at)}</Text>
          </View>
        </View>


      </ScrollView>

      <View style={styles.footer}>
        {editMode ? (
          <View style={styles.editButtons}>
            <TouchableOpacity
              style={[styles.footerButton, styles.cancelButton]}
              onPress={toggleEditMode}
              disabled={isSaving}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.footerButton, styles.saveButton]}
              onPress={handleSave}
              disabled={isSaving}
            >
              {isSaving ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>Save Changes</Text>
              )}
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.editButtons}>
            <TouchableOpacity
              style={[styles.footerButton, styles.editButton]}
              onPress={toggleEditMode}
            >
              <FontAwesome5 name="edit" size={16} color="#fff" style={styles.buttonIcon} />
              <Text style={styles.editButtonText}>Edit Details</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
  },
  profileHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#EEF1FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  childName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  nameInput: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
    textAlign: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#4630EB',
    paddingBottom: 4,
    minWidth: 200,
  },
  childType: {
    fontSize: 16,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  infoLabel: {
    fontSize: 16,
    color: '#666',
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  infoInput: {
    fontSize: 16,
    color: '#333',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 8,
    minWidth: 150,
    textAlign: 'right',
  },

  footer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  editButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  footerButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 150,
  },
  editButton: {
    backgroundColor: '#4630EB',
  },
  saveButton: {
    backgroundColor: '#4CAF50',
    marginLeft: 12,
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  buttonIcon: {
    marginRight: 8,
  },
  editButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: 'bold',
  },
  button: {
    backgroundColor: '#4630EB',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
