import React, { createContext, useState, useEffect, useContext } from 'react';
import * as Location from 'expo-location';
import * as Battery from 'expo-battery';
import { useAuth } from './AuthContext';
import {
  startLocationTracking,
  stopLocationTracking,
  getCurrentLocation,
  isLocationInSafeZone
} from '../utils/location';
import {
  saveLocationUpdate,
  getChildLocation,
  getSafeZones,
  addRoutePoint
} from '../utils/supabase';
import { sendZoneNotification, getParentPushToken } from '../lib/notifications';

// Define types for our context
type LocationContextType = {
  isLocationTracking: boolean;
  currentLocation: LocationData | null;
  safeZones: SafeZone[];
  childLocations: { [childId: string]: LocationData };
  startTracking: () => Promise<void>;
  stopTracking: () => Promise<void>;
  getChildCurrentLocation: (childId: string) => Promise<LocationData | null>;
  isLoading: boolean;
  error: string | null;
  clearError: () => void;
  checkGeofenceStatus: () => Promise<void>;
};

type LocationData = {
  latitude: number;
  longitude: number;
  accuracy?: number;
  speed?: number;
  battery?: number;
  timestamp?: Date;
};

type SafeZone = {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  radius: number;
};

// Create context with default values
const LocationContext = createContext<LocationContextType>({
  isLocationTracking: false,
  currentLocation: null,
  safeZones: [],
  childLocations: {},
  startTracking: async () => {},
  stopTracking: async () => {},
  getChildCurrentLocation: async () => null,
  isLoading: false,
  error: null,
  clearError: () => {},
  checkGeofenceStatus: async () => {},
});

// Provider component
export const LocationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, userType } = useAuth();
  const [isLocationTracking, setIsLocationTracking] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [childLocations, setChildLocations] = useState<{ [childId: string]: LocationData }>({});
  const [safeZones, setSafeZones] = useState<SafeZone[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previousSafeZoneStatus, setPreviousSafeZoneStatus] = useState<{[zoneId: string]: boolean}>({});

  // Load safe zones for parent
  useEffect(() => {
    const loadSafeZones = async () => {
      if (user && userType === 'parent') {
        try {
          const zones = await getSafeZones(user.id);
          setSafeZones(zones);
        } catch (err: any) {
          console.error('Error loading safe zones:', err);
          setError('Failed to load safe zones');
        }
      }
    };

    loadSafeZones();
  }, [user, userType]);

  // Note: Location tracking for children is now set up through the onboarding process
  // instead of automatically when they log in. This allows for proper permission flow.
  // The location tracking will be started by the PermissionOnboardingScreen after
  // the user grants the necessary permissions.

  // Start location tracking
  const startTracking = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!user) {
        throw new Error('User must be authenticated to track location');
      }

      if (userType === 'child') {
        await startLocationTracking(user.id);
        setIsLocationTracking(true);
      } else {
        throw new Error('Only child devices can be tracked');
      }
    } catch (err: any) {
      console.error('Error starting location tracking:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Stop location tracking
  const stopTracking = async () => {
    try {
      setIsLoading(true);
      await stopLocationTracking();
      setIsLocationTracking(false);
    } catch (err: any) {
      console.error('Error stopping location tracking:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Get a child's current location
  const getChildCurrentLocation = async (childId: string): Promise<LocationData | null> => {
    try {
      setIsLoading(true);

      // Check if we already have it cached
      if (childLocations[childId]) {
        return childLocations[childId];
      }

      // Fetch from server
      const locationData = await getChildLocation(childId);

      if (locationData) {
        const location = {
          latitude: locationData.latitude,
          longitude: locationData.longitude,
          accuracy: locationData.accuracy,
          battery: locationData.battery_level,
          timestamp: new Date(locationData.timestamp),
        };

        // Update our cache
        setChildLocations(prev => ({
          ...prev,
          [childId]: location
        }));

        return location;
      }

      return null;
    } catch (err: any) {
      console.error('Error getting child location:', err);
      setError(err.message);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user has entered or exited any safe zones
  const checkGeofenceStatus = async () => {
    try {
      if (!currentLocation || !user || userType !== 'child') {
        return;
      }

      // Get all safe zones assigned to this child
      const zones = await getSafeZones(user.id);

      // Get parent's push token
      const parentToken = await getParentPushToken(user.id);

      if (parentToken) {
        console.log('Found parent push token for geofence notifications:', parentToken);
      } else {
        console.log('No parent push token found for geofence notifications');
      }

      // Check each zone
      for (const zone of zones) {
        const isInZone = isLocationInSafeZone(currentLocation, zone);
        const wasInZone = previousSafeZoneStatus[zone.id];

        // If status changed, trigger a notification
        if (isInZone !== wasInZone) {
          if (isInZone) {
            // Entered zone
            console.log(`Child ${user.name || 'Child'} entered zone ${zone.name}`);
            await sendZoneNotification(user.name || 'Child', zone.name, 'entered', parentToken || undefined);
          } else {
            // Left zone
            console.log(`Child ${user.name || 'Child'} exited zone ${zone.name}`);
            await sendZoneNotification(user.name || 'Child', zone.name, 'exited', parentToken || undefined);
          }

          // Update status
          setPreviousSafeZoneStatus(prev => ({
            ...prev,
            [zone.id]: isInZone
          }));
        }
      }
    } catch (err: any) {
      console.error('Error checking geofence status:', err);
      setError(err.message);
    }
  };

  // Clear any errors
  const clearError = () => setError(null);

  return (
    <LocationContext.Provider
      value={{
        isLocationTracking,
        currentLocation,
        safeZones,
        childLocations,
        startTracking,
        stopTracking,
        getChildCurrentLocation,
        isLoading,
        error,
        clearError,
        checkGeofenceStatus,
      }}
    >
      {children}
    </LocationContext.Provider>
  );
};

// Custom hook to use the location context
export const useLocation = () => {
  const context = useContext(LocationContext);

  if (!context) {
    throw new Error('useLocation must be used within a LocationProvider');
  }

  return context;
};