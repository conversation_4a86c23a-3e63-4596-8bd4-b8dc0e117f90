import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Alert, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { useLocation } from '../../contexts/LocationContext';
import MapView, { PROVIDER_DEFAULT, Marker, Circle } from 'react-native-maps';
import ChildLocationMarker from '../../components/parent/ChildLocationMarker';
import SafeZoneMarker from '../../components/parent/SafeZoneMarker';
import PermissionExplainer from '../../components/common/PermissionExplainer';
import usePermissionRequest from '../../hooks/usePermissionRequest';
import WebViewOpenStreetMap from '../../components/shared/WebViewOpenStreetMap';
import NativeOpenStreetMapView from '../../components/shared/NativeOpenStreetMapView';

export default function ParentMapScreen() {
  const { user } = useAuth();
  const { childLocations, safeZones } = useLocation();
  const navigation = useNavigation();
  const [selectedChildId, setSelectedChildId] = useState<string | null>(null);

  // Set up notifications permission
  const {
    permissionStatus: notificationsStatus,
    showPermissionExplainer: showNotificationsExplainer,
    requestPermission: requestNotificationsPermission,
    dismissExplainer: dismissNotificationsExplainer,
  } = usePermissionRequest('notifications', 'parent');

  const handleChildMarkerPress = (childId: string) => {
    setSelectedChildId(childId);
  };

  const handleOpenPermissionGuide = () => {
    navigation.navigate('PermissionGuide' as never);
  };

  const handleShowPermissionHelp = () => {
    Alert.alert(
      'Configurazione autorizzazioni',
      'Vuoi vedere le istruzioni per configurare correttamente le autorizzazioni di posizione sul dispositivo di tuo figlio?',
      [
        { text: 'Annulla', style: 'cancel' },
        { text: 'Mostra guida', onPress: handleOpenPermissionGuide }
      ]
    );
  };

  return (
    <View style={styles.container}>
      {Platform.OS === 'android' ? (
        <NativeOpenStreetMapView
          latitude={41.9028}  // Default to Rome, Italy
          longitude={12.4964}
          zoom={13}
          style={styles.map}
          markers={[
            // Convert child locations to markers
            ...Object.entries(childLocations).map(([childId, location]) => ({
              latitude: location.latitude,
              longitude: location.longitude,
              title: location.name || 'Child'
            })),
            // Convert safe zones to markers
            ...safeZones.map(zone => ({
              latitude: zone.latitude,
              longitude: zone.longitude,
              title: zone.name
            }))
          ]}
        />
      ) : (
        <WebViewOpenStreetMap
          latitude={41.9028}  // Default to Rome, Italy
          longitude={12.4964}
          zoom={13}
          style={styles.map}
          markers={[
            // Convert child locations to markers
            ...Object.entries(childLocations).map(([childId, location]) => ({
              latitude: location.latitude,
              longitude: location.longitude,
              title: location.name || 'Child'
            })),
            // Convert safe zones to markers
            ...safeZones.map(zone => ({
              latitude: zone.latitude,
              longitude: zone.longitude,
              title: zone.name
            }))
          ]}
        />
      )}

      {/* Permission helper button */}
      <TouchableOpacity
        style={styles.helpButton}
        onPress={handleShowPermissionHelp}
      >
        <Ionicons name="help-circle" size={24} color="white" />
        <Text style={styles.helpButtonText}>Configura autorizzazioni</Text>
      </TouchableOpacity>

      {/* Notification permission explainer */}
      <PermissionExplainer
        visible={showNotificationsExplainer}
        onRequestPermission={requestNotificationsPermission}
        onDismiss={dismissNotificationsExplainer}
        permissionType="notifications"
        userType="parent"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  helpButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#4630EB',
    borderRadius: 30,
    paddingVertical: 10,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  helpButtonText: {
    color: 'white',
    marginLeft: 8,
    fontWeight: 'bold',
  },
});