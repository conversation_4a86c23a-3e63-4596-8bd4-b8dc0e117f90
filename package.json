{"name": "kidguard", "main": "expo-router/entry", "version": "1.0.31", "scripts": {"start": "expo start", "start:simple": "node ./scripts/start-simple.js", "start:clear": "expo start --clear", "start:env": "node ./scripts/start-with-env.js", "start:prod": "node ./scripts/start-production.js", "start:force-prod": "node ./scripts/force-production.js", "check:env": "node ./scripts/check-env.js", "check:dev": "node ./scripts/check-dev-mode.js", "reset-project": "node ./scripts/reset-project.js", "reset-cache": "node ./scripts/reset-cache.js", "clean-restart": "node ./scripts/clean-restart.js", "deep-clean": "node ./scripts/deep-clean.js", "update-deps": "node ./scripts/update-dependencies.js", "prepare-prod": "node ./scripts/prepare-production.js", "test-billing": "node ./scripts/test-billing.js", "test-init": "node ./scripts/test-initialization.js", "force-onboarding": "node ./scripts/force-onboarding.js", "build-test": "node ./scripts/build-test-apk.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web --no-dev", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@brighthustle/react-native-usage-stats-manager": "^0.1.5", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/geolocation": "^3.4.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@shopify/flash-list": "1.7.6", "@supabase/supabase-js": "^2.49.4", "@types/uuid": "^10.0.0", "base64-js": "^1.5.1", "buffer": "^6.0.3", "chalk": "^5.4.1", "date-fns": "^4.1.0", "events": "^3.3.0", "expo": "~53.0.0", "expo-battery": "^9.0.2", "expo-blur": "~14.1.4", "expo-build-properties": "~0.14.6", "expo-clipboard": "^7.0.1", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.4", "expo-dev-client": "~5.1.8", "expo-device": "~7.1.4", "expo-env": "^1.1.1", "expo-file-system": "^18.0.12", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "^16.0.6", "expo-intent-launcher": "~12.1.4", "expo-linking": "~7.1.5", "expo-location": "^18.0.10", "expo-modules-core": "~2.3.13", "expo-notifications": "~0.31.2", "expo-router": "~5.0.7", "expo-secure-store": "^14.0.1", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-task-manager": "~13.1.5", "expo-updates": "~0.28.13", "expo-web-browser": "~14.1.6", "geokit": "^1.1.0", "https-browserify": "^1.0.0", "lottie-react-native": "^7.1.0", "metro-minify-terser": "0.80.0", "process": "^0.11.10", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-iap": "^12.16.2", "react-native-maps": "1.20.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "stream-browserify": "^3.0.0", "url": "^0.11.4", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "latest", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^19.0.0", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "dotenv": "^16.5.0", "jest": "^29.2.1", "jest-expo": "~53.0.5", "react-native-dotenv": "^3.4.11", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-maps"], "listUnknownPackages": false}}}}