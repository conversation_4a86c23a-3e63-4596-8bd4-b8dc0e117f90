import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import TrialInfoScreen from '../../components/subscription/TrialInfoScreen';
import LoadingIndicator from '../../components/shared/LoadingIndicator';

export default function TrialInfoPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  // If no user is logged in, redirect to login
  if (!user && !isLoading) {
    router.replace('/auth/parent-login');
    return null;
  }

  if (isLoading) {
    return <LoadingIndicator fullScreen text="Loading..." />;
  }

  return (
    <View style={styles.container}>
      <TrialInfoScreen />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});
