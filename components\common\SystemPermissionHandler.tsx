import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Modal, ActivityIndicator } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import {
  openBatteryOptimizationSettingsNative,
  isBatteryOptimizationDisabledNative,
  openNotificationListenerSettingsNative,
  hasNotificationListenerAccessNative,
  listenToBatteryOptimizationChanges
} from '../../utils/systemSettingsNative';

interface SystemPermissionHandlerProps {
  permissionType: 'batteryOptimization' | 'notificationAccess';
  onPermissionGranted?: () => void;
  onPermissionDenied?: () => void;
  buttonText?: string;
  title?: string;
  description?: string;
}

const SystemPermissionHandler: React.FC<SystemPermissionHandlerProps> = ({
  permissionType,
  onPermissionGranted,
  onPermissionDenied,
  buttonText = 'Attiva',
  title,
  description
}) => {
  const [permissionStatus, setPermissionStatus] = useState<boolean | null>(null);
  const [checking, setChecking] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [checkingAfterSettings, setCheckingAfterSettings] = useState(false);

  // Determina il titolo e la descrizione in base al tipo di autorizzazione
  const getContent = () => {
    if (permissionType === 'batteryOptimization') {
      return {
        title: title || 'Ottimizzazione Batteria',
        description: description || 'Per garantire che l\'app funzioni correttamente in background, è necessario disattivare l\'ottimizzazione della batteria.',
        icon: 'battery-full'
      };
    } else {
      return {
        title: title || 'Accesso alle Notifiche',
        description: description || 'Per ricevere notifiche importanti, è necessario concedere l\'accesso alle notifiche.',
        icon: 'bell'
      };
    }
  };

  const content = getContent();

  // Verifica lo stato dell'autorizzazione
  const checkPermissionStatus = async () => {
    try {
      setChecking(true);
      let status = false;

      if (permissionType === 'batteryOptimization') {
        status = await isBatteryOptimizationDisabledNative();
      } else {
        status = await hasNotificationListenerAccessNative();
      }

      console.log(`[SystemPermissionHandler] ${permissionType} status:`, status);
      setPermissionStatus(status);

      if (status && onPermissionGranted) {
        onPermissionGranted();
      } else if (!status && onPermissionDenied) {
        onPermissionDenied();
      }

      setChecking(false);
      return status;
    } catch (error) {
      console.error(`[SystemPermissionHandler] Error checking ${permissionType} status:`, error);
      setPermissionStatus(false);
      setChecking(false);
      return false;
    }
  };

  // Ascolta i cambiamenti dello stato dell'ottimizzazione della batteria
  useEffect(() => {
    if (permissionType === 'batteryOptimization') {
      const unsubscribe = listenToBatteryOptimizationChanges((isDisabled) => {
        console.log('[SystemPermissionHandler] Battery optimization status changed:', isDisabled);
        setPermissionStatus(isDisabled);

        if (isDisabled && onPermissionGranted) {
          onPermissionGranted();
        } else if (!isDisabled && onPermissionDenied) {
          onPermissionDenied();
        }
      });

      return unsubscribe;
    }
  }, [permissionType, onPermissionGranted, onPermissionDenied]);

  // Verifica lo stato dell'autorizzazione all'avvio
  useEffect(() => {
    checkPermissionStatus();

    // Verifica periodica dello stato (ogni 2 secondi)
    const intervalId = setInterval(() => {
      if (!checkingAfterSettings) {
        checkPermissionStatus();
      }
    }, 2000);

    return () => clearInterval(intervalId);
  }, []);

  // Apre le impostazioni di sistema per concedere l'autorizzazione
  const openSystemSettings = async () => {
    try {
      setShowModal(true);

      if (permissionType === 'batteryOptimization') {
        await openBatteryOptimizationSettingsNative();
      } else {
        await openNotificationListenerSettingsNative();
      }

      // Dopo aver aperto le impostazioni, verifichiamo lo stato dopo un breve ritardo
      setCheckingAfterSettings(true);

      // Verifica lo stato ogni secondo per 10 secondi
      let attempts = 0;
      const maxAttempts = 10;
      const checkInterval = setInterval(async () => {
        attempts++;
        console.log(`[SystemPermissionHandler] Checking ${permissionType} status after settings (attempt ${attempts}/${maxAttempts})...`);

        const status = await checkPermissionStatus();

        if (status || attempts >= maxAttempts) {
          clearInterval(checkInterval);
          setCheckingAfterSettings(false);
          setShowModal(false);

          if (status) {
            console.log(`[SystemPermissionHandler] ${permissionType} permission granted!`);
            if (onPermissionGranted) onPermissionGranted();
          } else {
            console.log(`[SystemPermissionHandler] ${permissionType} permission still not granted after ${maxAttempts} attempts`);
            if (onPermissionDenied) onPermissionDenied();
          }
        }
      }, 1000);

    } catch (error) {
      console.error(`[SystemPermissionHandler] Error opening ${permissionType} settings:`, error);
      setShowModal(false);
      setCheckingAfterSettings(false);
      Alert.alert('Errore', 'Non è stato possibile aprire le impostazioni di sistema.');
    }
  };

  if (checking && permissionStatus === null) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#4630EB" />
        <Text style={styles.loadingText}>Verifica delle autorizzazioni in corso...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Modal
        visible={showModal}
        transparent={true}
        animationType="fade"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <ActivityIndicator size="large" color="#4630EB" />
            <Text style={styles.modalText}>
              Verifica delle autorizzazioni in corso...
            </Text>
            <Text style={styles.modalSubText}>
              Torna all'app dopo aver concesso l'autorizzazione nelle impostazioni di sistema.
            </Text>
          </View>
        </View>
      </Modal>

      <View style={styles.iconContainer}>
        <FontAwesome5 name={content.icon as any} size={40} color="#4630EB" />
      </View>

      <Text style={styles.title}>{content.title}</Text>
      <Text style={styles.description}>{content.description}</Text>

      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Stato:</Text>
        <View style={styles.statusIndicator}>
          {permissionStatus ? (
            <>
              <FontAwesome5 name="check-circle" size={20} color="#4CAF50" />
              <Text style={[styles.statusText, styles.statusGranted]}>Autorizzazione concessa</Text>
            </>
          ) : (
            <>
              <FontAwesome5 name="times-circle" size={20} color="#F44336" />
              <Text style={[styles.statusText, styles.statusDenied]}>Autorizzazione non concessa</Text>
            </>
          )}
        </View>
      </View>

      {!permissionStatus && (
        <TouchableOpacity
          style={styles.button}
          onPress={openSystemSettings}
          disabled={checkingAfterSettings}
        >
          {checkingAfterSettings ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text style={styles.buttonText}>{buttonText}</Text>
          )}
        </TouchableOpacity>
      )}

      {/* Rimosso il pulsante "Verifica stato" quando l'autorizzazione è già concessa */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#EBE7FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
    color: '#333',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    color: '#666',
    lineHeight: 22,
  },
  statusContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 24,
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    width: '100%',
  },
  statusLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    marginLeft: 8,
  },
  statusGranted: {
    color: '#4CAF50',
  },
  statusDenied: {
    color: '#F44336',
  },
  button: {
    backgroundColor: '#4630EB',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 32,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 200,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EBE7FF',
    borderRadius: 30,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginTop: 16,
  },
  refreshButtonText: {
    color: '#4630EB',
    fontWeight: 'bold',
    fontSize: 14,
    marginLeft: 8,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
  },
  modalText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
    color: '#333',
  },
  modalSubText: {
    fontSize: 14,
    textAlign: 'center',
    color: '#666',
    lineHeight: 20,
  },
});

export default SystemPermissionHandler;
