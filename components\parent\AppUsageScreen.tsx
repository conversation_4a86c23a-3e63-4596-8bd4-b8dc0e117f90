import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useAppUsage } from '../../hooks/useAppUsage';
import { AppUsageData } from '../../services/appUsageService';

export default function AppUsageScreen() {
  const {
    hasPermission,
    isLoading,
    error,
    todayStats,
    weeklyStats,
    topApps,
    totalScreenTime,
    checkPermission,
    requestPermission,
    loadAllStats,
    refresh,
    formatTime,
    isAppOfConcern,
  } = useAppUsage();

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (hasPermission) {
      loadAllStats();
    }
  }, [hasPermission, loadAllStats]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refresh();
    setRefreshing(false);
  };

  const handleRequestPermission = () => {
    Alert.alert(
      'Permesso Richiesto',
      'Per monitorare l\'utilizzo delle app, è necessario concedere il permesso di accesso alle statistiche di utilizzo. Verrai reindirizzato alle impostazioni.',
      [
        { text: 'Annulla', style: 'cancel' },
        { text: 'Apri Impostazioni', onPress: requestPermission },
      ]
    );
  };

  const renderPermissionRequest = () => (
    <View style={styles.permissionContainer}>
      <FontAwesome5 name="shield-alt" size={50} color="#4630EB" />
      <Text style={styles.permissionTitle}>Permesso Richiesto</Text>
      <Text style={styles.permissionText}>
        Per monitorare l'utilizzo delle app del tuo bambino, è necessario concedere il permesso di accesso alle statistiche di utilizzo.
      </Text>
      <TouchableOpacity style={styles.permissionButton} onPress={handleRequestPermission}>
        <FontAwesome5 name="cog" size={16} color="#FFFFFF" style={styles.buttonIcon} />
        <Text style={styles.permissionButtonText}>Concedi Permesso</Text>
      </TouchableOpacity>
    </View>
  );

  const renderAppItem = (app: AppUsageData, index: number) => (
    <View key={`${app.packageName}-${index}`} style={styles.appItem}>
      <View style={styles.appInfo}>
        <View style={[
          styles.appIcon,
          isAppOfConcern(app.packageName) && styles.concernAppIcon
        ]}>
          <FontAwesome5 
            name="mobile-alt" 
            size={20} 
            color={isAppOfConcern(app.packageName) ? "#F44336" : "#4630EB"} 
          />
        </View>
        <View style={styles.appDetails}>
          <Text style={styles.appName}>{app.appName || app.packageName}</Text>
          <Text style={styles.packageName}>{app.packageName}</Text>
        </View>
      </View>
      <View style={styles.appStats}>
        <Text style={styles.usageTime}>{formatTime(app.totalTimeInForeground)}</Text>
        {isAppOfConcern(app.packageName) && (
          <FontAwesome5 name="exclamation-triangle" size={12} color="#F44336" />
        )}
      </View>
    </View>
  );

  const renderTodayStats = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Utilizzo di Oggi</Text>
      <View style={styles.totalTimeContainer}>
        <FontAwesome5 name="clock" size={24} color="#4630EB" />
        <Text style={styles.totalTime}>{formatTime(totalScreenTime)}</Text>
        <Text style={styles.totalTimeLabel}>Tempo totale schermo</Text>
      </View>
      
      {todayStats.length > 0 ? (
        <View style={styles.appsList}>
          {todayStats.slice(0, 5).map(renderAppItem)}
          {todayStats.length > 5 && (
            <Text style={styles.moreAppsText}>
              +{todayStats.length - 5} altre app
            </Text>
          )}
        </View>
      ) : (
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>Nessun dato disponibile per oggi</Text>
        </View>
      )}
    </View>
  );

  const renderTopApps = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>App Più Utilizzate</Text>
      {topApps.length > 0 ? (
        <View style={styles.appsList}>
          {topApps.map((app, index) => (
            <View key={`top-${app.packageName}-${index}`} style={styles.topAppItem}>
              <Text style={styles.topAppRank}>#{index + 1}</Text>
              {renderAppItem(app, index)}
            </View>
          ))}
        </View>
      ) : (
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>Nessun dato disponibile</Text>
        </View>
      )}
    </View>
  );

  const renderWeeklyOverview = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Panoramica Settimanale</Text>
      {weeklyStats.length > 0 ? (
        <View style={styles.weeklyContainer}>
          {weeklyStats.map((day, index) => (
            <View key={day.date} style={styles.dayItem}>
              <Text style={styles.dayLabel}>
                {new Date(day.date).toLocaleDateString('it-IT', { weekday: 'short' })}
              </Text>
              <Text style={styles.dayTime}>{formatTime(day.totalScreenTime)}</Text>
            </View>
          ))}
        </View>
      ) : (
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>Caricamento dati settimanali...</Text>
        </View>
      )}
    </View>
  );

  if (!hasPermission) {
    return (
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        {renderPermissionRequest()}
      </ScrollView>
    );
  }

  if (error) {
    return (
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <View style={styles.errorContainer}>
          <FontAwesome5 name="exclamation-triangle" size={50} color="#F44336" />
          <Text style={styles.errorTitle}>Errore</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={refresh}>
            <Text style={styles.retryButtonText}>Riprova</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  }

  return (
    <ScrollView 
      style={styles.container} 
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      <View style={styles.header}>
        <FontAwesome5 name="chart-bar" size={24} color="#4630EB" />
        <Text style={styles.title}>Monitoraggio App</Text>
      </View>

      {isLoading && !refreshing && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4630EB" />
          <Text style={styles.loadingText}>Caricamento statistiche...</Text>
        </View>
      )}

      {renderTodayStats()}
      {renderTopApps()}
      {renderWeeklyOverview()}

      <View style={styles.infoContainer}>
        <FontAwesome5 name="info-circle" size={16} color="#666" />
        <Text style={styles.infoText}>
          Le statistiche vengono aggiornate automaticamente. Le app contrassegnate con ⚠️ potrebbero richiedere maggiore attenzione.
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 12,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  permissionContainer: {
    alignItems: 'center',
    padding: 32,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  permissionText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  permissionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4630EB',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  buttonIcon: {
    marginRight: 4,
  },
  totalTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F0FF',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  totalTime: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4630EB',
    marginLeft: 12,
  },
  totalTimeLabel: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  appsList: {
    gap: 8,
  },
  appItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 8,
    backgroundColor: '#F9F9F9',
    borderRadius: 8,
  },
  appInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  appIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8E8FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  concernAppIcon: {
    backgroundColor: '#FFEBEE',
  },
  appDetails: {
    flex: 1,
  },
  appName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  packageName: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  appStats: {
    alignItems: 'flex-end',
  },
  usageTime: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4630EB',
  },
  topAppItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  topAppRank: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4630EB',
    width: 30,
    textAlign: 'center',
  },
  weeklyContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dayItem: {
    alignItems: 'center',
    flex: 1,
  },
  dayLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  dayTime: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4630EB',
  },
  moreAppsText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
  noDataContainer: {
    alignItems: 'center',
    padding: 20,
  },
  noDataText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  errorContainer: {
    alignItems: 'center',
    padding: 32,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#F44336',
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#4630EB',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F0F8FF',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  infoText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
    flex: 1,
    lineHeight: 16,
  },
});
