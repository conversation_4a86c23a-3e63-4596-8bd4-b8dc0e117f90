import { createClient } from '@supabase/supabase-js';
import 'react-native-url-polyfill/auto';
import * as SecureStore from 'expo-secure-store';
import config from '../config';
import { createSOSNotification, sendPushNotification, sendSOSNotification, getParentPushToken } from '../lib/notifications';

// Get environment variables
const supabaseUrl = config.supabaseUrl;
const supabaseAnonKey = config.supabaseAnonKey;

// SecureStore adapter for Supabase storage
const ExpoSecureStoreAdapter = {
  getItem: (key: string) => {
    return SecureStore.getItemAsync(key);
  },
  setItem: (key: string, value: string) => {
    SecureStore.setItemAsync(key, value);
  },
  removeItem: (key: string) => {
    SecureStore.deleteItemAsync(key);
  },
};

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: ExpoSecureStoreAdapter,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Authentication helpers
export const signUpWithEmail = async (email: string, password: string, name: string) => {
  try {
    console.log('Starting signUpWithEmail for:', email);

    // Sign up the user
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
          user_type: 'parent',
        },
      },
    });

    if (error) {
      console.error('Supabase auth.signUp error:', error);
      throw error;
    }

    console.log('Supabase auth.signUp successful:', data.user?.id);

    // If sign up successful, create a profile record
    if (data.user) {
      try {
        console.log('Creating profile record for user:', data.user.id);

        // Create profile record
        const { error: profileError } = await supabase
          .from('profiles')
          .upsert({
            id: data.user.id,
            name: name,
            email: email,
            user_type: 'parent',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (profileError) {
          console.error('Error creating profile:', profileError);
        } else {
          console.log('Profile record created successfully');
        }

        // Also create a record in the users table
        console.log('Creating user record in users table');
        const { error: userError } = await supabase
          .from('users')
          .upsert({
            id: data.user.id,
            name: name,
            email: email,
            user_type: 'parent',
            created_at: new Date().toISOString()
          });

        if (userError) {
          console.error('Error creating user record:', userError);
        } else {
          console.log('User record created successfully');
        }
      } catch (profileErr) {
        console.error('Error in profile/user creation:', profileErr);
        // We don't throw here to not interrupt the sign up flow
      }
    }

    return data;
  } catch (error: any) {
    console.error('Sign up error:', error);
    throw error;
  }
};

export const signInWithEmail = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    return data;
  } catch (error: any) {
    console.error('Sign in error:', error);
    throw error;
  }
};

export const signInWithToken = async (token: string) => {
  try {
    console.log('Starting signInWithToken with token:', token);

    // Validate token format
    if (!token || token.length < 6) {
      console.error('Invalid token format:', token);
      throw new Error('Token non valido');
    }

    // Clean the token (remove hyphens, spaces, etc.)
    token = token.replace(/[^A-Z0-9]/g, '').toUpperCase();
    console.log('Cleaned token:', token);

    // Use the RPC function to verify the token
    console.log('Verifying token using RPC function');
    const { data: childData, error: verifyError } = await supabase.rpc(
      'verify_child_token',
      { p_token: token }
    );

    if (verifyError) {
      console.error('Error verifying token:', verifyError);
      throw new Error(verifyError.message || 'Token non valido o scaduto');
    }

    if (!childData) {
      console.error('No child data returned from verify_child_token');
      throw new Error('Dati del bambino non trovati');
    }

    console.log('Found child data:', childData);

    return childData;
  } catch (error: any) {
    console.error('Child sign in error:', error);
    throw error;
  }
};

export const signOut = async () => {
  try {
    console.log('Executing signOut in supabase.ts');
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error('Supabase signOut error:', error);
      throw error;
    }

    console.log('Supabase signOut completed successfully');
    return true;
  } catch (error: any) {
    console.error('Sign out error:', error);
    // Don't throw the error, just return false to indicate failure
    return false;
  }
};

export const getCurrentUser = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      if (error.message.includes('Auth session missing')) {
        console.log('No active session found');
        return null;
      }
      throw error;
    }

    if (!user) {
      return null;
    }

    try {
      // Try to get additional user data from the users table
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (!userError && userData) {
        return { ...user, ...userData };
      }

      // If user doesn't exist in the users table, create it
      console.log('User not found in users table, creating record...');
      const userType = user.user_metadata?.user_type || 'parent';
      const userName = user.user_metadata?.name || user.email?.split('@')[0] || 'User';

      const { error: insertError } = await supabase
        .from('users')
        .upsert({
          id: user.id,
          email: user.email,
          name: userName,
          user_type: userType,
          created_at: new Date().toISOString()
        });

      if (insertError) {
        console.error('Error creating user record:', insertError);
      } else {
        console.log('User record created successfully');

        // Fetch the newly created user data
        const { data: newUserData, error: newUserError } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();

        if (!newUserError && newUserData) {
          return { ...user, ...newUserData };
        }
      }
    } catch (userDataError) {
      console.error('Error fetching/creating user data:', userDataError);
    }

    // If we couldn't get or create user data in the users table, create a minimal user object
    return {
      ...user,
      name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
      user_type: user.user_metadata?.user_type || 'parent'
    };
  } catch (error: any) {
    console.error('Error getting current user:', error);
    return null;
  }
};

// User data helpers
export const createOrUpdateUser = async (userId: string, userData: any) => {
  const { data, error } = await supabase
    .from('users')
    .upsert({
      id: userId,
      ...userData,
      updated_at: new Date()
    })
    .select()
    .single();

  if (error) throw error;
  return data;
};

// Save push notification token
export const savePushToken = async (userId: string, token: string) => {
  try {
    console.log('Saving push token for user:', userId);
    console.log('Token:', token);

    const { data, error } = await supabase
      .from('user_push_tokens')
      .upsert({
        user_id: userId,
        token,
        updated_at: new Date()
      })
      .select()
      .single();

    if (error) {
      console.error('Error saving push token:', error);
      throw error;
    }

    console.log('Push token saved successfully:', data);
    return data;
  } catch (err) {
    console.error('Error in savePushToken:', err);
    throw err;
  }
};

export const getChildrenForParent = async (parentId: string) => {
  try {
    console.log('[getChildrenForParent] Fetching children for parent:', parentId);

    // Prima ottieni i bambini diretti del genitore
    const { data: directChildren, error: directError } = await supabase
      .from('parent_child_relationships')
      .select(`
        child_id,
        token,
        users:child_id (id, name, email, user_type, age, created_at)
      `)
      .eq('parent_id', parentId);

    if (directError) {
      console.error('[getChildrenForParent] Error fetching direct children:', directError);
      throw directError;
    }

    console.log('[getChildrenForParent] Direct children found:', directChildren?.length || 0);

    // Poi prova a ottenere anche i bambini della famiglia se disponibile
    let familyChildren: any[] = [];
    try {
      const { getAllAccessibleChildren } = await import('./familyAccountService');
      const allChildren = await getAllAccessibleChildren(parentId);

      // Filtra per ottenere solo i bambini della famiglia (non quelli diretti)
      if (allChildren && Array.isArray(allChildren)) {
        const directChildIds = new Set(directChildren?.map(child => child.child_id) || []);
        familyChildren = allChildren.filter(child => !directChildIds.has(child.child_id));
        console.log('[getChildrenForParent] Family children found:', familyChildren.length);
      }
    } catch (familyError) {
      console.warn('[getChildrenForParent] Could not fetch family children:', familyError);
      // Non è un errore critico, continua con i bambini diretti
    }

    // Combina i risultati
    const allChildren = [...(directChildren || []), ...familyChildren];
    console.log('[getChildrenForParent] Total children returned:', allChildren.length);

    return allChildren;
  } catch (error) {
    console.error('[getChildrenForParent] Critical error:', error);
    throw error;
  }
};

/**
 * Verifica e ripara le relazioni genitore-figlio mancanti
 */
export const verifyAndRepairChildRelationships = async (parentId: string) => {
  try {
    console.log('[verifyAndRepairChildRelationships] Checking relationships for parent:', parentId);

    // Ottieni tutte le relazioni esistenti
    const { data: existingRelations, error: relError } = await supabase
      .from('parent_child_relationships')
      .select('child_id, token')
      .eq('parent_id', parentId);

    if (relError) {
      console.error('[verifyAndRepairChildRelationships] Error fetching existing relations:', relError);
      return false;
    }

    console.log('[verifyAndRepairChildRelationships] Found', existingRelations?.length || 0, 'existing relationships');

    // Verifica che ogni bambino nella relazione esista nella tabella users
    if (existingRelations && existingRelations.length > 0) {
      for (const relation of existingRelations) {
        const { data: childUser, error: childError } = await supabase
          .from('users')
          .select('id, name')
          .eq('id', relation.child_id)
          .single();

        if (childError && childError.code === 'PGRST116') {
          console.warn('[verifyAndRepairChildRelationships] Child user not found:', relation.child_id);
          // Il bambino non esiste nella tabella users, potrebbe essere necessario rimuovere la relazione
        } else if (childError) {
          console.error('[verifyAndRepairChildRelationships] Error checking child user:', childError);
        } else {
          console.log('[verifyAndRepairChildRelationships] Child user verified:', childUser.name);
        }
      }
    }

    return true;
  } catch (error) {
    console.error('[verifyAndRepairChildRelationships] Error:', error);
    return false;
  }
};

export const registerChild = async (parentId: string, childData: any, token: string) => {
  try {
    // Verify that the parent exists in the users table
    const { data: parentData, error: parentError } = await supabase
      .from('users')
      .select('id')
      .eq('id', parentId)
      .single();

    if (parentError) {
      console.error('Error verifying parent user:', parentError);

      // Try to get the parent ID from the auth.users table
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        console.log('Current authenticated user:', user.id, user.email);

        // Use the current authenticated user as the parent
        parentId = user.id;

        // Make sure the parent exists in the users table
        const { error: insertError } = await supabase
          .from('users')
          .upsert({
            id: user.id,
            email: user.email,
            name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
            user_type: 'parent',
            created_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Error ensuring parent exists in users table:', insertError);
          throw new Error('Failed to create parent user record');
        }

        console.log('Parent user record created or updated successfully');
      } else {
        throw new Error('Parent user not found');
      }
    }

    // Generate a random email and password for the child
    const randomEmail = `child_${Date.now()}_${Math.floor(Math.random() * 10000)}@kidguard.app`;
    const randomPassword = Math.random().toString(36).slice(-10) + Math.random().toString(36).slice(-10);

    // Register the child through Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: randomEmail,
      password: randomPassword,
      options: {
        data: {
          name: childData.name,
          user_type: 'child',
        },
      },
    });

    if (authError) {
      console.error('Error creating child auth user:', authError);
      throw authError;
    }

    if (!authData.user) {
      throw new Error('Failed to create child user');
    }

    console.log('Child auth user created:', authData.user.id);

    // Create or update the child in the users table
    const { error: childUserError } = await supabase
      .from('users')
      .upsert({
        id: authData.user.id,
        email: randomEmail,
        name: childData.name,
        age: childData.age, // Include the age field
        user_type: 'child',
        created_at: new Date().toISOString()
      });

    if (childUserError) {
      console.error('Error creating/updating child in users table:', childUserError);
      // Continue with the process even if there's an error here
      // The child user might already exist in the users table
    } else {
      console.log('Child user record created or updated successfully');
    }

    // Create the parent-child relationship using the RPC function
    const { data: relData, error: relError } = await supabase.rpc(
      'create_parent_child_relationship',
      {
        p_parent_id: parentId,
        p_child_id: authData.user.id,
        p_token: token
      }
    );

    if (relError) {
      console.error('Error creating parent-child relationship:', relError);
      throw relError;
    }

    console.log('Parent-child relationship created:', relData);

    // Verifica che la relazione sia stata effettivamente salvata
    const { data: verifyData, error: verifyError } = await supabase
      .from('parent_child_relationships')
      .select('*')
      .eq('parent_id', parentId)
      .eq('child_id', authData.user.id)
      .single();

    if (verifyError) {
      console.error('Error verifying parent-child relationship:', verifyError);
      throw new Error('Failed to verify parent-child relationship was saved');
    }

    console.log('Parent-child relationship verified:', verifyData);

    // Return the child data and relationship
    return {
      child: {
        id: authData.user.id,
        name: childData.name,
        user_type: 'child',
        email: randomEmail
      },
      relationship: verifyData
    };
  } catch (error) {
    console.error('Error in registerChild:', error);
    throw error;
  }
};

// Location tracking
export const saveLocationUpdate = async (
  userId: string,
  locationData: {
    latitude: number,
    longitude: number,
    accuracy?: number,
    speed?: number,
    battery?: number,
    timestamp?: Date
  }
) => {
  try {
    console.log('Saving location update for user:', userId);
    console.log('Location data:', JSON.stringify(locationData));

    // Use the RPC function to save the location update
    const { data, error } = await supabase.rpc(
      'save_location_update',
      {
        p_user_id: userId,
        p_latitude: locationData.latitude,
        p_longitude: locationData.longitude,
        p_accuracy: locationData.accuracy || 0,
        p_battery_level: locationData.battery || 0,
        p_created_at: locationData.timestamp ? locationData.timestamp.toISOString() : new Date().toISOString()
      }
    );

    if (error) {
      console.error('Error from RPC function:', error);
      throw error;
    }

    console.log('Location update saved successfully:', data);
    return data;
  } catch (error) {
    console.error('Error saving location update:', error);
    throw error;
  }
};

export const getChildLocation = async (childId: string) => {
  try {
    // First try to get from location_history table
    const { data: historyData, error: historyError } = await supabase
      .from('location_history')
      .select('*')
      .eq('child_id', childId)
      .order('timestamp', { ascending: false })
      .limit(1)
      .single();

    if (!historyError && historyData) {
      return historyData;
    }

    // If not found in location_history, try location_updates table
    const { data: updatesData, error: updatesError } = await supabase
      .from('location_updates')
      .select('id, user_id, latitude, longitude, accuracy, battery_level, created_at')
      .eq('user_id', childId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (updatesError) {
      // If no location found, return null instead of throwing an error
      if (updatesError.code === 'PGRST116') {
        console.log(`No location data found for child ${childId} in either table`);
        return null;
      }
      throw updatesError;
    }

    // Convert location_updates format to match location_history format
    return {
      id: updatesData.id,
      child_id: updatesData.user_id,
      latitude: updatesData.latitude,
      longitude: updatesData.longitude,
      timestamp: updatesData.created_at,
      battery_level: updatesData.battery_level
    };
  } catch (err) {
    console.error(`Error getting location for child ${childId}:`, err);
    return null;
  }
};

export const updateChildLocation = async (childId: string, latitude: number, longitude: number) => {
  try {
    const { data, error } = await supabase
      .from('location_history')
      .insert({
        child_id: childId,
        latitude,
        longitude,
        timestamp: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (err) {
    console.error(`Error updating location for child ${childId}:`, err);
    throw err;
  }
};

// Safe zones
export const createSafeZone = async (parentId: string, zoneData: any) => {
  console.log('Creating safe zone for parent:', parentId);
  console.log('Zone data:', zoneData);

  try {
    // Ensure all required fields are present and have the correct type
    const safeZoneData = {
      parent_id: parentId,
      name: zoneData.name,
      latitude: parseFloat(zoneData.latitude),
      longitude: parseFloat(zoneData.longitude),
      radius: parseFloat(zoneData.radius) || 100,
      created_at: new Date().toISOString()
    };

    console.log('Formatted safe zone data:', safeZoneData);

    // Insert into the database
    const { data, error } = await supabase
      .from('safe_zones')
      .insert(safeZoneData)
      .select()
      .single();

    if (error) {
      console.error('Error creating safe zone:', error);
      throw error;
    }

    console.log('Safe zone created successfully:', data);
    return data;
  } catch (err) {
    console.error('Error in createSafeZone:', err);
    throw err;
  }
};

export const getSafeZones = async (parentId: string) => {
  try {
    // Prima proviamo a utilizzare la funzione che restituisce le zone con i bambini assegnati
    const { data, error } = await supabase.rpc('get_safe_zones_with_children', {
      p_parent_id: parentId
    });

    if (error) throw error;
    return data || [];
  } catch (err) {
    console.error('Error in getSafeZones with children:', err);

    // Fallback: prova con la funzione che restituisce solo le zone
    try {
      const { data, error } = await supabase.rpc('get_safe_zones_for_parent', {
        p_parent_id: parentId
      });

      if (error) throw error;
      return data || [];
    } catch (rpcErr) {
      console.error('RPC fallback failed:', rpcErr);

      // Ultimo fallback: prova con una query diretta
      try {
        const { data, error } = await supabase
          .from('safe_zones')
          .select('id, name, latitude, longitude, radius, created_at, parent_id')
          .eq('parent_id', parentId);

        if (error) throw error;
        return data || [];
      } catch (fallbackErr) {
        console.error('Direct query fallback failed:', fallbackErr);
        throw fallbackErr;
      }
    }
  }
};

export const assignSafeZoneToChild = async (zoneId: string, childId: string) => {
  console.log('Assigning safe zone to child:', { zoneId, childId });
  try {
    // Usa solo la tabella zone_assignments
    const { data, error } = await supabase
      .from('zone_assignments')
      .insert({
        zone_id: zoneId,
        child_id: childId,
        active: true,
      })
      .select()
      .single();

    if (error) {
      console.error('Error assigning safe zone to child:', error);
      throw error;
    }

    console.log('Safe zone assigned successfully:', data);
    return data;
  } catch (err) {
    console.error('Error in assignSafeZoneToChild:', err);
    throw err;
  }
};

export const deleteSafeZone = async (zoneId: string) => {
  console.log('Deleting safe zone:', zoneId);
  try {
    // Prima elimina tutte le assegnazioni di questa zona
    const { error: assignmentError } = await supabase
      .from('zone_assignments')
      .delete()
      .eq('zone_id', zoneId);

    if (assignmentError) {
      console.error('Error deleting zone assignments:', assignmentError);
      // Continua comunque con l'eliminazione della zona
    }

    // Poi elimina la zona stessa
    const { data, error } = await supabase
      .from('safe_zones')
      .delete()
      .eq('id', zoneId)
      .select()
      .single();

    if (error) {
      console.error('Error deleting safe zone:', error);
      throw error;
    }

    console.log('Safe zone deleted successfully:', data);
    return data;
  } catch (err) {
    console.error('Error in deleteSafeZone:', err);
    throw err;
  }
};

// Missions
export const createMission = async (parentId: string, missionData: any) => {
  console.log('Creating mission for parent:', parentId, 'with data:', missionData);
  const { data, error } = await supabase
    .from('missions')
    .insert({
      parent_id: parentId,
      ...missionData,
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating mission:', error);
    throw error;
  }
  console.log('Mission created successfully:', data);
  return data;
};

export const assignMissionToChild = async (missionId: string, childId: string) => {
  console.log('Assigning mission', missionId, 'to child', childId);
  const { data, error } = await supabase
    .from('mission_assignments')
    .insert({
      mission_id: missionId,
      child_id: childId,
      status: 'pending',
    })
    .select()
    .single();

  if (error) {
    console.error('Error assigning mission to child:', error);
    throw error;
  }
  console.log('Mission assigned successfully:', data);
  return data;
};

export const getChildMissions = async (childId: string) => {
  console.log('Getting missions for child:', childId);

  try {
    // First, check if there are any mission assignments for this child
    console.log('Fetching mission assignments for child ID:', childId);
    const { data: assignmentsData, error: assignmentsError } = await supabase
      .from('mission_assignments')
      .select('id, mission_id, status, completion_date')
      .eq('child_id', childId);

    if (assignmentsError) {
      console.error('Error fetching mission assignments:', assignmentsError);
      throw assignmentsError;
    }

    console.log('Mission assignments found:', assignmentsData?.length || 0);
    if (assignmentsData && assignmentsData.length > 0) {
      console.log('First few assignments:', assignmentsData.slice(0, 3));
    }

    if (!assignmentsData || assignmentsData.length === 0) {
      console.log('No mission assignments found for child ID:', childId);
      return [];
    }

    // Get the mission details for each assignment
    const missionIds = assignmentsData.map(assignment => assignment.mission_id);
    console.log('Fetching mission details for mission IDs:', missionIds.slice(0, 3), '...');

    const { data: missionsData, error: missionsError } = await supabase
      .from('missions')
      .select('id, title, description, reward, due_date, created_at, parent_id')
      .in('id', missionIds);

    if (missionsError) {
      console.error('Error fetching mission details:', missionsError);
      throw missionsError;
    }

    console.log('Mission details found:', missionsData?.length || 0);
    if (missionsData && missionsData.length > 0) {
      console.log('First few missions:', missionsData.slice(0, 3));
    }

    // Combine the data
    const result = assignmentsData.map(assignment => {
      const mission = missionsData?.find(m => m.id === assignment.mission_id);
      return {
        id: assignment.id,
        mission_id: assignment.mission_id,
        child_id: childId,
        status: assignment.status,
        completion_date: assignment.completion_date,
        mission: mission || undefined
      };
    });

    console.log('Combined mission data count:', result.length);
    console.log('Missions by status:', {
      pending: result.filter(m => m.status === 'pending').length,
      in_progress: result.filter(m => m.status === 'in_progress').length,
      completed: result.filter(m => m.status === 'completed').length,
      verified: result.filter(m => m.status === 'verified').length
    });

    return result;
  } catch (err) {
    console.error('Error in getChildMissions:', err);
    throw err;
  }
};

export const updateMissionStatus = async (assignmentId: string, status: string) => {
  console.log('Updating mission status:', assignmentId, status);

  // First, update the mission assignment status
  const { data, error } = await supabase
    .from('mission_assignments')
    .update({
      status,
      ...(status === 'completed' ? { completion_date: new Date() } : {}),
    })
    .eq('id', assignmentId)
    .select()
    .single();

  if (error) {
    console.error('Error updating mission status:', error);
    throw error;
  }

  console.log('Mission status updated successfully:', data);

  // Then, get the mission details using the RPC function
  try {
    const { data: missionData, error: missionError } = await supabase.rpc(
      'get_mission_assignment_with_details',
      { p_assignment_id: assignmentId }
    );

    if (missionError) {
      console.error('Error fetching mission details after status update:', missionError);
      // Return the basic data if we can't get the details
      return data;
    }

    if (missionData && missionData.length > 0) {
      console.log('Mission details fetched successfully:', missionData[0]);

      // Transform the data to match the expected format
      const result = {
        id: missionData[0].assignment_id,
        mission_id: missionData[0].mission_id,
        child_id: missionData[0].child_id,
        status: missionData[0].status,
        completion_date: missionData[0].completion_date,
        mission: {
          id: missionData[0].mission_id,
          title: missionData[0].title,
          description: missionData[0].description,
          reward: missionData[0].reward,
          due_date: missionData[0].due_date,
          created_at: missionData[0].created_at,
          parent_id: missionData[0].parent_id
        }
      };

      return result;
    }
  } catch (detailsError) {
    console.error('Error in mission details fetch:', detailsError);
  }

  // Return the basic data if we can't get the details
  return data;
};

// SOS Alerts
export type SOSAlert = {
  id: string;
  created_at: string;
  child_id: string;
  latitude: number;
  longitude: number;
  acknowledged: boolean;
  timestamp: string;
  child_name?: string;
  additional_info?: string;
};

export const getSOSAlertById = async (alertId: string) => {
  try {
    console.log('Getting SOS alert details for ID:', alertId);

    const { data, error } = await supabase
      .from('sos_alerts')
      .select('*, children:child_id(name, age)')
      .eq('id', alertId)
      .single();

    if (error) {
      console.error('Error getting SOS alert details:', error);
      throw error;
    }

    console.log('SOS alert details retrieved successfully');
    return data;
  } catch (err) {
    console.error('Error in getSOSAlertById:', err);
    throw err;
  }
};

// User profile functions
export async function getUserProfile(userId: string) {
  try {
    // First try to get data from the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (!error) {
      return data;
    }

    // If no data in profiles table, try the users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error fetching user data from users table:', userError);
      throw userError;
    }

    return userData;
  } catch (error) {
    console.error('Error in getUserProfile:', error);
    // Return a minimal profile to avoid breaking the app
    return {
      id: userId,
      name: 'User',
      user_type: 'unknown'
    };
  }
}

export async function updateUserProfile(userId: string, updates: any) {
  try {
    // First try to update the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select();

    if (!error) {
      return data;
    }

    // If no data in profiles table, try the users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select();

    if (userError) {
      console.error('Error updating user data in users table:', userError);
      throw userError;
    }

    return userData;
  } catch (error) {
    console.error('Error in updateUserProfile:', error);
    throw error;
  }
}

// SOS alert functions
export async function createSOSAlert(childId: string, latitude: number, longitude: number, additional_info?: string) {
  console.log('Creating SOS alert for child:', childId);
  console.log('Location:', { latitude, longitude });

  let alertData;
  let notificationSent = false;
  let parentFound = false;

  try {
    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();
    console.log('Current authenticated user:', user ? user.id : 'No user');
    console.log('Child ID for SOS alert:', childId);

    // First create the alert using the RPC function
    const { data, error } = await supabase.rpc(
      'create_sos_alert',
      {
        p_child_id: childId,
        p_latitude: latitude,
        p_longitude: longitude,
        p_additional_info: additional_info,
        p_timestamp: new Date().toISOString()
      }
    );

    if (error) {
      console.error('Error from RPC function:', error);
      throw error;
    }

    alertData = data;
    console.log('SOS alert created successfully:', alertData);

    // Se la funzione RPC restituisce già le informazioni del genitore, usale
    if (alertData && alertData.parent_token && alertData.success) {
      console.log('RPC function returned parent token:', alertData.parent_token);
      parentFound = true;

      // Send push notification to parent with high priority
      const locationStr = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
      const title = `🚨 SOS Alert from ${alertData.child_name}`;
      const body = `${alertData.child_name} has sent an emergency alert from ${locationStr}`;

      await sendPushNotification(alertData.parent_token, title, body, {
        type: 'sos',
        childName: alertData.child_name,
        location: locationStr,
        timestamp: new Date().toISOString(),
        priority: 'high',
        sound: 'default',
        badge: 1,
        alertId: alertData.alert_id
      });

      notificationSent = true;
      return { alertData, notificationSent, parentFound };
    }
  } catch (err) {
    console.error('Error creating SOS alert:', err);
    throw err;
  }

  try {
    // Then get the child details to include in notification
    const { data: childData, error: childError } = await supabase
      .from('users')
      .select('name, id')
      .eq('id', childId);

    if (childError) {
      console.error('Error fetching child data for notification:', childError);
      // Create a default notification without child name
      await createSOSNotification('Your child');
      notificationSent = true;
      return { alertData, notificationSent, parentFound };
    }

    // If no child data found, use a default name
    const childName = childData && childData.length > 0 ? childData[0].name : 'Your child';

    // Get parent push token using the new helper function
    const parentToken = await getParentPushToken(childId);

    if (parentToken) {
      console.log('Found parent push token for SOS notification:', parentToken);
      parentFound = true;

      // Send push notification to parent with high priority (NO local notification on child device)
      const locationStr = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
      const title = `🚨 SOS Alert from ${childName}`;
      const body = `${childName} has sent an emergency alert from ${locationStr}`;

      await sendPushNotification(parentToken, title, body, {
        type: 'sos',
        childName,
        location: locationStr,
        timestamp: new Date().toISOString(),
        priority: 'high',
        sound: 'default',
        badge: 1
      });

      notificationSent = true;
      return { alertData, notificationSent, parentFound };
    }

    // If we couldn't get the parent token with the helper function, try the old methods
    console.log('No parent token found with helper function, trying alternative methods');

    // Try a direct SQL query to bypass RLS
    try {
      const { data: directData, error: directError } = await supabase.rpc(
        'get_parent_for_child',
        { p_child_id: childId }
      );

      console.log('Direct query result:', { directData, directError });

      if (!directError && directData && directData.length > 0) {
        // Parent found via direct query
        parentFound = true;
        const parentId = directData[0].parent_id;

        // Try to send push notification to parent (NO local notification on child device)
        try {
          const { data: tokenData, error: tokenError } = await supabase
            .from('user_push_tokens')
            .select('token')
            .eq('user_id', parentId);

          if (tokenData && tokenData.length > 0 && !tokenError) {
            const locationStr = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
            const title = `🚨 SOS Alert from ${childName}`;
            const body = `${childName} has sent an emergency alert from ${locationStr}`;

            await sendPushNotification(tokenData[0].token, title, body, {
              type: 'sos',
              childName,
              location: locationStr,
              timestamp: new Date().toISOString(),
              priority: 'high',
              sound: 'default',
              badge: 1
            });

            notificationSent = true;
          }
        } catch (notifError) {
          console.error('Error sending push notification:', notifError);
        }

        return { alertData, notificationSent, parentFound };
      }
    } catch (directQueryError) {
      console.error('Error with direct query:', directQueryError);
    }

    // Fall back to standard query if direct query fails
    const { data: relationData, error: relationError } = await supabase
      .from('parent_child_relationships')
      .select('parent_id')
      .eq('child_id', childId);

    console.log('Parent relationship query result:', { relationData, relationError });

    if (relationError) {
      console.error('Error fetching parent relationship for notification:', relationError);
      // Create a notification without trying to send to parent
      await createSOSNotification(childName);
      notificationSent = true;
      return { alertData, notificationSent, parentFound };
    }

    if (!relationData || relationData.length === 0) {
      console.error('No parent relationship found for child:', childId);
      // Create a notification without trying to send to parent
      await createSOSNotification(childName);
      notificationSent = true;
      return { alertData, notificationSent, parentFound };
    }

    // Parent found
    parentFound = true;

    // Try to send push notification to parent (NO local notification on child device)
    try {
      const parentId = relationData[0].parent_id;
      const { data: tokenData, error: tokenError } = await supabase
        .from('user_push_tokens')
        .select('token')
        .eq('user_id', parentId);

      if (tokenData && tokenData.length > 0 && !tokenError) {
        const locationStr = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
        const title = `🚨 SOS Alert from ${childName}`;
        const body = `${childName} has sent an emergency alert from ${locationStr}`;

        await sendPushNotification(tokenData[0].token, title, body, {
          type: 'sos',
          childName,
          location: locationStr,
          timestamp: new Date().toISOString(),
          priority: 'high',
          sound: 'default',
          badge: 1
        });

        notificationSent = true;
      }
    } catch (notifError) {
      console.error('Error sending push notification:', notifError);
    }
  } catch (err) {
    console.error('Error sending SOS alert:', err);
    // Don't rethrow here, we already created the alert successfully
  }

  return { alertData, notificationSent, parentFound };
}

export async function getParentSOSAlerts(parentId?: string) {
  if (!parentId) {
    // Get current user if parentId not provided
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('Authentication required');
    }

    parentId = user.id;
  }

  // Get alerts for children linked to this parent
  const { data, error } = await supabase
    .from('sos_alerts')
    .select(`
      *,
      children:child_id(name, id)
    `)
    .order('timestamp', { ascending: false });

  if (error) {
    throw error;
  }

  // Format the data to include child_name
  return data.map(alert => ({
    ...alert,
    child_name: alert.children?.name
  }));
}

export async function getChildSOSAlerts(childId: string) {
  const { data, error } = await supabase
    .from('sos_alerts')
    .select('*')
    .eq('child_id', childId)
    .order('timestamp', { ascending: false });

  if (error) {
    throw error;
  }

  return data;
}

export async function acknowledgeSOSAlert(alertId: string) {
  const { data, error } = await supabase
    .from('sos_alerts')
    .update({ acknowledged: true })
    .eq('id', alertId)
    .select();

  if (error) {
    throw error;
  }

  return data;
}

// Children management functions
export async function getParentChildren(parentId?: string) {
  if (!parentId) {
    // Get current user if parentId not provided
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('Authentication required');
    }

    parentId = user.id;
  }

  const { data, error } = await supabase
    .from('children')
    .select('*')
    .eq('parent_id', parentId);

  if (error) {
    throw error;
  }

  return data;
}

// Delete a child and all related data
export async function deleteChild(childId: string) {
  console.log('Deleting child:', childId);
  try {
    // Ottieni l'utente corrente
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Utilizziamo la funzione RPC per eliminare il bambino e tutte le sue relazioni
    // Questo garantisce che tutte le operazioni vengano eseguite in una transazione
    console.log('Calling delete_child_with_relationships RPC function');
    const { data, error } = await supabase.rpc('delete_child_with_relationships', {
      p_child_id: childId,
      p_parent_id: user.id
    });

    if (error) {
      console.error('Error in delete_child_with_relationships RPC:', error);
      throw error;
    }

    console.log('RPC function result:', data);

    // Verifica manualmente che la relazione genitore-figlio sia stata eliminata
    const { data: checkRelationship, error: checkError } = await supabase
      .from('parent_child_relationships')
      .select('*')
      .eq('child_id', childId)
      .eq('parent_id', user.id);

    if (checkError) {
      console.error('Error checking relationship:', checkError);
    } else {
      console.log('Relationship check result:', checkRelationship);
      if (checkRelationship && checkRelationship.length > 0) {
        console.warn('Relationship still exists after deletion!');

        // Tenta di eliminare nuovamente la relazione
        const { error: retryError } = await supabase
          .from('parent_child_relationships')
          .delete()
          .eq('child_id', childId)
          .eq('parent_id', user.id);

        if (retryError) {
          console.error('Error in retry delete relationship:', retryError);
        } else {
          console.log('Relationship deleted in retry');
        }
      } else {
        console.log('Relationship successfully deleted');
      }
    }

    console.log('Child deleted successfully');
    return true;
  } catch (err) {
    console.error('Error in deleteChild:', err);
    throw err;
  }
}

// AI Homework Sessions
export const saveHomeworkSession = async (childId: string, question: string, answer: string, imageUrl?: string) => {
  try {
    console.log('Saving homework session for child:', childId);
    console.log('Question:', question.substring(0, 50) + '...');

    // Get current user to verify authentication
    const { data: { user } } = await supabase.auth.getUser();
    console.log('Current authenticated user:', user?.id);

    // Skip authentication check since we're having issues with it
    // Instead, log the childId we're using
    console.log('Using provided childId for homework session:', childId);

    // if (!user) {
    //   throw new Error('User not authenticated');
    // }

    // Try to insert into the public table first
    try {
      const { data: publicData, error: publicError } = await supabase
        .from('ai_homework_sessions_public')
        .insert({
          child_id: childId,
          question,
          answer,
          image_url: imageUrl,
          timestamp: new Date(),
        })
        .select()
        .single();

      if (!publicError) {
        console.log('Homework session saved to public table successfully:', publicData.id);
        return publicData;
      } else {
        console.error('Error saving to public table, trying original table:', publicError);
      }
    } catch (publicErr) {
      console.error('Exception saving to public table:', publicErr);
    }

    // Fall back to the original table
    const { data, error } = await supabase
      .from('ai_homework_sessions')
      .insert({
        child_id: childId,
        question,
        answer,
        image_url: imageUrl,
        timestamp: new Date(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error saving homework session:', error);
      throw error;
    }

    console.log('Homework session saved successfully:', data.id);
    return data;
  } catch (err) {
    console.error('Error in saveHomeworkSession:', err);
    throw err;
  }
};

export const getHomeworkSessions = async (childId: string) => {
  try {
    // Try to get from public table first
    const { data: publicData, error: publicError } = await supabase
      .from('ai_homework_sessions_public')
      .select('*')
      .eq('child_id', childId)
      .order('timestamp', { ascending: false });

    if (!publicError && publicData.length > 0) {
      console.log('Retrieved homework sessions from public table:', publicData.length);
      return publicData;
    }

    // Fall back to original table
    const { data, error } = await supabase
      .from('ai_homework_sessions')
      .select('*')
      .eq('child_id', childId)
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error getting homework sessions:', error);
      throw error;
    }

    return data;
  } catch (err) {
    console.error('Error in getHomeworkSessions:', err);
    throw err;
  }
};

// Missions
export const deleteMission = async (missionId: string, parentId: string) => {
  console.log('Deleting mission:', missionId, 'for parent:', parentId);
  try {
    // Step 1: Delete all mission assignments for this mission
    const { error: assignmentError } = await supabase
      .from('mission_assignments')
      .delete()
      .eq('mission_id', missionId);

    if (assignmentError) {
      console.error('Error deleting mission assignments:', assignmentError);
      throw assignmentError;
    }

    // Step 2: Delete the mission itself
    const { data, error } = await supabase
      .from('missions')
      .delete()
      .eq('id', missionId)
      .eq('parent_id', parentId) // Ensure the parent is the owner
      .select()
      .single();

    if (error) {
      console.error('Error deleting mission:', error);
      throw error;
    }

    console.log('Mission deleted successfully:', data);
    return data;
  } catch (err) {
    console.error('Error in deleteMission:', err);
    throw err;
  }
};

// App usage
export const saveAppUsage = async (childId: string, appName: string, durationSeconds: number) => {
  try {
    console.log(`[saveAppUsage] Saving app usage for child ${childId}: ${appName} for ${durationSeconds} seconds`);

    // Prima verifichiamo se la stored procedure esiste
    try {
      // Utilizziamo una stored procedure (RPC) per bypassare le policy RLS
      const { data, error } = await supabase.rpc(
        'save_app_usage',
        {
          p_child_id: childId,
          p_app_name: appName,
          p_usage_duration: durationSeconds,
          p_date: new Date().toISOString()
        }
      );

      if (error) {
        console.error('[saveAppUsage] Error from RPC function:', error);
        // Se l'errore è che la funzione non esiste, proviamo con l'inserimento diretto
        if (error.message.includes('function') && error.message.includes('does not exist')) {
          console.log('[saveAppUsage] RPC function does not exist, trying direct insert');
          throw new Error('RPC function does not exist');
        }
        throw error;
      }

      console.log('[saveAppUsage] App usage saved successfully via RPC:', data);
      return data;
    } catch (rpcError) {
      // Se la stored procedure non esiste o fallisce, proviamo con l'inserimento diretto
      console.log('[saveAppUsage] Trying direct insert as fallback');

      const { data: insertData, error: insertError } = await supabase
        .from('app_usage')
        .insert({
          child_id: childId,
          app_name: appName,
          usage_duration: durationSeconds,
          date: new Date().toISOString()
        })
        .select()
        .single();

      if (insertError) {
        console.error('[saveAppUsage] Error with direct insert:', insertError);
        throw insertError;
      }

      console.log('[saveAppUsage] App usage saved successfully via direct insert:', insertData);
      return insertData;
    }
  } catch (err) {
    console.error('[saveAppUsage] Error saving app usage:', err);

    // Come ultima risorsa, proviamo a fare un insert senza select
    try {
      console.log('[saveAppUsage] Trying last resort insert without select');
      const { error: lastResortError } = await supabase
        .from('app_usage')
        .insert({
          child_id: childId,
          app_name: appName,
          usage_duration: durationSeconds,
          date: new Date().toISOString()
        });

      if (lastResortError) {
        console.error('[saveAppUsage] Last resort insert failed:', lastResortError);
        throw lastResortError;
      }

      console.log('[saveAppUsage] App usage saved successfully via last resort insert');
      return { success: true };
    } catch (lastError) {
      console.error('[saveAppUsage] All attempts to save app usage failed:', lastError);
      throw lastError;
    }
  }
};

export const getAppUsage = async (childId: string, startDate?: Date, endDate?: Date) => {
  try {
    console.log(`[getAppUsage] Fetching app usage for child ${childId} from ${startDate?.toISOString() || 'all time'} to ${endDate?.toISOString() || 'now'}`);

    let query = supabase
      .from('app_usage')
      .select('*')
      .eq('child_id', childId);

    if (startDate) {
      query = query.gte('date', startDate.toISOString());
    }

    if (endDate) {
      query = query.lte('date', endDate.toISOString());
    }

    const { data, error } = await query.order('date', { ascending: false });

    if (error) {
      console.error('[getAppUsage] Error fetching app usage:', error);
      throw error;
    }

    console.log(`[getAppUsage] Retrieved ${data?.length || 0} app usage records`);

    // Se non ci sono dati, proviamo a generare alcuni dati di esempio per i test
    if (!data || data.length === 0) {
      console.log('[getAppUsage] No app usage data found, checking if we should generate test data');

      // Verifichiamo se siamo in modalità di sviluppo
      if (__DEV__) {
        console.log('[getAppUsage] Development mode detected, NOT generating test data anymore');
        // Non generiamo più dati di test, vogliamo vedere solo dati reali
        return [];
      }

      return [];
    }

    // Logghiamo alcuni esempi di dati per debug
    if (data && data.length > 0) {
      console.log('[getAppUsage] Sample app usage data:', data.slice(0, 3));
    }

    return data || [];
  } catch (err) {
    console.error('[getAppUsage] Error in getAppUsage function:', err);
    // In caso di errore, restituiamo un array vuoto invece di lanciare un'eccezione
    return [];
  }
};

// App blocking functions
export const blockApp = async (childId: string, appName: string) => {
  try {
    console.log(`[blockApp] Blocking app ${appName} for child ${childId}`);

    // Check if there's already a blocked_apps table entry for this child and app
    const { data: existingBlock, error: checkError } = await supabase
      .from('blocked_apps')
      .select('id')
      .eq('child_id', childId)
      .eq('app_name', appName) // Questo deve essere il nome del pacchetto originale
      .maybeSingle();

    if (checkError) {
      console.error('[blockApp] Error checking if app is already blocked:', checkError);
      throw checkError;
    }

    // If the app is already blocked, just return the existing entry
    if (existingBlock) {
      console.log('[blockApp] App is already blocked, returning existing entry');
      return existingBlock;
    }

    // Otherwise, create a new blocked_apps entry
    const { data, error } = await supabase
      .from('blocked_apps')
      .insert({
        child_id: childId,
        app_name: appName, // Questo deve essere il nome del pacchetto originale
        blocked_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('[blockApp] Error blocking app:', error);
      throw error;
    }

    console.log('[blockApp] App blocked successfully:', data);
    return data;
  } catch (err) {
    console.error('[blockApp] Error in blockApp:', err);
    throw err;
  }
};

export const unblockApp = async (childId: string, appName: string) => {
  try {
    console.log(`[unblockApp] Unblocking app ${appName} for child ${childId}`);

    const { data, error } = await supabase
      .from('blocked_apps')
      .delete()
      .eq('child_id', childId)
      .eq('app_name', appName) // Questo deve essere il nome del pacchetto originale
      .select()
      .maybeSingle();

    if (error) {
      console.error('[unblockApp] Error unblocking app:', error);
      throw error;
    }

    console.log('[unblockApp] App unblocked successfully');
    return data || { success: true };
  } catch (err) {
    console.error('[unblockApp] Error in unblockApp:', err);
    throw err;
  }
};

// Get blocked apps for a child
export const getBlockedApps = async (childId: string) => {
  try {
    // Get permanently blocked apps
    const { data: permanentData, error: permanentError } = await supabase
      .from('blocked_apps')
      .select('app_name, blocked_at')
      .eq('child_id', childId);

    if (permanentError) {
      console.error('Error getting permanently blocked apps:', permanentError);
      throw permanentError;
    }

    // Get temporarily blocked apps
    const { data: temporaryData, error: temporaryError } = await supabase
      .from('temporary_blocks')
      .select('app_name, end_time')
      .eq('child_id', childId);

    if (temporaryError) {
      console.error('Error getting temporarily blocked apps:', temporaryError);
      throw temporaryError;
    }

    // Convert to a map for easier lookup
    const blockedAppsMap: { [key: string]: boolean } = {};

    // Add permanently blocked apps
    permanentData.forEach(app => {
      blockedAppsMap[app.app_name] = true;
    });

    // Add temporarily blocked apps that haven't expired
    const now = new Date();
    temporaryData.forEach(app => {
      const endTime = new Date(app.end_time);
      if (endTime > now) {
        blockedAppsMap[app.app_name] = true;
      }
    });

    return blockedAppsMap;
  } catch (err) {
    console.error('Error in getBlockedApps:', err);
    throw err;
  }
};

// Temporary block functions
export const setTemporaryBlock = async (childId: string, appName: string, endTime: string) => {
  try {
    console.log(`[setTemporaryBlock] Setting temporary block for app ${appName} until ${endTime} for child ${childId}`);

    // Check if there's already a temporary block for this app
    const { data: existingBlock, error: checkError } = await supabase
      .from('temporary_blocks')
      .select('id')
      .eq('child_id', childId)
      .eq('app_name', appName)
      .maybeSingle();

    if (checkError) {
      console.error('[setTemporaryBlock] Error checking existing temporary block:', checkError);
      throw checkError;
    }

    // Upsert the temporary block
    const { data, error } = await supabase
      .from('temporary_blocks')
      .upsert({
        id: existingBlock?.id,
        child_id: childId,
        app_name: appName,
        end_time: endTime,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('[setTemporaryBlock] Error setting temporary block:', error);
      throw error;
    }

    console.log('[setTemporaryBlock] Temporary block set successfully:', data);
    return data;
  } catch (err) {
    console.error('[setTemporaryBlock] Error in setTemporaryBlock:', err);
    throw err;
  }
};

export const removeTemporaryBlock = async (childId: string, appName: string) => {
  try {
    console.log(`[removeTemporaryBlock] Removing temporary block for app ${appName} for child ${childId}`);

    const { data, error } = await supabase
      .from('temporary_blocks')
      .delete()
      .eq('child_id', childId)
      .eq('app_name', appName)
      .select()
      .maybeSingle();

    if (error) {
      console.error('[removeTemporaryBlock] Error removing temporary block:', error);
      throw error;
    }

    console.log('[removeTemporaryBlock] Temporary block removed successfully');
    return data || { success: true };
  } catch (err) {
    console.error('[removeTemporaryBlock] Error in removeTemporaryBlock:', err);
    throw err;
  }
};

// App time limits functions
export const setAppTimeLimit = async (childId: string, appName: string, dailyLimitMinutes: number) => {
  try {
    console.log(`[setAppTimeLimit] Setting time limit for app ${appName} to ${dailyLimitMinutes} minutes for child ${childId}`);

    // Assicuriamoci che il nome dell'app sia il nome del pacchetto originale
    // Questo è importante perché il blocco deve funzionare con il nome del pacchetto, non con il nome leggibile
    const { data, error } = await supabase
      .from('app_time_limits')
      .upsert({
        child_id: childId,
        app_name: appName, // Questo deve essere il nome del pacchetto originale
        daily_limit_minutes: dailyLimitMinutes,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('[setAppTimeLimit] Error setting app time limit:', error);
      throw error;
    }

    console.log('[setAppTimeLimit] Time limit set successfully:', data);
    return data;
  } catch (err) {
    console.error('[setAppTimeLimit] Error in setAppTimeLimit:', err);
    throw err;
  }
};

export const removeAppTimeLimit = async (childId: string, appName: string) => {
  try {
    console.log(`[removeAppTimeLimit] Removing time limit for app ${appName} for child ${childId}`);

    const { data, error } = await supabase
      .from('app_time_limits')
      .delete()
      .eq('child_id', childId)
      .eq('app_name', appName) // Questo deve essere il nome del pacchetto originale
      .select()
      .maybeSingle();

    if (error) {
      console.error('[removeAppTimeLimit] Error removing app time limit:', error);
      throw error;
    }

    console.log('[removeAppTimeLimit] Time limit removed successfully');
    return data || { success: true };
  } catch (err) {
    console.error('[removeAppTimeLimit] Error in removeAppTimeLimit:', err);
    throw err;
  }
};

export const getAppTimeLimits = async (childId: string) => {
  try {
    const { data, error } = await supabase
      .from('app_time_limits')
      .select('app_name, daily_limit_minutes')
      .eq('child_id', childId);

    if (error) {
      console.error('Error getting app time limits:', error);
      throw error;
    }

    // Convert to a map for easier lookup
    const timeLimitsMap: { [key: string]: number } = {};
    data.forEach(app => {
      timeLimitsMap[app.app_name] = app.daily_limit_minutes;
    });

    return timeLimitsMap;
  } catch (err) {
    console.error('Error in getAppTimeLimits:', err);
    throw err;
  }
};

// Function to check if a child has exceeded the time limit for an app
export const checkAppTimeLimit = async (childId: string, appName: string) => {
  try {
    // Get the time limit for this app
    const { data: limitData, error: limitError } = await supabase
      .from('app_time_limits')
      .select('daily_limit_minutes')
      .eq('child_id', childId)
      .eq('app_name', appName)
      .maybeSingle();

    if (limitError) {
      console.error('Error checking app time limit:', limitError);
      throw limitError;
    }

    // If no time limit is set, return null
    if (!limitData) {
      return { hasLimit: false };
    }

    // Get today's usage for this app
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const { data: usageData, error: usageError } = await supabase
      .from('app_usage')
      .select('usage_duration')
      .eq('child_id', childId)
      .eq('app_name', appName)
      .gte('date', today.toISOString());

    if (usageError) {
      console.error('Error getting app usage:', usageError);
      throw usageError;
    }

    // Calculate total usage in minutes
    const totalUsageSeconds = usageData.reduce((total, usage) => total + usage.usage_duration, 0);
    const totalUsageMinutes = Math.floor(totalUsageSeconds / 60);

    // Check if the limit has been exceeded
    const limitExceeded = totalUsageMinutes >= limitData.daily_limit_minutes;
    const remainingMinutes = Math.max(0, limitData.daily_limit_minutes - totalUsageMinutes);

    return {
      hasLimit: true,
      limitMinutes: limitData.daily_limit_minutes,
      usageMinutes: totalUsageMinutes,
      limitExceeded,
      remainingMinutes,
    };
  } catch (err) {
    console.error('Error in checkAppTimeLimit:', err);
    throw err;
  }
};

/**
 * Ottiene i dettagli di un bambino specifico tramite il suo ID.
 */
export const getChildById = async (childId: string) => {
  try {
    console.log('Getting child details for ID:', childId);

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', childId)
      .eq('user_type', 'child')
      .single();

    if (error) {
      console.error('Error getting child details:', error);
      throw error;
    }

    console.log('Child details retrieved successfully');
    return data;
  } catch (err) {
    console.error('Error in getChildById:', err);
    throw err;
  }
};

/**
 * Aggiorna i dettagli di un bambino.
 */
export const updateChildDetails = async (childId: string, details: { name?: string; age?: number }) => {
  try {
    console.log('Updating child details for ID:', childId, details);

    const { data, error } = await supabase
      .from('users')
      .update(details)
      .eq('id', childId)
      .eq('user_type', 'child')
      .select();

    if (error) {
      console.error('Error updating child details:', error);
      throw error;
    }

    console.log('Child details updated successfully');
    return data;
  } catch (err) {
    console.error('Error in updateChildDetails:', err);
    throw err;
  }
};

// Child Routes (Tragitti)

/**
 * Aggiunge un punto al tragitto di un bambino.
 * Se non esiste un tragitto recente (ultimi 30 minuti), ne crea uno nuovo.
 */
export const addRoutePoint = async (
  childId: string,
  latitude: number,
  longitude: number,
  timestamp: Date = new Date(),
  accuracy?: number,
  batteryLevel?: number
) => {
  try {
    console.log('Adding route point for child:', childId);

    const { data, error } = await supabase.rpc(
      'add_route_point',
      {
        p_child_id: childId,
        p_latitude: latitude,
        p_longitude: longitude,
        p_timestamp: timestamp.toISOString(),
        p_accuracy: accuracy,
        p_battery_level: batteryLevel
      }
    );

    if (error) {
      console.error('Error adding route point:', error);
      throw error;
    }

    console.log('Route point added successfully:', data);
    return data;
  } catch (err) {
    console.error('Error in addRoutePoint:', err);
    throw err;
  }
};

/**
 * Ottiene i tragitti di un bambino in un determinato periodo.
 * Se non vengono specificate le date, restituisce i tragitti degli ultimi 7 giorni.
 */
export const getChildRoutes = async (
  childId: string,
  startDate?: Date,
  endDate?: Date
) => {
  try {
    console.log('Getting routes for child:', childId);

    // Use direct SQL queries instead of the stored procedure
    // First, get the routes
    const startDateStr = startDate ? startDate.toISOString().split('T')[0] : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const endDateStr = endDate ? endDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0];

    const { data: routesData, error: routesError } = await supabase
      .from('child_routes')
      .select('id, child_id, route_date, start_time, end_time, distance_meters, created_at, updated_at')
      .eq('child_id', childId)
      .gte('route_date', startDateStr)
      .lte('route_date', endDateStr)
      .order('route_date', { ascending: false })
      .order('start_time', { ascending: false });

    if (routesError) {
      console.error('Error getting child routes:', routesError);
      throw routesError;
    }

    // If no routes found, return empty result
    if (!routesData || routesData.length === 0) {
      console.log('No routes found for child');
      return { routes: [], points: {} };
    }

    // Get the route IDs
    const routeIds = routesData.map(route => route.id);

    // Get the route points for each route
    const { data: pointsData, error: pointsError } = await supabase
      .from('route_points')
      .select('id, route_id, latitude, longitude, timestamp, accuracy, battery_level')
      .in('route_id', routeIds)
      .order('timestamp', { ascending: true });

    if (pointsError) {
      console.error('Error getting route points:', pointsError);
      throw pointsError;
    }

    // Group the points by route_id
    const pointsByRoute = {};
    pointsData.forEach(point => {
      if (!pointsByRoute[point.route_id]) {
        pointsByRoute[point.route_id] = [];
      }
      pointsByRoute[point.route_id].push(point);
    });

    // Format the result to match the expected structure
    const result = {
      routes: routesData,
      points: pointsByRoute
    };

    console.log('Child routes retrieved successfully');
    return result;
  } catch (err) {
    console.error('Error in getChildRoutes:', err);
    throw err;
  }
};

/**
 * Ottiene i tragitti di un bambino per una data specifica.
 */
export const getChildRoutesForDate = async (
  childId: string,
  date: Date
) => {
  try {
    const dateString = date.toISOString().split('T')[0];
    console.log(`Getting routes for child ${childId} on date ${dateString}`);

    return await getChildRoutes(childId, date, date);
  } catch (err) {
    console.error('Error in getChildRoutesForDate:', err);
    throw err;
  }
};

/**
 * Ottiene i tragitti di un bambino per oggi.
 */
export const getChildRoutesForToday = async (childId: string) => {
  try {
    const today = new Date();
    console.log(`Getting routes for child ${childId} for today`);

    return await getChildRoutesForDate(childId, today);
  } catch (err) {
    console.error('Error in getChildRoutesForToday:', err);
    throw err;
  }
};

/**
 * Ottiene le statistiche dei tragitti di un bambino per una data specifica.
 */
export const getChildRouteStats = async (childId: string, date: Date) => {
  try {
    const dateString = date.toISOString().split('T')[0];
    console.log(`Getting route stats for child ${childId} on date ${dateString}`);

    const { data, error } = await supabase.rpc(
      'get_child_routes_stats',
      {
        p_child_id: childId,
        p_date: dateString
      }
    );

    if (error) {
      console.error('Error getting child route stats:', error);
      throw error;
    }

    console.log('Child route stats retrieved successfully');
    return data;
  } catch (err) {
    console.error('Error in getChildRouteStats:', err);
    throw err;
  }
};

/**
 * Ottiene le attività recenti di tutti i bambini di un genitore
 * @param parentId ID del genitore
 * @param limit Numero massimo di attività da restituire (default: 10)
 * @returns Array di attività recenti
 */
export const getRecentActivities = async (parentId: string, limit: number = 10) => {
  try {
    console.log(`[getRecentActivities] Getting recent activities for parent ${parentId}`);

    // Prima otteniamo l'elenco dei bambini del genitore
    const children = await getChildrenForParent(parentId);
    if (!children || children.length === 0) {
      console.log('[getRecentActivities] No children found for parent');
      return [];
    }

    const childIds = children.map(child => child.child_id);
    console.log(`[getRecentActivities] Found ${childIds.length} children:`, childIds);

    // Creiamo una mappa per associare rapidamente l'ID del bambino al suo nome
    const childNameMap: {[key: string]: string} = {};
    children.forEach(child => {
      childNameMap[child.child_id] = child.users?.name || 'Child';
    });

    // Array per memorizzare tutte le attività
    const allActivities: any[] = [];

    // 1. Ottieni le attività di posizione dalla tabella location_history
    try {
      const { data: locationData, error: locationError } = await supabase
        .from('location_history')
        .select('id, child_id, latitude, longitude, timestamp')
        .in('child_id', childIds)
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (locationError) {
        console.error('[getRecentActivities] Error getting location history:', locationError);
      } else if (locationData && locationData.length > 0) {
        console.log(`[getRecentActivities] Found ${locationData.length} location activities`);

        // Convertiamo i dati di posizione in attività
        const locationActivities = locationData.map(location => ({
          id: `location_${location.id}`,
          type: 'location',
          childId: location.child_id,
          childName: childNameMap[location.child_id],
          latitude: location.latitude,
          longitude: location.longitude,
          location: 'Location updated', // Potremmo fare geocoding inverso qui per ottenere l'indirizzo
          timestamp: location.timestamp
        }));

        allActivities.push(...locationActivities);
      }
    } catch (locationErr) {
      console.error('[getRecentActivities] Exception getting location history:', locationErr);
    }

    // 2. Ottieni le attività delle missioni dalla tabella mission_assignments
    try {
      const { data: missionData, error: missionError } = await supabase
        .from('mission_assignments')
        .select('id, child_id, mission_id, status, completion_date, updated_at, missions:mission_id(title)')
        .in('child_id', childIds)
        .order('updated_at', { ascending: false })
        .limit(limit);

      if (missionError) {
        console.error('[getRecentActivities] Error getting mission assignments:', missionError);
      } else if (missionData && missionData.length > 0) {
        console.log(`[getRecentActivities] Found ${missionData.length} mission activities`);

        // Convertiamo i dati delle missioni in attività
        const missionActivities = missionData.map(mission => ({
          id: `mission_${mission.id}`,
          type: 'mission',
          childId: mission.child_id,
          childName: childNameMap[mission.child_id],
          missionName: mission.missions?.title || 'Mission',
          status: mission.status,
          timestamp: mission.updated_at || mission.completion_date
        }));

        allActivities.push(...missionActivities);
      }
    } catch (missionErr) {
      console.error('[getRecentActivities] Exception getting mission assignments:', missionErr);
    }

    // 3. Ottieni le attività di utilizzo delle app dalla tabella app_usage
    try {
      const { data: appUsageData, error: appUsageError } = await supabase
        .from('app_usage')
        .select('id, child_id, app_name, usage_duration, date')
        .in('child_id', childIds)
        .order('date', { ascending: false })
        .limit(limit);

      if (appUsageError) {
        console.error('[getRecentActivities] Error getting app usage:', appUsageError);
      } else if (appUsageData && appUsageData.length > 0) {
        console.log(`[getRecentActivities] Found ${appUsageData.length} app usage activities`);

        // Convertiamo i dati di utilizzo delle app in attività
        const appUsageActivities = appUsageData.map(usage => ({
          id: `app_usage_${usage.id}`,
          type: 'app_usage',
          childId: usage.child_id,
          childName: childNameMap[usage.child_id],
          appName: usage.app_name,
          duration: usage.usage_duration,
          timestamp: usage.date
        }));

        allActivities.push(...appUsageActivities);
      }
    } catch (appUsageErr) {
      console.error('[getRecentActivities] Exception getting app usage:', appUsageErr);
    }

    // 4. Ottieni le attività dell'assistente compiti dalla tabella ai_homework_sessions_public
    try {
      const { data: homeworkData, error: homeworkError } = await supabase
        .from('ai_homework_sessions_public')
        .select('id, child_id, question, timestamp')
        .in('child_id', childIds)
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (homeworkError) {
        console.error('[getRecentActivities] Error getting homework sessions:', homeworkError);
      } else if (homeworkData && homeworkData.length > 0) {
        console.log(`[getRecentActivities] Found ${homeworkData.length} homework activities`);

        // Convertiamo i dati delle sessioni di compiti in attività
        const homeworkActivities = homeworkData.map(session => ({
          id: `homework_${session.id}`,
          type: 'homework',
          childId: session.child_id,
          childName: childNameMap[session.child_id],
          question: session.question,
          timestamp: session.timestamp
        }));

        allActivities.push(...homeworkActivities);
      }
    } catch (homeworkErr) {
      console.error('[getRecentActivities] Exception getting homework sessions:', homeworkErr);
    }

    // Ordina tutte le attività per timestamp (dalla più recente alla meno recente)
    allActivities.sort((a, b) => {
      const dateA = new Date(a.timestamp).getTime();
      const dateB = new Date(b.timestamp).getTime();
      return dateB - dateA;
    });

    // Limita il numero di attività restituite
    const limitedActivities = allActivities.slice(0, limit);
    console.log(`[getRecentActivities] Returning ${limitedActivities.length} activities`);

    return limitedActivities;
  } catch (err) {
    console.error('[getRecentActivities] Error getting recent activities:', err);
    return [];
  }
};

// Default export for the Supabase client
export { supabase };
export default supabase;