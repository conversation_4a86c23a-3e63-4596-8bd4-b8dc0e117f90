/**
 * Billing Configuration
 * 
 * This file contains configuration for in-app purchases and subscriptions
 */

import { Platform } from 'react-native';

// Subscription Product IDs
export const SUBSCRIPTION_SKUS = {
  MONTHLY: 'com.evotech.kidsafety.monthly',
  YEARLY: 'com.evotech.kidsafety.annual',
};

// RSA Public Key for Android (Base64 encoded)
export const ANDROID_BILLING_KEY = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3a9VXB8MQI72xnPZDZKY/TqI253wuk78UBFs+wgy+isemPBP9VxiGSl/PjgXF9jfogUA1eI25O5knhxvAhfLx5ZGOHpPNm30XhPfILAtHHdzEO3SZq2nj/xoWtTEl+bdmgqdsx2Ej8rmdWwiGi+hPyCplgCWdJfUiCTOazB9MxoqTWYmDY0Bf++YGqXuFhfYKZdlPSwzlviOIpVaVmFRBabM/6bwhisBUWr8jTJsIFgECdvApUTxDSydArk59ZiTI06Nh2DK+3P05Mh4cdcsGvnhn24lQqiOk+nAEr6RoSc3f6Sfrsoc0ZDUMAQ9IGlp1NmPlmdNc9DdC/s2PWVngQIDAQAB';

// Helper function to get the appropriate subscription SKUs based on platform
export const getSubscriptionSkus = () => {
  return [SUBSCRIPTION_SKUS.MONTHLY, SUBSCRIPTION_SKUS.YEARLY];
};

// Helper function to get the appropriate billing key based on platform
export const getBillingKey = () => {
  return Platform.OS === 'android' ? ANDROID_BILLING_KEY : '';
};

export default {
  SUBSCRIPTION_SKUS,
  ANDROID_BILLING_KEY,
  getSubscriptionSkus,
  getBillingKey,
}; 