import { NativeModules, Platform, NativeEventEmitter } from 'react-native';

// Verifica se il modulo nativo è disponibile
const SystemSettingsModule = NativeModules.SystemSettingsModule || {
  // Implementazione di fallback per quando il modulo nativo non è disponibile
  openBatteryOptimizationSettings: async () => {
    console.warn('SystemSettingsModule non disponibile: openBatteryOptimizationSettings');
    return false;
  },
  openNotificationListenerSettings: async () => {
    console.warn('SystemSettingsModule non disponibile: openNotificationListenerSettings');
    return false;
  },
  openLocationSettings: async () => {
    console.warn('SystemSettingsModule non disponibile: openLocationSettings');
    return false;
  },
  isBatteryOptimizationDisabled: async () => {
    console.warn('SystemSettingsModule non disponibile: isBatteryOptimizationDisabled');
    // In modalità di sviluppo, restituiamo false per forzare la verifica
    return false;
  },
  hasNotificationListenerAccess: async () => {
    console.warn('SystemSettingsModule non disponibile: hasNotificationListenerAccess');
    // In modalità di sviluppo, restituiamo false per forzare la verifica
    return false;
  }
};

// Crea un emettitore di eventi nativo se il modulo è disponibile
const eventEmitter = SystemSettingsModule ? new NativeEventEmitter(NativeModules.SystemSettingsModule) : null;

// Funzione per ascoltare i cambiamenti dello stato dell'ottimizzazione della batteria
export const listenToBatteryOptimizationChanges = (callback: (isDisabled: boolean) => void): (() => void) => {
  if (!eventEmitter) {
    console.warn('SystemSettingsModule non disponibile: impossibile ascoltare i cambiamenti');
    return () => {}; // Funzione di pulizia vuota
  }

  // Ascolta l'evento di cambiamento dello stato
  const subscription = eventEmitter.addListener('BatteryOptimizationStatusChanged', (event) => {
    console.log('[SystemSettingsNative] Battery optimization status changed:', event);
    callback(event.isDisabled);
  });

  // Restituisce una funzione per rimuovere il listener
  return () => {
    subscription.remove();
  };
};

/**
 * Apre le impostazioni di ottimizzazione della batteria
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
export const openBatteryOptimizationSettingsNative = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    console.log('[SystemSettingsNative] Not on Android, cannot open battery optimization settings');
    return false;
  }

  try {
    console.log('[SystemSettingsNative] Opening battery optimization settings with native module');
    return await SystemSettingsModule.openBatteryOptimizationSettings();
  } catch (error) {
    console.error('[SystemSettingsNative] Error opening battery optimization settings:', error);
    return false;
  }
};

/**
 * Apre le impostazioni di accesso alle notifiche
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
export const openNotificationListenerSettingsNative = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    console.log('[SystemSettingsNative] Not on Android, cannot open notification listener settings');
    return false;
  }

  try {
    console.log('[SystemSettingsNative] Opening notification listener settings with native module');
    return await SystemSettingsModule.openNotificationListenerSettings();
  } catch (error) {
    console.error('[SystemSettingsNative] Error opening notification listener settings:', error);
    return false;
  }
};

/**
 * Verifica se l'ottimizzazione della batteria è disabilitata per l'app
 * @returns Promise<boolean> - true se l'ottimizzazione è disabilitata, false altrimenti
 */
export const isBatteryOptimizationDisabledNative = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    console.log('[SystemSettingsNative] Not on Android, automatically granting battery optimization');
    return true; // Su iOS, consideriamo l'ottimizzazione come disabilitata
  }

  try {
    console.log('[SystemSettingsNative] Checking battery optimization status with native module');
    return await SystemSettingsModule.isBatteryOptimizationDisabled();
  } catch (error) {
    console.error('[SystemSettingsNative] Error checking battery optimization status:', error);
    return false;
  }
};

/**
 * Verifica se l'app ha accesso alle notifiche
 * @returns Promise<boolean> - true se l'app ha accesso alle notifiche, false altrimenti
 */
export const hasNotificationListenerAccessNative = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    console.log('[SystemSettingsNative] Not on Android, automatically granting notification access');
    return true; // Su iOS, consideriamo l'accesso come concesso
  }

  try {
    console.log('[SystemSettingsNative] Checking notification listener access with native module');
    return await SystemSettingsModule.hasNotificationListenerAccess();
  } catch (error) {
    console.error('[SystemSettingsNative] Error checking notification listener access:', error);
    return false;
  }
};

/**
 * Apre le impostazioni di posizione in background
 * @returns Promise<boolean> - true se le impostazioni sono state aperte con successo
 */
export const openLocationSettingsNative = async (): Promise<boolean> => {
  if (Platform.OS !== 'android') {
    console.log('[SystemSettingsNative] Not on Android, cannot open location settings');
    return false;
  }

  try {
    console.log('[SystemSettingsNative] Opening location settings with native module');
    return await SystemSettingsModule.openLocationSettings();
  } catch (error) {
    console.error('[SystemSettingsNative] Error opening location settings:', error);
    return false;
  }
};
