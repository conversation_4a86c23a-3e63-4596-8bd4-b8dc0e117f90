import * as Crypto from 'expo-crypto';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

/**
 * Modulo di crittografia sicura per KidSafety
 * Implementa crittografia semplificata compatibile con Expo
 *
 * IMPORTANTE: Questo modulo gestisce dati sensibili di minori.
 * Seguire rigorosamente le best practices di sicurezza.
 */

// Costanti di sicurezza
const ENCRYPTION_KEY_ALIAS = 'kidsafety_encryption_key_v2';
const MASTER_KEY_ALIAS = 'kidsafety_master_key_v2';
const IV_LENGTH = 12; // 96 bits per GCM (standard)
const TAG_LENGTH = 16; // 128 bits per GCM (standard)
const KEY_LENGTH = 32; // 256 bits per AES-256

// Interfacce TypeScript
interface EncryptedData {
  data: string;
  iv: string;
  tag?: string;
  algorithm: string;
  version: string;
}

/**
 * Genera o recupera la chiave di crittografia principale
 */
async function getOrCreateEncryptionKey(): Promise<string> {
  try {
    // Prova a recuperare la chiave esistente
    let key = await SecureStore.getItemAsync(ENCRYPTION_KEY_ALIAS);

    if (!key) {
      // Genera una nuova chiave se non esiste
      const keyBytes = await Crypto.getRandomBytesAsync(KEY_LENGTH); // 256 bits
      key = Array.from(keyBytes, byte => byte.toString(16).padStart(2, '0')).join('');

      // Salva la chiave in modo sicuro
      await SecureStore.setItemAsync(ENCRYPTION_KEY_ALIAS, key, {
        requireAuthentication: false,
        keychainService: 'kidsafety-keychain',
        accessGroup: Platform.OS === 'ios' ? 'group.com.evotech.kidsafety' : undefined,
      });

      console.log('🔑 Nuova chiave di crittografia generata e salvata');
    }

    return key;
  } catch (error) {
    console.error('❌ Errore nella gestione della chiave di crittografia:', error);
    throw new Error('Impossibile ottenere la chiave di crittografia');
  }
}

/**
 * Converte una stringa hex in Uint8Array
 */
function hexToUint8Array(hex: string): Uint8Array {
  if (hex.length % 2 !== 0) {
    throw new Error('Stringa hex non valida');
  }
  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
  }
  return bytes;
}

/**
 * Converte un Uint8Array in stringa hex
 */
function uint8ArrayToHex(bytes: Uint8Array): string {
  return Array.from(bytes, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Implementazione semplificata di XOR per crittografia
 */
function xorEncrypt(data: Uint8Array, key: Uint8Array): Uint8Array {
  const result = new Uint8Array(data.length);
  for (let i = 0; i < data.length; i++) {
    result[i] = data[i] ^ key[i % key.length];
  }
  return result;
}

/**
 * Cripta i dati usando un algoritmo semplificato
 */
export async function encryptData(plaintext: string): Promise<string> {
  try {
    if (!plaintext || plaintext.trim().length === 0) {
      throw new Error('Dati da crittografare non forniti o vuoti');
    }

    // Ottieni la chiave di crittografia
    const keyHex = await getOrCreateEncryptionKey();
    const key = hexToUint8Array(keyHex);

    // Genera IV casuale
    const iv = await Crypto.getRandomBytesAsync(IV_LENGTH);

    // Converti il testo in bytes
    const plaintextBytes = new TextEncoder().encode(plaintext);

    // Implementazione semplificata: XOR
    const encryptedBytes = xorEncrypt(plaintextBytes, key);
    const encryptedHex = uint8ArrayToHex(encryptedBytes);

    const encryptedData: EncryptedData = {
      data: encryptedHex,
      iv: uint8ArrayToHex(iv),
      algorithm: 'XOR-Simple',
      version: '2.0'
    };

    return JSON.stringify(encryptedData);
  } catch (error) {
    console.error('❌ Errore durante la crittografia:', error);
    throw new Error(`Crittografia fallita: ${error.message}`);
  }
}

/**
 * Decripta i dati usando l'algoritmo corrispondente
 */
export async function decryptData(encryptedData: string): Promise<string> {
  try {
    if (!encryptedData || encryptedData.trim().length === 0) {
      throw new Error('Dati crittografati non forniti o vuoti');
    }

    const parsed: EncryptedData = JSON.parse(encryptedData);

    if (!parsed.data || !parsed.iv) {
      throw new Error('Formato dati crittografati non valido');
    }

    // Ottieni la chiave di crittografia
    const keyHex = await getOrCreateEncryptionKey();
    const key = hexToUint8Array(keyHex);

    // Converti i dati crittografati da hex a bytes
    const encryptedBytes = hexToUint8Array(parsed.data);

    // Decripta usando XOR (algoritmo simmetrico)
    const decryptedBytes = xorEncrypt(encryptedBytes, key);

    // Converti i bytes in stringa
    const decrypted = new TextDecoder().decode(decryptedBytes);

    return decrypted;
  } catch (error) {
    console.error('❌ Errore durante la decrittografia:', error);
    throw new Error(`Decrittografia fallita: ${error.message}`);
  }
}

/**
 * Cripta dati sensibili per il database
 */
export async function encryptSensitiveData(data: any): Promise<string> {
  try {
    const jsonString = JSON.stringify(data);
    return await encryptData(jsonString);
  } catch (error) {
    console.error('❌ Errore crittografia dati sensibili:', error);
    throw error;
  }
}

/**
 * Decripta dati sensibili dal database
 */
export async function decryptSensitiveData<T>(encryptedData: string): Promise<T> {
  try {
    const decrypted = await decryptData(encryptedData);
    return JSON.parse(decrypted);
  } catch (error) {
    console.error('❌ Errore decrittografia dati sensibili:', error);
    throw error;
  }
}

/**
 * Hash sicuro per password e token
 */
export async function hashPassword(password: string, salt?: string): Promise<{ hash: string; salt: string }> {
  try {
    const actualSalt = salt || uint8ArrayToHex(await Crypto.getRandomBytesAsync(16));
    const hash = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      password + actualSalt
    );

    return { hash, salt: actualSalt };
  } catch (error) {
    console.error('❌ Errore durante l\'hashing:', error);
    throw new Error('Hashing fallito');
  }
}

/**
 * Verifica password hashata
 */
export async function verifyPassword(password: string, hash: string, salt: string): Promise<boolean> {
  try {
    const { hash: computedHash } = await hashPassword(password, salt);
    return computedHash === hash;
  } catch (error) {
    console.error('❌ Errore durante la verifica password:', error);
    return false;
  }
}

/**
 * Genera token sicuro
 */
export async function generateSecureToken(length: number = 32): Promise<string> {
  try {
    const bytes = await Crypto.getRandomBytesAsync(length);
    return uint8ArrayToHex(bytes);
  } catch (error) {
    console.error('❌ Errore generazione token:', error);
    throw new Error('Generazione token fallita');
  }
}

/**
 * Pulisce la chiave di crittografia (per logout sicuro)
 */
export async function clearEncryptionKey(): Promise<void> {
  try {
    await SecureStore.deleteItemAsync(ENCRYPTION_KEY_ALIAS);
    await SecureStore.deleteItemAsync(MASTER_KEY_ALIAS);
    console.log('🔑 Chiavi di crittografia rimosse');
  } catch (error) {
    console.error('❌ Errore rimozione chiave:', error);
  }
}
