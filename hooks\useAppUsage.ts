import { useState, useEffect, useCallback } from 'react';
import { appUsageService, AppUsageData, AppUsageStats } from '../services/appUsageService';
import { EventFrequency } from '@brighthustle/react-native-usage-stats-manager';

export interface AppUsageState {
  hasPermission: boolean;
  isLoading: boolean;
  error: string | null;
  todayStats: AppUsageData[];
  weeklyStats: AppUsageStats[];
  topApps: AppUsageData[];
  totalScreenTime: number;
}

export const useAppUsage = () => {
  const [state, setState] = useState<AppUsageState>({
    hasPermission: false,
    isLoading: false,
    error: null,
    todayStats: [],
    weeklyStats: [],
    topApps: [],
    totalScreenTime: 0,
  });

  // Controlla il permesso
  const checkPermission = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const hasPermission = await appUsageService.checkPermission();
      setState(prev => ({ ...prev, hasPermission, isLoading: false }));
      return hasPermission;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage,
        hasPermission: false 
      }));
      return false;
    }
  }, []);

  // Richiede il permesso
  const requestPermission = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      await appUsageService.requestPermission();
      // Dopo aver aperto le impostazioni, ricontrolla il permesso
      setTimeout(() => {
        checkPermission();
      }, 1000);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to request permission';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
    }
  }, [checkPermission]);

  // Carica le statistiche di oggi
  const loadTodayStats = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const todayStats = await appUsageService.getTodayUsageStats();
      const totalScreenTime = todayStats.reduce((total, app) => total + app.totalTimeInForeground, 0);
      
      setState(prev => ({ 
        ...prev, 
        todayStats, 
        totalScreenTime,
        isLoading: false 
      }));
      return todayStats;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load today stats';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      return [];
    }
  }, []);

  // Carica le statistiche settimanali
  const loadWeeklyStats = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const weeklyStats = await appUsageService.getWeeklyUsageStats();
      
      setState(prev => ({ 
        ...prev, 
        weeklyStats,
        isLoading: false 
      }));
      return weeklyStats;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load weekly stats';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      return [];
    }
  }, []);

  // Carica le app più utilizzate
  const loadTopApps = useCallback(async (limit: number = 10) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const topApps = await appUsageService.getTopUsedApps(limit);
      
      setState(prev => ({ 
        ...prev, 
        topApps,
        isLoading: false 
      }));
      return topApps;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load top apps';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      return [];
    }
  }, []);

  // Carica statistiche personalizzate per un periodo
  const loadCustomStats = useCallback(async (
    startDate: Date, 
    endDate: Date, 
    frequency: EventFrequency = EventFrequency.INTERVAL_DAILY
  ) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const stats = await appUsageService.getUsageStats(startDate, endDate, frequency);
      setState(prev => ({ ...prev, isLoading: false }));
      return stats;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load custom stats';
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: errorMessage 
      }));
      return [];
    }
  }, []);

  // Carica tutte le statistiche
  const loadAllStats = useCallback(async () => {
    const hasPermission = await checkPermission();
    if (!hasPermission) {
      return;
    }

    await Promise.all([
      loadTodayStats(),
      loadTopApps(),
      loadWeeklyStats()
    ]);
  }, [checkPermission, loadTodayStats, loadTopApps, loadWeeklyStats]);

  // Refresh dei dati
  const refresh = useCallback(async () => {
    await loadAllStats();
  }, [loadAllStats]);

  // Formatta il tempo
  const formatTime = useCallback((milliseconds: number) => {
    return appUsageService.formatTime(milliseconds);
  }, []);

  // Controlla se un'app è di interesse
  const isAppOfConcern = useCallback((packageName: string) => {
    return appUsageService.isAppOfConcern(packageName);
  }, []);

  // Inizializza al mount
  useEffect(() => {
    checkPermission();
  }, [checkPermission]);

  return {
    // State
    ...state,
    
    // Actions
    checkPermission,
    requestPermission,
    loadTodayStats,
    loadWeeklyStats,
    loadTopApps,
    loadCustomStats,
    loadAllStats,
    refresh,
    
    // Utilities
    formatTime,
    isAppOfConcern,
  };
};
