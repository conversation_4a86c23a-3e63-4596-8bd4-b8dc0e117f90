import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useTranslations } from '../../contexts/TranslationContext';
import QuestionInput from './QuestionInput';
import ImageUploader from './ImageUploader';
import AIResponse from './AIResponse';
import { askHomeworkQuestion, askFollowUpQuestion } from '../../utils/anthropic';
import { supabase } from '../../utils/supabase';
import config from '../../config';

interface HomeworkSessionProps {
  childId: string;
}

interface Message {
  id: string;
  type: 'question' | 'answer';
  content: string;
  timestamp: Date;
}

const HomeworkSession: React.FC<HomeworkSessionProps> = ({ childId }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [showImageUploader, setShowImageUploader] = useState(false);
  const [authStatus, setAuthStatus] = useState<string>('checking');
  const [apiKeyError, setApiKeyError] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { t } = useTranslations();

  // Verifica l'API key all'avvio
  useEffect(() => {
    if (!config.anthropicApiKey) {
      console.error('Anthropic API key not found in configuration');
      setApiKeyError(true);
      setError('Chiave API mancante');
    } else if (config.anthropicApiKey === '') {
      console.error('Anthropic API key is empty');
      setApiKeyError(true);
      setError('Chiave API vuota');
    } else {
      console.log('Anthropic API key found, length:', config.anthropicApiKey.length);
      setApiKeyError(false);
      setError(null);
    }
  }, []);

  // Check authentication status on component mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          console.error('Auth error:', error);
          setAuthStatus('error');
          return;
        }

        if (data?.session) {
          console.log('User is authenticated with session:', data.session.user.id);
          setAuthStatus('authenticated');
        } else {
          console.log('No active session found');
          setAuthStatus('unauthenticated');
        }
      } catch (err) {
        console.error('Error checking auth:', err);
        setAuthStatus('error');
      }
    };

    checkAuth();
  }, []);

  const handleQuestionSubmit = async (question: string) => {
    // Verifica se l'API key è configurata
    if (!config.anthropicApiKey) {
      Alert.alert(
        'Configurazione mancante',
        'L\'assistente ai compiti non è configurato correttamente. Contatta l\'amministratore.',
        [{ text: 'OK' }]
      );
      setApiKeyError(true);
      return;
    }

    try {
      setIsLoading(true);

      // Get current session
      const { data: sessionData } = await supabase.auth.getSession();
      console.log('Session when submitting question:', sessionData?.session ? 'Active' : 'None');

      // Add question to messages
      const questionId = Date.now().toString();
      const questionMessage: Message = {
        id: questionId,
        type: 'question',
        content: question,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, questionMessage]);

      // Get previous questions and answers for context
      const previousQuestions = messages
        .filter((m) => m.type === 'question')
        .map((m) => m.content);
      const previousAnswers = messages
        .filter((m) => m.type === 'answer')
        .map((m) => m.content);

      let response;
      if (messages.length === 0) {
        // First question
        response = await askHomeworkQuestion(childId, question, imageUrl || undefined);
      } else {
        // Follow-up question
        response = await askFollowUpQuestion(
          childId,
          previousQuestions,
          previousAnswers,
          question,
          imageUrl || undefined
        );
      }

      // Add answer to messages
      const answerMessage: Message = {
        id: response.sessionId || Date.now().toString() + '-answer',
        type: 'answer',
        content: response.answer,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, answerMessage]);

      // Reset image URL after using it
      setImageUrl(null);
      setShowImageUploader(false);
    } catch (error: any) {
      console.error('Error submitting question:', error);
      Alert.alert(
        t.homework?.error || 'Errore',
        `${t.homework?.errorMessage || 'Non è stato possibile ottenere una risposta:'} ${error.message}`
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageUploaded = (url: string) => {
    setImageUrl(url);
    Alert.alert(
      'Immagine caricata',
      'L\'immagine è stata caricata con successo. Ora puoi fare una domanda sui tuoi compiti.',
      [{ text: 'OK' }]
    );
  };

  const handleImageUploadError = (error: string) => {
    Alert.alert('Errore', error, [{ text: 'OK' }]);
  };

  const toggleImageUploader = () => {
    setShowImageUploader(!showImageUploader);
  };

  const clearConversation = () => {
    Alert.alert(
      'Cancellare la conversazione?',
      'Sei sicuro di voler cancellare tutta la conversazione?',
      [
        { text: 'Annulla', style: 'cancel' },
        {
          text: 'Cancella',
          style: 'destructive',
          onPress: () => {
            setMessages([]);
            setImageUrl(null);
            setShowImageUploader(false);
          },
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{t.homework?.title || 'Assistente Compiti'}</Text>
        {messages.length > 0 && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={clearConversation}
          >
            <FontAwesome5 name="trash-alt" size={16} color="#FF3B30" />
            <Text style={styles.clearButtonText}>Cancella</Text>
          </TouchableOpacity>
        )}
      </View>

      {apiKeyError ? (
        <View style={styles.apiErrorContainer}>
          <FontAwesome5 name="exclamation-triangle" size={50} color="#FFC107" />
          <Text style={styles.apiErrorTitle}>
            Configurazione incompleta
          </Text>
          <Text style={styles.apiErrorText}>
            L'assistente ai compiti non è disponibile al momento perché manca la configurazione dell'API. 
            {error ? `\n\nDettagli: ${error}` : ''}
            {'\n\nContatta l\'amministratore dell\'app.'}
          </Text>
        </View>
      ) : (
        <>
          <ScrollView
            style={styles.messagesContainer}
            contentContainerStyle={styles.messagesContent}
            showsVerticalScrollIndicator={true}
            persistentScrollbar={true}
          >
            {messages.length === 0 ? (
              <View style={styles.emptyState}>
                <FontAwesome5 name="book" size={50} color="#CCCCCC" />
                <Text style={styles.emptyStateTitle}>
                  {t.homework?.emptyStateTitle || 'Assistente Compiti'}
                </Text>
                <Text style={styles.emptyStateText}>
                  {t.homework?.emptyStateText || 'Fai una domanda sui tuoi compiti o carica una foto per ricevere aiuto.'}
                </Text>
              </View>
            ) : (
              messages.map((message, index) => {
                if (index % 2 === 0 && index + 1 < messages.length) {
                  // Question and answer pair
                  const question = message;
                  const answer = messages[index + 1];
                  return (
                    <AIResponse
                      key={question.id}
                      question={question.content}
                      answer={answer.content}
                      timestamp={question.timestamp}
                      isLoading={index === messages.length - 1 && isLoading}
                    />
                  );
                } else if (index === messages.length - 1 && index % 2 === 0) {
                  // Last question without answer yet
                  return (
                    <AIResponse
                      key={message.id}
                      question={message.content}
                      answer=""
                      timestamp={message.timestamp}
                      isLoading={true}
                    />
                  );
                }
                return null;
              })
            )}
          </ScrollView>

          {showImageUploader && (
            <ImageUploader
              onImageUploaded={handleImageUploaded}
              onError={handleImageUploadError}
            />
          )}

          <View style={styles.inputContainer}>
            <TouchableOpacity
              style={styles.imageButton}
              onPress={toggleImageUploader}
            >
              <FontAwesome5
                name={showImageUploader ? 'times' : 'camera'}
                size={20}
                color="#4630EB"
              />
            </TouchableOpacity>

            <QuestionInput
              onSubmit={handleQuestionSubmit}
              isLoading={isLoading}
              placeholder={
                imageUrl
                  ? t.homework?.imagePlaceholder || 'Fai una domanda sull\'immagine...'
                  : t.homework?.placeholder || 'Fai una domanda sui compiti...'
              }
            />
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 6,
  },
  clearButtonText: {
    marginLeft: 5,
    color: '#FF3B30',
    fontSize: 14,
  },
  messagesContainer: {
    flex: 1,
    padding: 16,
  },
  messagesContent: {
    paddingBottom: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
    paddingHorizontal: 20,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  imageButton: {
    padding: 10,
    marginRight: 8,
  },
  apiErrorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFF9E6',
    borderRadius: 10,
    margin: 20,
  },
  apiErrorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFC107',
    marginTop: 15,
    marginBottom: 10,
    textAlign: 'center',
  },
  apiErrorText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default HomeworkSession;
