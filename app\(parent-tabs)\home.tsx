import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { Marker, Circle } from 'react-native-maps';
import GoogleMapView from '../../components/shared/GoogleMapView';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import {
  getChildrenForParent,
  getChildLocation,
  getSafeZones,
  createSafeZone,
  assignSafeZoneToChild,
  deleteSafeZone,
} from '../../utils/supabase';
import * as Notifications from 'expo-notifications';
import Header from '../../components/shared/Header';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import { useTranslations } from '../../contexts/TranslationContext';

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

// Function to send zone notification
async function sendZoneNotification(childName: string, zoneName: string, action: 'entered' | 'exited') {
  try {
    const title = `${childName} ${action} ${zoneName}`;
    const body = action === 'entered'
      ? `${childName} has arrived at ${zoneName}`
      : `${childName} has left ${zoneName}`;

    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: {
          type: 'zone',
          childName,
          zoneName,
          action,
          timestamp: new Date().toISOString(),
        },
      },
      trigger: null, // null means show immediately
    });

    console.log(`Notification sent: ${childName} ${action} ${zoneName}`);
  } catch (error) {
    console.error('Error sending notification:', error);
  }
}

interface Child {
  child_id: string;
  users: {
    name: string;
    age?: number;
  };
  location?: {
    latitude: number;
    longitude: number;
    timestamp: string;
    accuracy?: number;
    battery_level?: number;
  };
}

interface SafeZone {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  radius: number;
  children?: any[];
}

// SOSAlert interface removed

function ParentHomeScreen() {
  const { user } = useAuth();
  const router = useRouter();
  const { t } = useTranslations();
  const [children, setChildren] = useState<Child[]>([]);
  const [safeZones, setSafeZones] = useState<SafeZone[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedFeature, setSelectedFeature] = useState<string>('children');
  const mapRef = useRef<any>(null);
  const region = {
    latitude: 41.9028, // Default to Rome, Italy
    longitude: 12.4964,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  };

  // States for safe zone creation
  const [addingSafeZone, setAddingSafeZone] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<{latitude: number, longitude: number} | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedChildren, setSelectedChildren] = useState<Child[]>([]);
  const [childSelectVisible, setChildSelectVisible] = useState(false);
  const [newSafeZone, setNewSafeZone] = useState({
    name: '',
    radius: '100',
    address: '',
  });

  // Load data
  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user]);

  const fetchData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Get children
      const childrenData = await getChildrenForParent(user.id);
      console.log('Children data:', childrenData);

      // Get location for each child
      const childrenWithLocation = await Promise.all(
        childrenData.map(async (child: any) => {
          try {
            const location = await getChildLocation(child.child_id);
            return {
              ...child,
              location: location || undefined,
            };
          } catch (error) {
            console.error(`Error getting location for child ${child.child_id}:`, error);
            return child;
          }
        })
      );

      setChildren(childrenWithLocation);

      // Get safe zones
      const zonesData = await getSafeZones(user.id);
      setSafeZones(zonesData);

      // SOS alerts removed

      // Fit map to show all children and safe zones
      setTimeout(() => {
        fitMapToAll(childrenWithLocation, zonesData);
      }, 500);
    } catch (error) {
      console.error('Error fetching data:', error);
      Alert.alert('Error', 'Failed to load data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Fit map to show all children and safe zones
  const fitMapToAll = (childrenData: Child[], zonesData: SafeZone[]) => {
    if (!mapRef.current) return;

    interface Coordinate {
      latitude: number;
      longitude: number;
    }

    const validLocations: Coordinate[] = [];

    // Add children locations
    childrenData.forEach((child) => {
      if (child.location) {
        validLocations.push({
          latitude: child.location.latitude,
          longitude: child.location.longitude,
        });
      }
    });

    // Add safe zone locations
    zonesData.forEach((zone) => {
      validLocations.push({
        latitude: zone.latitude,
        longitude: zone.longitude,
      });
    });

    // SOS alert locations removed

    if (validLocations.length === 0) return;

    mapRef.current.fitToCoordinates(validLocations, {
      edgePadding: { top: 100, right: 50, bottom: 150, left: 50 },
      animated: true,
    });
  };

  // Navigate to feature screens function removed

  // Toggle map features
  const toggleFeature = (feature: string) => {
    setSelectedFeature(feature);
    // Reset safe zone creation mode when switching features
    if (addingSafeZone) {
      setAddingSafeZone(false);
      setSelectedLocation(null);
    }
  };

  // Handle map press for safe zone creation
  const handleMapPress = (event: any) => {
    if (!addingSafeZone) return;

    try {
      console.log('Map pressed');
      // Get the coordinate from the event
      const { coordinate } = event.nativeEvent;
      console.log('Coordinate:', coordinate);

      // Set the selected location
      setSelectedLocation(coordinate);

      // Get address from coordinates (reverse geocoding)
      // In a real app, you would use a geocoding service
      setNewSafeZone({
        ...newSafeZone,
        address: 'Address will be shown here'
      });

      // Provide feedback to the user
      Alert.alert(
        'Location Selected',
        'You have selected a location for your safe zone. Now tap the "Add Safe Zone" button to continue.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error handling map press:', error);
      Alert.alert('Error', 'Failed to select location. Please try again.');
    }
  };

  // Toggle safe zone creation mode
  const toggleSafeZoneCreation = () => {
    const newMode = !addingSafeZone;
    setAddingSafeZone(newMode);

    if (newMode) {
      // Switch to safe zones view when entering creation mode
      setSelectedFeature('safe-zones');
      Alert.alert(
        'Add Safe Zone',
        'Tap on the map to select a location for your safe zone.',
        [{ text: 'OK' }]
      );
    } else {
      // Clear selected location when exiting creation mode
      setSelectedLocation(null);
    }
  };

  // Open the safe zone creation modal
  const openSafeZoneModal = () => {
    if (!selectedLocation) {
      Alert.alert('No Location Selected', 'Please tap on the map to select a location first.');
      return;
    }

    // Reset form
    setNewSafeZone({
      name: '',
      radius: '100',
      address: '',
    });
    setSelectedChildren([]);
    setModalVisible(true);
  };

  // Handle child selection for safe zone
  const toggleChildSelection = (child: Child) => {
    if (selectedChildren.some(c => c.child_id === child.child_id)) {
      setSelectedChildren(selectedChildren.filter(c => c.child_id !== child.child_id));
    } else {
      setSelectedChildren([...selectedChildren, child]);
    }
  };

  // Create a new safe zone
  const handleCreateSafeZone = async () => {
    // Validation
    if (!selectedLocation) {
      Alert.alert('Error', 'Please select a location on the map');
      return;
    }

    if (!newSafeZone.name.trim()) {
      Alert.alert('Error', 'Please enter a name for the safe zone');
      return;
    }

    try {
      setIsLoading(true);

      // Create the safe zone in the database
      const zoneData = {
        name: newSafeZone.name,
        latitude: selectedLocation.latitude,
        longitude: selectedLocation.longitude,
        radius: parseInt(newSafeZone.radius) || 100,
        address: newSafeZone.address || 'Address not available'
      };

      // Create the safe zone
      const createdZone = await createSafeZone(user.id, zoneData);

      // Assign children to the safe zone
      for (const child of selectedChildren) {
        await assignSafeZoneToChild(createdZone.id, child.child_id);
      }

      // Add the new zone to the state
      const newZone: SafeZone = {
        ...createdZone,
        children: selectedChildren
      };

      setSafeZones(prevZones => [...prevZones, newZone]);

      // Send notification for each selected child
      for (const child of selectedChildren) {
        await sendZoneNotification(child.users.name, newSafeZone.name, 'entered');
      }

      // Reset form and close modal
      setNewSafeZone({
        name: '',
        radius: '100',
        address: '',
      });
      setSelectedLocation(null);
      setSelectedChildren([]);
      setModalVisible(false);
      setAddingSafeZone(false);

      Alert.alert('Success', 'Safe zone created successfully!');
    } catch (err) {
      console.error('Error creating safe zone:', err);
      Alert.alert('Error', 'Failed to create safe zone. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Delete a safe zone
  const handleDeleteSafeZone = (zone: SafeZone) => {
    Alert.alert(
      'Delete Safe Zone',
      `Are you sure you want to delete the "${zone.name}" safe zone? This action cannot be undone.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              await deleteSafeZone(zone.id);
              setSafeZones(prevZones => prevZones.filter(z => z.id !== zone.id));
              Alert.alert('Success', 'Safe zone deleted successfully');
            } catch (error) {
              console.error('Error deleting safe zone:', error);
              Alert.alert('Error', 'Failed to delete safe zone. Please try again.');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  if (isLoading) {
    return <LoadingIndicator fullScreen text="Loading map..." />;
  }

  return (
    <View style={styles.container}>
      <Header title="KidSafety" />

      {/* Map View */}
      <View style={styles.mapContainer}>
        <GoogleMapView
          ref={mapRef}
          style={styles.map}
          initialRegion={region}
          onPress={handleMapPress}
        >
          {/* Children Markers */}
          {selectedFeature === 'children' &&
            children.map((child) => (
              child.location ? (
                <React.Fragment key={child.child_id}>
                  <Marker
                    coordinate={{
                      latitude: child.location.latitude,
                      longitude: child.location.longitude,
                    }}
                    title={child.users?.name || 'Child'}
                    description={`Last updated: ${new Date(child.location.timestamp).toLocaleTimeString()}`}
                  >
                    <View style={styles.childMarker}>
                      <FontAwesome5 name="child" size={20} color="#FFFFFF" />
                    </View>
                  </Marker>

                  <Circle
                    center={{
                      latitude: child.location.latitude,
                      longitude: child.location.longitude,
                    }}
                    radius={child.location.accuracy || 100}
                    fillColor="rgba(70, 48, 235, 0.1)"
                    strokeColor="rgba(70, 48, 235, 0.5)"
                  />
                </React.Fragment>
              ) : null
            ))}

          {/* Safe Zones */}
          {selectedFeature === 'safe-zones' &&
            safeZones.map((zone) => (
              <React.Fragment key={zone.id}>
                <Circle
                  center={{
                    latitude: zone.latitude,
                    longitude: zone.longitude,
                  }}
                  radius={zone.radius}
                  fillColor="rgba(46, 204, 113, 0.2)"
                  strokeColor="rgba(46, 204, 113, 0.7)"
                  strokeWidth={2}
                />
                <Marker
                  coordinate={{
                    latitude: zone.latitude,
                    longitude: zone.longitude,
                  }}
                  title={zone.name}
                  description={`${zone.children?.length || 0} children assigned`}
                  onPress={() => {
                    if (addingSafeZone) return; // Disable marker interaction in creation mode
                    Alert.alert(
                      zone.name,
                      `Radius: ${zone.radius}m\nChildren: ${zone.children?.length || 0}`,
                      [
                        { text: 'OK' },
                        {
                          text: 'Delete',
                          style: 'destructive',
                          onPress: () => handleDeleteSafeZone(zone)
                        }
                      ]
                    );
                  }}
                >
                  <View style={styles.safeZoneMarker}>
                    <FontAwesome5 name="shield-alt" size={24} color="#FFFFFF" />
                  </View>
                </Marker>
              </React.Fragment>
            ))}

          {/* Selected Location for new Safe Zone */}
          {addingSafeZone && selectedLocation && (
            <React.Fragment>
              <Circle
                center={selectedLocation}
                radius={parseInt(newSafeZone.radius) || 100}
                fillColor="rgba(70, 48, 235, 0.1)"
                strokeColor="rgba(70, 48, 235, 0.5)"
                strokeWidth={2}
              />
              <Marker
                coordinate={selectedLocation}
                title="New Safe Zone"
                description="Tap the Add Safe Zone button to continue"
              >
                <View style={[styles.safeZoneMarker, { backgroundColor: '#4630EB' }]}>
                  <FontAwesome5 name="shield-alt" size={24} color="#FFFFFF" />
                </View>
              </Marker>
            </React.Fragment>
          )}

          {/* SOS Alerts removed */}
        </GoogleMapView>

        {/* Map Controls */}
        <View style={styles.mapControls}>
          <TouchableOpacity
            style={styles.mapButton}
            onPress={fetchData}
          >
            <FontAwesome5 name="sync" size={16} color="#4630EB" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.mapButton}
            onPress={() => fitMapToAll(children, safeZones)}
          >
            <FontAwesome5 name="compress-arrows-alt" size={16} color="#4630EB" />
          </TouchableOpacity>
        </View>

        {/* Feature Toggle Buttons */}
        <View style={styles.featureToggle}>
          <TouchableOpacity
            style={[
              styles.featureButton,
              selectedFeature === 'children' && styles.featureButtonActive,
            ]}
            onPress={() => toggleFeature('children')}
          >
            <FontAwesome5
              name="child"
              size={16}
              color={selectedFeature === 'children' ? '#FFFFFF' : '#4630EB'}
            />
            <Text
              style={[
                styles.featureButtonText,
                selectedFeature === 'children' && styles.featureButtonTextActive,
              ]}
            >
              {t.dashboard?.children || 'Children'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.featureButton,
              selectedFeature === 'safe-zones' && styles.featureButtonActive,
            ]}
            onPress={() => toggleFeature('safe-zones')}
          >
            <FontAwesome5
              name="shield-alt"
              size={16}
              color={selectedFeature === 'safe-zones' ? '#FFFFFF' : '#4630EB'}
            />
            <Text
              style={[
                styles.featureButtonText,
                selectedFeature === 'safe-zones' && styles.featureButtonTextActive,
              ]}
            >
              {t.dashboard?.safeZones || 'Safe Zones'}
            </Text>
          </TouchableOpacity>

          {/* SOS Alerts button removed */}
        </View>
      </View>

      {/* Floating Action Buttons */}
      <View style={styles.fabContainer}>
        <TouchableOpacity
          style={[styles.fab, { backgroundColor: '#4630EB' }]}
          onPress={() => router.push('/children')}
        >
          <FontAwesome5 name="user-plus" size={20} color="#FFFFFF" />
        </TouchableOpacity>

        {addingSafeZone && selectedLocation ? (
          <TouchableOpacity
            style={[styles.fab, { backgroundColor: '#4CAF50', marginTop: 16 }]}
            onPress={openSafeZoneModal}
          >
            <FontAwesome5 name="check" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.fab, { backgroundColor: addingSafeZone ? '#FF5722' : '#2ECC71', marginTop: 16 }]}
            onPress={toggleSafeZoneCreation}
          >
            <FontAwesome5 name={addingSafeZone ? "times" : "shield-alt"} size={20} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </View>

      {/* Safe Zone Creation Instructions */}
      {addingSafeZone && (
        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsText}>
            {t.safeZone?.tapToSelectLocation || 'Tap on the map to select a location for your safe zone'}
          </Text>
        </View>
      )}

      {/* Create Safe Zone Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t.safeZone?.createSafeZone || 'Create Safe Zone'}</Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <FontAwesome5 name="times" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              <Text style={styles.inputLabel}>{t.safeZone?.zoneName || 'Zone Name'}</Text>
              <TextInput
                style={styles.input}
                value={newSafeZone.name}
                onChangeText={(text) => setNewSafeZone({ ...newSafeZone, name: text })}
                placeholder={t.safeZone?.enterZoneName || "Enter zone name (e.g. Home, School)"}
              />

              <Text style={styles.inputLabel}>{t.safeZone?.radiusMeters || 'Radius (meters)'}</Text>
              <TextInput
                style={styles.input}
                value={newSafeZone.radius}
                onChangeText={(text) => setNewSafeZone({ ...newSafeZone, radius: text.replace(/[^0-9]/g, '') })}
                placeholder={t.safeZone?.enterRadius || "Enter radius in meters"}
                keyboardType="numeric"
              />

              {selectedLocation && (
                <View style={styles.locationInfo}>
                  <Text style={styles.locationTitle}>{t.safeZone?.selectedLocation || 'Selected Location'}</Text>
                  <Text style={styles.locationText}>
                    {t.safeZone?.latitude || 'Latitude'}: {selectedLocation.latitude.toFixed(6)}
                  </Text>
                  <Text style={styles.locationText}>
                    {t.safeZone?.longitude || 'Longitude'}: {selectedLocation.longitude.toFixed(6)}
                  </Text>
                  <Text style={styles.locationText}>
                    {t.safeZone?.address || 'Address'}: {newSafeZone.address || (t.common?.notAvailable || 'Not available')}
                  </Text>
                </View>
              )}

              <Text style={styles.inputLabel}>{t.safeZone?.assignChildren || 'Assign Children'}</Text>
              <TouchableOpacity
                style={styles.childPickerButton}
                onPress={() => setChildSelectVisible(true)}
              >
                <Text style={styles.childPickerText}>
                  {selectedChildren.length > 0
                    ? `${selectedChildren.length} ${t.safeZone?.childrenSelected || 'children selected'}`
                    : t.safeZone?.selectChildren || 'Select children'}
                </Text>
                <FontAwesome5 name="chevron-down" size={16} color="#4630EB" />
              </TouchableOpacity>

              {selectedChildren.length > 0 && (
                <View style={styles.selectedChildrenContainer}>
                  {selectedChildren.map((child) => (
                    <View key={child.child_id} style={styles.selectedChildChip}>
                      <Text style={styles.selectedChildName}>{child.users.name}</Text>
                      <TouchableOpacity
                        onPress={() => toggleChildSelection(child)}
                        style={styles.removeChildButton}
                      >
                        <FontAwesome5 name="times" size={12} color="#fff" />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>{t.common?.cancel || 'Cancel'}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.createButton, (!selectedLocation || isLoading) && styles.disabledButton]}
                disabled={!selectedLocation || isLoading}
                onPress={handleCreateSafeZone}
              >
                {isLoading ? (
                  <View style={styles.buttonContentWithLoader}>
                    <ActivityIndicator size="small" color="#fff" />
                    <Text style={styles.createButtonText}>{t.safeZone?.creating || 'Creating...'}</Text>
                  </View>
                ) : (
                  <Text style={styles.createButtonText}>{t.safeZone?.createZone || 'Create Zone'}</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Child Selection Modal */}
      <Modal
        visible={childSelectVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setChildSelectVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, styles.childSelectModal]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t.safeZone?.selectChildren || 'Select Children'}</Text>
              <TouchableOpacity onPress={() => setChildSelectVisible(false)}>
                <FontAwesome5 name="times" size={20} color="#666" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              {children.length === 0 ? (
                <Text style={styles.noChildrenText}>{t.dashboard?.noChildrenAdded || 'No children added yet'}</Text>
              ) : (
                children.map((child) => (
                  <TouchableOpacity
                    key={child.child_id}
                    style={styles.childOption}
                    onPress={() => toggleChildSelection(child)}
                  >
                    <View style={styles.childIconContainer}>
                      <FontAwesome5 name="child" size={20} color="#4630EB" />
                    </View>
                    <Text style={styles.childOptionText}>{child.users.name}</Text>
                    {selectedChildren.some(c => c.child_id === child.child_id) && (
                      <FontAwesome5 name="check" size={16} color="#4630EB" />
                    )}
                  </TouchableOpacity>
                ))
              )}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setChildSelectVisible(false)}
              >
                <Text style={styles.cancelButtonText}>{t.common?.cancel || 'Cancel'}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.createButton]}
                onPress={() => setChildSelectVisible(false)}
              >
                <Text style={styles.createButtonText}>{t.safeZone?.confirmSelection || 'Confirm Selection'}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  mapControls: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'column',
  },
  mapButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  featureToggle: {
    position: 'absolute',
    top: 16,
    left: 16,
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  featureButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginVertical: 4,
  },
  featureButtonActive: {
    backgroundColor: '#4630EB',
  },
  featureButtonText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#4630EB',
    fontWeight: '500',
  },
  featureButtonTextActive: {
    color: '#FFFFFF',
  },
  childMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4630EB',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  safeZoneMarker: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#2ECC71',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  // SOS marker style removed
  fabContainer: {
    position: 'absolute',
    right: 16,
    bottom: 30,
    zIndex: 10,
  },
  fab: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  // Instructions container
  instructionsContainer: {
    position: 'absolute',
    top: 80,
    left: 0,
    right: 0,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  instructionsText: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    color: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    fontSize: 14,
    fontWeight: 'bold',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  childSelectModal: {
    maxHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalContent: {
    padding: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
  },
  locationInfo: {
    backgroundColor: '#f0f0ff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  locationTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  childPickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  childPickerText: {
    fontSize: 16,
    color: '#333',
  },
  selectedChildrenContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  selectedChildChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4630EB',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedChildName: {
    fontSize: 14,
    color: '#fff',
    marginRight: 8,
  },
  removeChildButton: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#666',
    fontWeight: 'bold',
  },
  createButton: {
    backgroundColor: '#4630EB',
    marginLeft: 8,
  },
  createButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: '#B0BEC5',
    opacity: 0.7,
  },
  buttonContentWithLoader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  childOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  childIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  childOptionText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  noChildrenText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  }
});

export default ParentHomeScreen;
