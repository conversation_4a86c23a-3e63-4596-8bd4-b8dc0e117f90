import React, { useState, useEffect } from 'react';
import { View, ActivityIndicator, StyleSheet, Text, ViewStyle, TouchableOpacity } from 'react-native';

interface LoadingIndicatorProps {
  size?: 'small' | 'large';
  color?: string;
  text?: string;
  fullScreen?: boolean;
  style?: ViewStyle;
  timeout?: number; // Timeout in millisecondi dopo il quale mostrare il pulsante di annullamento
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  size = 'large',
  color = '#4630EB',
  text,
  fullScreen = false,
  style,
  timeout = 8000, // Default timeout di 8 secondi
}) => {
  const [showCancelButton, setShowCancelButton] = useState(false);
  const [visible, setVisible] = useState(true);

  // Aggiungiamo un timeout per mostrare il pulsante di annullamento dopo un certo periodo
  useEffect(() => {
    if (fullScreen) {
      const timer = setTimeout(() => {
        console.log('[LoadingIndicator] Timeout reached, showing cancel button');
        setShowCancelButton(true);
      }, timeout);

      return () => clearTimeout(timer);
    }
  }, [fullScreen, timeout]);

  // Se l'indicatore non è visibile, non mostriamo nulla
  if (!visible) {
    return null;
  }

  if (fullScreen) {
    return (
      <View style={[styles.fullScreenContainer, style]}>
        <ActivityIndicator size={size} color={color} />
        {text && <Text style={styles.text}>{text}</Text>}

        {showCancelButton && (
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => {
              console.log('[LoadingIndicator] Cancel button pressed, hiding loading indicator');
              setVisible(false);
            }}
          >
            <Text style={styles.cancelButtonText}>Annulla caricamento</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator size={size} color={color} />
      {text && <Text style={styles.text}>{text}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullScreenContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
  },
  text: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  cancelButton: {
    marginTop: 30,
    backgroundColor: '#f44336',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default LoadingIndicator;