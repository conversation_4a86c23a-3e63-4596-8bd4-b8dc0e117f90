#!/usr/bin/env node

/**
 * Test script to verify billing configuration
 * Run with: node scripts/test-billing.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Google Play Billing Configuration...\n');

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env file not found!');
  process.exit(1);
}

// Read .env file
const envContent = fs.readFileSync(envPath, 'utf8');
const envLines = envContent.split('\n');

// Parse environment variables
const envVars = {};
envLines.forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim();
  }
});

console.log('📋 Checking required environment variables:\n');

// Check required variables for native billing
const requiredVars = [
  'EXPO_PUBLIC_PREMIUM_MONTHLY_SKU',
  'EXPO_PUBLIC_PREMIUM_YEARLY_SKU',
  'EXPO_PUBLIC_GOOGLE_PLAY_PUBLIC_KEY',
  'EXPO_PUBLIC_GOOGLE_PLAY_PACKAGE_NAME'
];

let allValid = true;

requiredVars.forEach(varName => {
  const value = envVars[varName];
  if (!value || value === 'your_key_here' || value.includes('dummy')) {
    console.log(`❌ ${varName}: Missing or invalid`);
    allValid = false;
  } else {
    console.log(`✅ ${varName}: Configured`);
  }
});

console.log('\n📱 Checking product IDs:\n');

// Check product IDs format
const monthlySkuValid = envVars.EXPO_PUBLIC_PREMIUM_MONTHLY_SKU === 'com.evotech.kidsafety.monthly';
const yearlySkuValid = envVars.EXPO_PUBLIC_PREMIUM_YEARLY_SKU === 'com.evotech.kidsafety.annual';

console.log(`${monthlySkuValid ? '✅' : '❌'} Monthly SKU: ${envVars.EXPO_PUBLIC_PREMIUM_MONTHLY_SKU}`);
console.log(`${yearlySkuValid ? '✅' : '❌'} Yearly SKU: ${envVars.EXPO_PUBLIC_PREMIUM_YEARLY_SKU}`);

console.log('\n🔑 Checking Google Play configuration:\n');

// Check Google Play package name
const packageNameValid = envVars.EXPO_PUBLIC_GOOGLE_PLAY_PACKAGE_NAME === 'com.evotech.kidsafety';
console.log(`${packageNameValid ? '✅' : '❌'} Package Name: ${envVars.EXPO_PUBLIC_GOOGLE_PLAY_PACKAGE_NAME}`);

// Check Google Play public key
const publicKeyValid = envVars.EXPO_PUBLIC_GOOGLE_PLAY_PUBLIC_KEY &&
                      envVars.EXPO_PUBLIC_GOOGLE_PLAY_PUBLIC_KEY.length > 100;
console.log(`${publicKeyValid ? '✅' : '❌'} Google Play Public Key: ${publicKeyValid ? 'Configured' : 'Missing or invalid'}`);

console.log('\n📦 Checking package.json dependencies:\n');

// Check package.json
const packagePath = path.join(process.cwd(), 'package.json');
if (fs.existsSync(packagePath)) {
  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const deps = { ...packageContent.dependencies, ...packageContent.devDependencies };
  
  const hasReactNativeIap = deps['react-native-iap'];
  const hasReactNativePurchases = deps['react-native-purchases'];

  console.log(`${hasReactNativeIap ? '✅' : '❌'} react-native-iap: ${hasReactNativeIap || 'Not installed'}`);
  if (hasReactNativePurchases) {
    console.log(`⚠️  react-native-purchases: ${hasReactNativePurchases} (should be removed)`);
  }
} else {
  console.log('❌ package.json not found');
  allValid = false;
}

console.log('\n🏗️ Checking app.config.js:\n');

// Check app.config.js for billing permission and plugin
const configPath = path.join(process.cwd(), 'app.config.js');
if (fs.existsSync(configPath)) {
  const configContent = fs.readFileSync(configPath, 'utf8');
  const hasBillingPermission = configContent.includes('com.android.vending.BILLING');
  const hasReactNativeIapPlugin = configContent.includes('react-native-iap');

  console.log(`${hasBillingPermission ? '✅' : '❌'} Billing permission: ${hasBillingPermission ? 'Configured' : 'Missing'}`);
  console.log(`${hasReactNativeIapPlugin ? '✅' : '❌'} react-native-iap plugin: ${hasReactNativeIapPlugin ? 'Configured' : 'Missing'}`);

  if (!hasBillingPermission || !hasReactNativeIapPlugin) {
    allValid = false;
  }
} else {
  console.log('❌ app.config.js not found');
  allValid = false;
}

console.log('\n' + '='.repeat(50));

if (allValid && packageNameValid && publicKeyValid) {
  console.log('🎉 All native billing configuration checks passed!');
  console.log('\n📋 Next steps:');
  console.log('1. Build development APK: eas build --platform android --profile development');
  console.log('2. Install on real Android device');
  console.log('3. Test billing initialization in app');
  console.log('4. Configure products in Google Play Console');
  console.log('5. Test purchases with Google Play test accounts');
} else {
  console.log('⚠️  Some configuration issues found!');
  console.log('\n🔧 To fix:');
  console.log('1. Ensure all required environment variables are set');
  console.log('2. Install react-native-iap: npm install react-native-iap');
  console.log('3. Remove react-native-purchases if present');
  console.log('4. Configure products in Google Play Console');
}

console.log('\n📚 Documentation:');
console.log('- Setup Guide: REVENUECAT_SETUP_GUIDE.md');
console.log('- Fix Guide: GOOGLE_PLAY_BILLING_FIX.md');
