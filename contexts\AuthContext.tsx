import React, { createContext, useState, useEffect, useContext } from 'react';
import { supabase, signInWithEmail, signUpWithEmail, signInWithToken, signOut, getCurrentUser } from '../utils/supabase';
import * as SecureStore from 'expo-secure-store';
import { nativeBillingService } from '../services/nativeBillingService';
import { SUBSCRIPTION_SKUS } from '../config/billing';

// Define types for our context
type SubscriptionStatus = {
  hasSubscription: boolean;
  isActive: boolean;
  status: 'none' | 'trial' | 'active' | 'expired' | 'cancelled' | 'error';
  daysRemaining: number;
  subscription?: any;
};

type AuthContextType = {
  user: any | null;
  userType: 'parent' | 'child' | null;
  isLoading: boolean;
  error: string | null;
  subscriptionStatus: SubscriptionStatus | null;
  isSubscriptionLoading: boolean;
  signUp: (email: string, password: string, name: string) => Promise<boolean>;
  signIn: (email: string, password: string) => Promise<void>;
  signInChild: (token: string) => Promise<void>;
  logout: () => Promise<boolean>;
  clearError: () => void;
  refreshUser: () => Promise<void>;
  startFreeTrial: () => Promise<boolean>;
  refreshSubscriptionStatus: () => Promise<void>;
};

// Create context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  userType: null,
  isLoading: true,
  error: null,
  subscriptionStatus: null,
  isSubscriptionLoading: false,
  signUp: async () => false,
  signIn: async () => {},
  signInChild: async () => {},
  logout: async () => false,
  clearError: () => {},
  refreshUser: async () => {},
  startFreeTrial: async () => false,
  refreshSubscriptionStatus: async () => {},
});

// Storage keys
const USER_TYPE_KEY = 'user_type';
const CHILD_USER_KEY = 'child_user';

// Provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<any | null>(null);
  const [userType, setUserType] = useState<'parent' | 'child' | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [isSubscriptionLoading, setIsSubscriptionLoading] = useState(false);

  // Refresh user data
  const refreshUser = async () => {
    try {
      setIsLoading(true);
      const currentUser = await getCurrentUser();

      if (currentUser) {
        setUser(currentUser);
        const type = await SecureStore.getItemAsync(USER_TYPE_KEY);
        setUserType(type as 'parent' | 'child' | null);
      } else {
        setUser(null);
        setUserType(null);
      }
    } catch (err: any) {
      console.error('Error refreshing user:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('🔄 AuthContext: Starting initialization...');

        // Timeout ridotto per evitare blocchi in produzione
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Auth initialization timeout')), 5000);
        });

        const initPromise = (async () => {
          try {
            const storedChildUser = await SecureStore.getItemAsync(CHILD_USER_KEY);
            const storedUserType = await SecureStore.getItemAsync(USER_TYPE_KEY);
            return { storedChildUser, storedUserType };
          } catch (storageError) {
            console.warn('⚠️ AuthContext: SecureStore error, continuing with defaults:', storageError);
            return { storedChildUser: null, storedUserType: null };
          }
        })();

        const { storedChildUser, storedUserType } = await Promise.race([initPromise, timeoutPromise]) as any;

        if (storedChildUser && storedUserType === 'child') {
          // Use the essential child data stored in SecureStore
          const childData = JSON.parse(storedChildUser);
          console.log('Retrieved child data from SecureStore:', childData);

          // Use the actual child ID from SecureStore
          console.log('Using actual child ID from SecureStore:', childData.id);

          setUser(childData);
          setUserType('child');
        } else {
          try {
            // Timeout più breve per getCurrentUser
            const userPromise = getCurrentUser();
            const userTimeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('getCurrentUser timeout')), 3000);
            });

            const currentUser = await Promise.race([userPromise, userTimeoutPromise]) as any;

            if (currentUser) {
              setUser(currentUser);
              setUserType('parent');
              await SecureStore.setItemAsync(USER_TYPE_KEY, 'parent');
            } else {
              await SecureStore.deleteItemAsync(USER_TYPE_KEY);
              await SecureStore.deleteItemAsync(CHILD_USER_KEY);
              setUser(null);
              setUserType(null);
            }
          } catch (userError) {
            console.warn('⚠️ AuthContext: getCurrentUser error, continuing as guest:', userError);
            setUser(null);
            setUserType(null);
          }
        }
      } catch (err: any) {
        console.error('❌ AuthContext: Error during initialization:', err);
        // Non impostare l'errore per evitare di bloccare l'app in produzione
        // setError(err.message);
        setUser(null);
        setUserType(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();

    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        const userData = await getCurrentUser();
        setUser(userData);
        setUserType('parent');
        await SecureStore.setItemAsync(USER_TYPE_KEY, 'parent');
      } else if (event === 'SIGNED_OUT') {
        await SecureStore.deleteItemAsync(USER_TYPE_KEY);
        await SecureStore.deleteItemAsync(CHILD_USER_KEY);
        setUser(null);
        setUserType(null);
      }
    });

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, []);

  // Sign up with email
  const signUp = async (email: string, password: string, name: string) => {
    try {
      console.log('AuthContext: Starting signUp for:', email);
      setIsLoading(true);
      setError(null);

      const result = await signUpWithEmail(email, password, name);
      console.log('AuthContext: signUpWithEmail result:', result ? 'success' : 'failed');

      if (result?.user) {
        console.log('AuthContext: New user created, getting user data');
        const userData = await getCurrentUser();
        console.log('AuthContext: User data retrieved:', userData ? 'success' : 'failed');

        setUser(userData);
        setUserType('parent');

        try {
          await SecureStore.setItemAsync(USER_TYPE_KEY, 'parent');
          console.log('AuthContext: User type saved to SecureStore');
        } catch (storageErr) {
          console.error('AuthContext: Error saving to SecureStore:', storageErr);
        }

        return true; // Indicate successful signup
      } else {
        console.log('AuthContext: No user returned from signUpWithEmail');
        return false; // Indicate failed signup
      }
    } catch (err: any) {
      console.error('AuthContext: Sign up error:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign in with email
  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const { user: authUser } = await signInWithEmail(email, password);
      if (authUser) {
        const userData = await getCurrentUser();
        setUser(userData);
        setUserType('parent');
        await SecureStore.setItemAsync(USER_TYPE_KEY, 'parent');
      }
    } catch (err: any) {
      console.error('Sign in error:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign in with child token
  const signInChild = async (token: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const childUser = await signInWithToken(token);
      if (childUser) {
        console.log('Child user authenticated:', childUser);

        // Use the actual child ID from the authenticated user
        console.log('Using actual child ID:', childUser.id);

        setUser(childUser);
        setUserType('child');

        // Store only essential child user data to avoid SecureStore size limit
        const essentialChildData = {
          id: childUser.id,
          name: childUser.name,
          child_id: childUser.id
        };

        console.log('Storing child data in SecureStore:', essentialChildData);
        await SecureStore.setItemAsync(CHILD_USER_KEY, JSON.stringify(essentialChildData));
        await SecureStore.setItemAsync(USER_TYPE_KEY, 'child');
      }
    } catch (err: any) {
      console.error('Child sign in error:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign out
  const logout = async () => {
    try {
      console.log('Logout started');
      setIsLoading(true);
      setError(null);

      // Always try to sign out from Supabase, regardless of user type
      try {
        await signOut();
        console.log('Supabase signOut completed');
      } catch (signOutErr) {
        console.error('Error in Supabase signOut:', signOutErr);
        // Continue with the logout process even if Supabase signOut fails
      }

      // Clear all local storage
      try {
        await SecureStore.deleteItemAsync(CHILD_USER_KEY);
        await SecureStore.deleteItemAsync(USER_TYPE_KEY);
        console.log('SecureStore items deleted');
      } catch (storageErr) {
        console.error('Error clearing SecureStore:', storageErr);
      }

      // Reset state
      setUser(null);
      setUserType(null);
      console.log('Auth state reset');

      return true; // Indicate successful logout
    } catch (err: any) {
      console.error('Sign out error:', err);
      setError(err.message);
      return false; // Indicate failed logout
    } finally {
      setIsLoading(false);
    }
  };

  // Clear any auth errors
  const clearError = () => setError(null);

  // Refresh subscription status
  const refreshSubscriptionStatus = async () => {
    if (!user || userType !== 'parent') {
      setSubscriptionStatus(null);
      return;
    }

    try {
      setIsSubscriptionLoading(true);
      console.log('[AuthContext] 🔄 Checking subscription status...');

      // Initialize billing service if not already done
      if (!nativeBillingService.isInitialized) {
        const initialized = await nativeBillingService.initialize();
        if (!initialized) {
          throw new Error('Failed to initialize billing service');
        }
      }

      // Get current purchases
      const purchases = await nativeBillingService.getPurchases();
      console.log('[AuthContext] 📦 Retrieved purchases:', purchases.length);

      // Check for active subscription
      const activePurchase = purchases.find(purchase =>
        purchase.isActive &&
        (purchase.productId === SUBSCRIPTION_SKUS.MONTHLY || purchase.productId === SUBSCRIPTION_SKUS.YEARLY)
      );

      if (activePurchase) {
        console.log('[AuthContext] ✅ Active subscription found:', activePurchase.productId);

        // Calculate days remaining
        let daysRemaining = 0;
        if (activePurchase.expirationDate) {
          const now = Date.now();
          const expiration = activePurchase.expirationDate;
          daysRemaining = Math.max(0, Math.ceil((expiration - now) / (1000 * 60 * 60 * 24)));
        }

        setSubscriptionStatus({
          hasSubscription: true,
          isActive: true,
          status: 'active',
          daysRemaining,
          subscription: activePurchase,
        });
      } else {
        console.log('[AuthContext] ❌ No active subscription found');
        setSubscriptionStatus({
          hasSubscription: false,
          isActive: false,
          status: 'none',
          daysRemaining: 0,
        });
      }
    } catch (err: any) {
      console.error('Error checking subscription status:', err);
      setSubscriptionStatus({
        hasSubscription: false,
        isActive: false,
        status: 'error',
        daysRemaining: 0,
      });
    } finally {
      setIsSubscriptionLoading(false);
    }
  };

  // Start a free trial
  const startFreeTrial = async () => {
    if (!user || userType !== 'parent') {
      return false;
    }

    try {
      setIsSubscriptionLoading(true);
      console.log('[AuthContext] 🚀 Starting free trial for user:', user.id);

      // Initialize billing service if not already done
      if (!nativeBillingService.isInitialized) {
        const initialized = await nativeBillingService.initialize();
        if (!initialized) {
          throw new Error('Failed to initialize billing service');
        }
      }

      // Purchase the monthly subscription (which includes free trial)
      const success = await nativeBillingService.purchaseSubscription(SUBSCRIPTION_SKUS.MONTHLY);

      if (success) {
        // Refresh subscription status after starting trial
        await refreshSubscriptionStatus();
        return true;
      }

      return false;
    } catch (err: any) {
      console.error('Error starting free trial:', err);
      return false;
    } finally {
      setIsSubscriptionLoading(false);
    }
  };

  // Initialize billing when user logs in
  useEffect(() => {
    if (user && userType === 'parent') {
      // Initialize billing
      console.log('[AuthContext] 🔧 Initializing billing for parent user');
      nativeBillingService.initialize().then(success => {
        if (success) {
          console.log('[AuthContext] ✅ Billing initialized successfully');
        } else {
          console.error('[AuthContext] ❌ Failed to initialize billing');
        }
      }).catch(error => {
        console.error('[AuthContext] ❌ Billing initialization error:', error);
      });

      // Check subscription status
      refreshSubscriptionStatus();
    }
  }, [user, userType]);

  return (
    <AuthContext.Provider
      value={{
        user,
        userType,
        isLoading,
        error,
        subscriptionStatus,
        isSubscriptionLoading,
        signUp,
        signIn,
        signInChild,
        logout,
        clearError,
        refreshUser,
        startFreeTrial,
        refreshSubscriptionStatus,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};