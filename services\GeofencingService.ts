import * as Location from 'expo-location';
import { checkSafeZoneStatus } from '../utils/locationUtils';
import { getSafeZones } from '../utils/supabase';
import createGeofenceNotification from '../utils/createGeofenceNotification';

// Memorizza lo stato precedente di ogni zona per ogni bambino
const childZoneStatus: Record<string, Record<string, boolean>> = {};

/**
 * Inizializza il servizio di geofencing per un bambino
 * @param childId ID del bambino
 * @param childName Nome del bambino
 * @param parentId ID del genitore (per ottenere le zone sicure)
 */
export const initGeofencingService = async (
  childId: string,
  childName: string,
  parentId: string
) => {
  console.log(`Initializing geofencing service for child: ${childName} (${childId})`);

  // Inizializza lo stato delle zone per questo bambino
  childZoneStatus[childId] = {};

  // Ottieni le zone sicure del genitore
  const safeZones = await getSafeZones(parentId);
  console.log(`Loaded ${safeZones.length} safe zones for parent ${parentId}`);

  try {
    // Richiedi i permessi di localizzazione
    console.log('Requesting location permissions');
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      console.error('Permission to access location was denied');
      return false;
    }

    // Avvia il monitoraggio della posizione
    console.log('[GeofencingService] Starting location monitoring');
    const subscription = await Location.watchPositionAsync(
      {
        accuracy: Location.Accuracy.High,
        distanceInterval: 5, // Aggiorna ogni 5 metri
        timeInterval: 3000,   // Aggiorna ogni 3 secondi
        mayShowUserSettingsDialog: true, // Mostra il dialogo per le impostazioni se necessario
      },
      async (location) => {
        console.log('[GeofencingService] Location update received:', location.coords);
        try {
          await checkGeofences(
            childId,
            childName,
            location.coords.latitude,
            location.coords.longitude,
            safeZones
          );
        } catch (error) {
          console.error('[GeofencingService] Error checking geofences:', error);
        }
      }
    );

    // Memorizza la sottoscrizione per poterla cancellare in seguito
    locationSubscriptions[childId] = subscription;
    console.log('Location monitoring started successfully');
  } catch (error) {
    console.error('Failed to initialize location monitoring:', error);
    return false;
  }

  return true;
};

/**
 * Verifica se il bambino è entrato o uscito da una zona sicura
 */
const checkGeofences = async (
  childId: string,
  childName: string,
  latitude: number,
  longitude: number,
  safeZones: any[]
) => {
  console.log(`[GeofencingService] Checking geofences for ${childName} at ${latitude}, ${longitude}`);
  console.log(`[GeofencingService] Number of safe zones to check: ${safeZones.length}`);

  // Inizializza lo stato per questo bambino se non esiste
  if (!childZoneStatus[childId]) {
    childZoneStatus[childId] = {};
    console.log(`[GeofencingService] Initializing zone status for child ${childId}`);
  }

  // Verifica ogni zona sicura
  for (const zone of safeZones) {
    if (!zone || !zone.id || !zone.name) {
      console.warn(`[GeofencingService] Invalid zone data:`, zone);
      continue;
    }

    const zoneId = zone.id;
    const zoneName = zone.name;

    console.log(`[GeofencingService] Checking zone: ${zoneName} (${zoneId})`);
    console.log(`[GeofencingService] Zone coordinates: ${zone.latitude}, ${zone.longitude}, radius: ${zone.radius}m`);

    // Verifica se il bambino è nella zona
    const zoneStatus = checkSafeZoneStatus(latitude, longitude, [zone]);
    const isInZone = zoneStatus?.isInside || false;

    console.log(`[GeofencingService] Child ${childName} is ${isInZone ? 'inside' : 'outside'} zone ${zoneName}`);

    // Ottieni lo stato precedente (se esiste)
    const wasInZone = childZoneStatus[childId][zoneId] || false;
    console.log(`[GeofencingService] Previous status for zone ${zoneName}: ${wasInZone ? 'inside' : 'outside'}`);

    // Aggiorna lo stato
    childZoneStatus[childId][zoneId] = isInZone;

    // Invia notifiche solo se lo stato è cambiato
    if (isInZone && !wasInZone) {
      // Il bambino è entrato nella zona
      console.log(`[GeofencingService] ${childName} has entered safe zone: ${zoneName}`);

      try {
        const notificationId = await createGeofenceNotification(childName, zoneName, true);
        console.log(`[GeofencingService] Entry notification sent with ID: ${notificationId}`);
      } catch (error) {
        console.error(`[GeofencingService] Error sending entry notification:`, error);
      }

      // Qui puoi anche salvare l'evento nel database
    } else if (!isInZone && wasInZone) {
      // Il bambino è uscito dalla zona
      console.log(`[GeofencingService] ${childName} has left safe zone: ${zoneName}`);

      try {
        const notificationId = await createGeofenceNotification(childName, zoneName, false);
        console.log(`[GeofencingService] Exit notification sent with ID: ${notificationId}`);
      } catch (error) {
        console.error(`[GeofencingService] Error sending exit notification:`, error);
      }

      // Qui puoi anche salvare l'evento nel database
    } else {
      console.log(`[GeofencingService] No status change for ${childName} in zone ${zoneName}`);
    }
  }
};

// Memorizza i subscription IDs per poterli cancellare
const locationSubscriptions: Record<string, { remove: () => void }> = {};

/**
 * Ferma il servizio di geofencing per un bambino
 */
export const stopGeofencingService = (childId: string) => {
  console.log(`Stopping geofencing service for child: ${childId}`);

  // Rimuovi lo stato delle zone per questo bambino
  delete childZoneStatus[childId];

  // Annulla la sottoscrizione al monitoraggio della posizione
  if (locationSubscriptions[childId]) {
    try {
      // Rimuovi la sottoscrizione a expo-location
      locationSubscriptions[childId].remove();
      delete locationSubscriptions[childId];
      console.log(`Geofencing subscription removed for child: ${childId}`);
    } catch (error) {
      console.error(`Error removing geofencing subscription for child: ${childId}`, error);
    }
  }
};

export default {
  initGeofencingService,
  stopGeofencingService
};
