import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Header from '../../components/shared/Header';
import LanguageSelector from '../../components/shared/LanguageSelector';
import { useTranslations } from '../../contexts/TranslationContext';

export default function SettingsScreen() {
  const { user, logout, subscriptionStatus } = useAuth();
  const { currentLanguage, setLanguage } = useLanguage();
  const router = useRouter();

  // Ottieni le traduzioni in base alla lingua corrente
  const { t } = useTranslations();

  const handleLogout = async () => {
    try {
      console.log('Logout initiated from settings screen');
      const success = await logout();

      if (success) {
        console.log('Logout successful, navigating to login screen');
        // Explicitly navigate to the login screen and reset the navigation stack
        router.replace({
          pathname: '/auth/parent-login',
          params: { reset: 'true' }
        });
      } else {
        console.error('Logout returned false');
        Alert.alert('Error', 'Failed to log out. Please try again.');
      }
    } catch (error) {
      console.error('Error logging out:', error);
      Alert.alert('Error', 'Failed to log out. Please try again.');
    }
  };

  const confirmLogout = () => {
    Alert.alert(
      t.settings.confirmLogout.title,
      t.settings.confirmLogout.message,
      [
        { text: t.settings.confirmLogout.cancel, style: 'cancel' },
        { text: t.settings.confirmLogout.logout, onPress: handleLogout, style: 'destructive' },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <Header titleKey="settings.title" />

      <ScrollView style={styles.content}>
        {/* Subscription Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t.subscription?.title || 'Subscription'}</Text>

          <View style={[
            styles.subscriptionStatus,
            subscriptionStatus?.isActive ? styles.activeSubscriptionStatus : styles.inactiveSubscriptionStatus
          ]}>
            <FontAwesome5
              name={subscriptionStatus?.isActive ? "check-circle" : "exclamation-circle"}
              size={20}
              color={subscriptionStatus?.isActive ? "#4CAF50" : "#F44336"}
              style={styles.settingIcon}
            />
            <View style={styles.subscriptionInfo}>
              <Text style={[
                styles.subscriptionStatusText,
                subscriptionStatus?.isActive ? styles.activeSubscriptionText : styles.inactiveSubscriptionText
              ]}>
                {subscriptionStatus?.isActive
                  ? "Abbonamento Attivo"
                  : "Nessun Abbonamento Attivo"}
              </Text>
              {subscriptionStatus?.isActive && subscriptionStatus.status === 'trial' && (
                <Text style={styles.subscriptionDetails}>
                  {subscriptionStatus.daysRemaining} {subscriptionStatus.daysRemaining === 1
                    ? (t.subscription?.day || 'giorno')
                    : (t.subscription?.days || 'giorni')} {t.subscription?.remaining || 'rimanenti'}
                </Text>
              )}

              {subscriptionStatus?.isActive && subscriptionStatus.status === 'active' && subscriptionStatus.subscription?.subscription_plan && (
                <Text style={styles.subscriptionDetails}>
                  {subscriptionStatus.subscription.subscription_plan === 'monthly_subscription'
                    ? t.subscription.monthlyPlan || 'Abbonamento Mensile'
                    : subscriptionStatus.subscription.subscription_plan === 'annual_subscription'
                      ? t.subscription.yearlyPlan || 'Abbonamento Annuale'
                      : subscriptionStatus.subscription.subscription_plan}
                </Text>
              )}
            </View>
          </View>

          <View style={styles.subscriptionInfoBox}>
            <Text style={styles.subscriptionInfoText}>
              {t.subscription.subscriptionRequired}
            </Text>
          </View>

          <View style={[styles.subscriptionInfoBox, {borderLeftColor: '#FF9800', backgroundColor: '#FFF3E0'}]}>
            <Text style={styles.subscriptionInfoText}>
              <Text style={{fontWeight: 'bold', color: '#FF9800'}}>Importante:</Text> L'abbonamento mensile include 3 giorni di prova gratuita. L'abbonamento annuale offre un risparmio del 67%.
            </Text>
          </View>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => router.push('/subscription')}
          >
            <FontAwesome5 name="crown" size={18} color="#4630EB" style={styles.settingIcon} />
            <Text style={styles.settingText}>Piani di Abbonamento</Text>
            <FontAwesome5 name="chevron-right" size={16} color="#ccc" />
          </TouchableOpacity>



          <View style={styles.subscriptionInfoBox}>
            <Text style={styles.subscriptionInfoText}>
              {t.subscription.autoRenewalNotice}
            </Text>
          </View>
        </View>

        {/* Settings Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t.settings.title}</Text>

          <View style={styles.settingItem}>
            <FontAwesome5 name="language" size={18} color="#4630EB" style={styles.settingIcon} />
            <LanguageSelector
              selectedLanguage={currentLanguage}
              onSelectLanguage={async (language) => {
                // Cambia la lingua
                const success = await setLanguage(language);

                if (success) {
                  // Importa la funzione getTranslations per ottenere le traduzioni per la nuova lingua
                  const { getTranslations } = require('../../utils/translations');
                  const newT = getTranslations(language.code);

                  // Mostra l'alert con le nuove traduzioni
                  Alert.alert(
                    newT.settings.languageChanged.split(' ')[0], // Prendi solo la prima parola del messaggio
                    newT.settings.languageChanged,
                    [{ text: newT.common.ok }]
                  );
                } else {
                  // Mostra un messaggio di errore
                  Alert.alert(
                    t.common.error,
                    'Failed to change language. Please try again.',
                    [{ text: t.common.ok }]
                  );
                }
              }}
            />
          </View>
        </View>



        <TouchableOpacity style={styles.logoutButton} onPress={confirmLogout}>
          <FontAwesome5 name="sign-out-alt" size={18} color="#fff" style={styles.logoutIcon} />
          <Text style={styles.logoutText}>{t.settings.logout}</Text>
        </TouchableOpacity>

        <Text style={styles.versionText}>{t.settings.version} 1.0.0</Text>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  profileIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  profileType: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  subscriptionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderRadius: 8,
    marginBottom: 12,
  },
  activeSubscriptionStatus: {
    backgroundColor: '#E8F5E9',
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  inactiveSubscriptionStatus: {
    backgroundColor: '#FFEBEE',
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  subscriptionInfo: {
    flex: 1,
  },
  subscriptionStatusText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  activeSubscriptionText: {
    color: '#4CAF50',
  },
  inactiveSubscriptionText: {
    color: '#F44336',
  },
  subscriptionDetails: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  subscriptionInfoBox: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    marginVertical: 10,
    borderLeftWidth: 3,
    borderLeftColor: '#4630EB',
  },
  subscriptionInfoText: {
    fontSize: 12,
    color: '#333333',
    lineHeight: 18,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingIcon: {
    width: 30,
  },
  settingText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F44336',
    borderRadius: 12,
    padding: 16,
    marginVertical: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  logoutIcon: {
    marginRight: 8,
  },
  logoutText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  versionText: {
    textAlign: 'center',
    fontSize: 12,
    color: '#999',
    marginBottom: 24,
  },
});
