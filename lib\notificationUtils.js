import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

// Request notification permissions
export async function requestNotificationPermissions() {
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  const { status } = await Notifications.requestPermissionsAsync();
  return status === 'granted';
}

// Schedule a local notification
export async function scheduleLocalNotification(title, body, data = {}) {
  await Notifications.scheduleNotificationAsync({
    content: {
      title,
      body,
      data,
      sound: true,
    },
    trigger: null, // null means show immediately
  });
}

// Send a zone notification
export async function sendZoneNotification(childName, zoneName, action) {
  const title = `${childName} ${action} ${zoneName}`;
  const body = action === 'entered'
    ? `${childName} has arrived at ${zoneName}`
    : `${childName} has left ${zoneName}`;

  await scheduleLocalNotification(title, body, {
    type: 'zone',
    childName,
    zoneName,
    action,
    timestamp: new Date().toISOString(),
  });
}

// Send a mission notification
export async function sendMissionNotification(childName, missionName, status) {
  const title = `Mission ${status}`;
  const body = status === 'completed'
    ? `${childName} has completed the mission: ${missionName}`
    : `${childName} has started the mission: ${missionName}`;

  await scheduleLocalNotification(title, body, {
    type: 'mission',
    childName,
    missionName,
    status,
    timestamp: new Date().toISOString(),
  });
}

// Send an SOS notification
export async function sendSOSNotification(childName, location) {
  const title = `SOS Alert from ${childName}`;
  const body = `${childName} has sent an emergency alert from ${location}`;

  await scheduleLocalNotification(title, body, {
    type: 'sos',
    childName,
    location,
    timestamp: new Date().toISOString(),
  });
}

// Default export to satisfy Expo Router
export default {
  requestNotificationPermissions,
  scheduleLocalNotification,
  sendZoneNotification,
  sendMissionNotification,
  sendSOSNotification
};
