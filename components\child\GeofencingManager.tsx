import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Switch } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { initGeofencingService, stopGeofencingService } from '../../services/GeofencingService';
import { startLocationTracking, stopLocationTracking } from '../../utils/location';
import { FontAwesome5 } from '@expo/vector-icons';

const GeofencingManager: React.FC = () => {
  const { user } = useAuth();
  const [isEnabled, setIsEnabled] = useState(false);
  const [parentId, setParentId] = useState<string | null>(null);

  useEffect(() => {
    // Ottieni l'ID del genitore dal bambino
    const getParentId = async () => {
      if (user) {
        console.log('GeofencingManager - Child user:', user);

        // In una situazione reale, dovresti ottenere l'ID del genitore dal database
        // Per ora, usiamo un valore fittizio o lo otteniamo dalle relazioni
        // Questo è solo un esempio, dovresti adattarlo alla tua struttura dati
        const parentIdValue = user.parent_id || null;
        console.log('GeofencingManager - Parent ID:', parentIdValue);
        setParentId(parentIdValue);

        // Avvia automaticamente il tracciamento della posizione se abbiamo l'ID del bambino
        if (user.id) {
          try {
            console.log('GeofencingManager - Starting location tracking for child:', user.id);

            // Avvia il tracciamento della posizione con un ritardo per assicurarsi che l'app sia completamente inizializzata
            setTimeout(async () => {
              try {
                await startLocationTracking(user.id);
                console.log('Location tracking started successfully for child:', user.id);
                setIsEnabled(true);
              } catch (delayedError) {
                console.error('Delayed error starting location tracking:', delayedError);
                setIsEnabled(false);
              }
            }, 2000); // Ritardo di 2 secondi
          } catch (error) {
            console.error('GeofencingManager - Error starting location tracking:', error);
            setIsEnabled(false);
          }
        }
      }
    };

    getParentId();
  }, [user]);

  const toggleGeofencing = async (value: boolean) => {
    if (!user || !parentId) return;

    if (value) {
      try {
        // Avvia il servizio di geofencing
        const success = await initGeofencingService(
          user.id,
          user.name || 'Child',
          parentId
        );

        // Avvia il tracciamento della posizione
        console.log('Manually starting location tracking for child:', user.id);
        await startLocationTracking(user.id);

        if (success) {
          setIsEnabled(true);
        } else {
          setIsEnabled(false);
        }
      } catch (error) {
        console.error('Error toggling geofencing ON:', error);
        setIsEnabled(false);
      }
    } else {
      try {
        // Ferma il servizio di geofencing
        stopGeofencingService(user.id);

        // Ferma il tracciamento della posizione
        console.log('Stopping location tracking for child:', user.id);
        await stopLocationTracking();

        setIsEnabled(false);
      } catch (error) {
        console.error('Error toggling geofencing OFF:', error);
      }
    }
  };

  if (!user || !parentId) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Unable to initialize location tracking.</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <FontAwesome5 name="map-marker-alt" size={20} color="#4630EB" />
        <Text style={styles.title}>Location Tracking</Text>
      </View>

      <View style={styles.switchContainer}>
        <Text style={styles.switchLabel}>
          {isEnabled ? 'Tracking is active' : 'Tracking is disabled'}
        </Text>
        <Switch
          trackColor={{ false: '#767577', true: '#81b0ff' }}
          thumbColor={isEnabled ? '#4630EB' : '#f4f3f4'}
          ios_backgroundColor="#3e3e3e"
          onValueChange={toggleGeofencing}
          value={isEnabled}
        />
      </View>

      <Text style={styles.description}>
        When enabled, your parents will receive notifications when you enter or leave safe zones.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
    color: '#333',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
  },
  description: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#f44336',
    textAlign: 'center',
  },
});

export default GeofencingManager;
