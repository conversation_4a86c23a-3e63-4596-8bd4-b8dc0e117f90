# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
/android
/ios

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

app-example

# @generated expo-cli sync-8d4afeec25ea8a192358fae2f8e2fc766bdce4ec
# The following patterns were generated by expo-cli

# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local
# .env - Rimosso per permettere il caricamento delle variabili di sviluppo
.env.production
.env.staging
.env.development

# typescript
*.tsbuildinfo

# 🔐 KidSafety Security Files
# IMPORTANTE: Non committare mai questi file!
google-services.json
GoogleService-Info.plist
*.keystore
*.jks
*.p12
*.mobileprovision
*.certSigningRequest
*.cer
*.p8

# API Keys e Secrets
**/secrets/
**/keys/
**/*secret*
**/*key*
**/*token*
**/*password*

# Database e Backup
*.db
*.sqlite
*.sqlite3
backup/
dumps/

# Log Files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Security Reports
security-audit-report.json
vulnerability-report.json
penetration-test-results/

# Temporary Files
tmp/
temp/
.tmp/
.temp/

# IDE Files
.vscode/settings.json
.idea/
*.swp
*.swo
*~

# OS Files
Thumbs.db
ehthumbs.db
Desktop.ini

# @end expo-cli