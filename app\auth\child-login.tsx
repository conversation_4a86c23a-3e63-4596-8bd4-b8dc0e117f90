import React, { useEffect } from 'react';
import { View, StyleSheet, Text, TouchableOpacity, Alert } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import AuthForm from '../../components/auth/AuthForm';
import LoadingIndicator from '../../components/shared/LoadingIndicator';
import Header from '../../components/shared/Header';
// Token validation utility (moved inline to fix build issues)
const TOKEN_CHARSET = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
const TOKEN_LENGTH = 6;

const cleanToken = (token: string): string => {
  return token.replace(/[^A-Z0-9]/gi, '').toUpperCase();
};

const isValidTokenFormat = (token: string): boolean => {
  const cleanedToken = cleanToken(token);

  if (cleanedToken.length !== TOKEN_LENGTH) {
    return false;
  }

  for (let i = 0; i < cleanedToken.length; i++) {
    if (!TOKEN_CHARSET.includes(cleanedToken[i])) {
      return false;
    }
  }

  return true;
};
import { Ionicons } from '@expo/vector-icons';
import { useTranslations } from '../../contexts/TranslationContext';

export default function ChildLoginScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { signInChild, error, isLoading, clearError } = useAuth();
  const { t } = useTranslations();

  // Handle reset parameter to prevent going back after logout
  useEffect(() => {
    if (params.reset === 'true') {
      console.log('Reset parameter detected, preventing back navigation');
    }
  }, [params]);

  // Handle form submission
  const handleLogin = async (values: Record<string, string>) => {
    try {
      // Format token (remove spaces, convert to uppercase)
      let token = values.token.trim();

      console.log('Child login with token:', token);

      // Validate token format (allow formats like XX-XX-XX or XXXXXX)
      const cleanToken = token.replace(/[^A-Z0-9]/gi, '').toUpperCase();
      if (cleanToken.length !== 6) {
        console.error('Invalid token format:', token);
        Alert.alert('Error', 'Invalid token format. Please enter a 6-character code.');
        return;
      }

      // Use the original token format (with or without hyphens)
      await signInChild(token);
      console.log('Child login successful, navigating to onboarding');
      router.replace("/auth/child-onboarding" as any);
    } catch (err: any) {
      // Error is handled by the AuthContext, but we might have a local validation error
      console.log('Child login error:', err.message);
      Alert.alert('Error', err.message || 'Unable to login. Please try again later.');
    }
  };

  // Navigate back to parent login
  const navigateToParentLogin = () => {
    // If we have a reset parameter, navigate to parent login instead of going back
    if (params.reset === 'true') {
      router.replace('/auth/parent-login');
    } else {
      router.back();
    }
  };

  if (isLoading) {
    return <LoadingIndicator fullScreen text="Logging in..." />;
  }

  return (
    <View style={styles.container}>
      <Header
        titleKey="auth.childLogin"
        showBackButton
        onBackPress={navigateToParentLogin}
      />

      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <View style={styles.logoIcon}>
            <Ionicons name="shield-checkmark" size={64} color="#4630EB" />
          </View>
          <Text style={styles.appName}>KidSafety</Text>
          <Text style={styles.tagline}>Just for kids!</Text>
        </View>

        <View style={styles.instructionContainer}>
          <Text style={styles.instructionText}>
            Enter the access code that your parent gave you
          </Text>
        </View>

        <View style={styles.formContainer}>
          <AuthForm
            formType="childLogin"
            onSubmit={handleLogin}
            isLoading={isLoading}
            error={error}
          />
        </View>

        <TouchableOpacity
          style={styles.parentLoginButton}
          onPress={navigateToParentLogin}
        >
          <Text style={styles.parentLoginText}>Parent? Login here</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  logoIcon: {
    width: 100,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: '#F0F0F0',
    borderRadius: 50,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4630EB',
    marginBottom: 4,
  },
  tagline: {
    fontSize: 18,
    color: '#666666',
  },
  instructionContainer: {
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#555555',
  },
  formContainer: {
    flex: 1,
  },
  parentLoginButton: {
    padding: 16,
    alignItems: 'center',
  },
  parentLoginText: {
    fontSize: 16,
    color: '#4630EB',
  },
});