import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Alert, ActivityIndicator, Modal, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { getParentSOSAlerts, acknowledgeSOSAlert } from '../utils/supabase';
import { Ionicons } from '@expo/vector-icons';
import WebViewOpenStreetMap from '../components/shared/WebViewOpenStreetMap';
import NativeOpenStreetMapView from '../components/shared/NativeOpenStreetMapView';

const SOSAlertScreen = () => {
  const [loading, setLoading] = useState(true);
  const [alerts, setAlerts] = useState<any[]>([]);
  const [selectedAlert, setSelectedAlert] = useState<any>(null);
  const [mapModalVisible, setMapModalVisible] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    fetchAlerts();
  }, []);

  const fetchAlerts = async () => {
    try {
      setLoading(true);
      const alertsData = await getParentSOSAlerts();
      setAlerts(alertsData);
    } catch (error) {
      console.error('Error fetching alerts:', error);
      Alert.alert('Error', 'Failed to load SOS alerts');
    } finally {
      setLoading(false);
    }
  };

  const handleAcknowledge = async (alertId: string) => {
    try {
      await acknowledgeSOSAlert(alertId);
      // Update the local state to reflect the change
      setAlerts(alerts.map(alert =>
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      ));
      Alert.alert('Success', 'Alert acknowledged successfully');
    } catch (error) {
      console.error('Error acknowledging alert:', error);
      Alert.alert('Error', 'Failed to acknowledge alert');
    }
  };

  const handleViewMap = (alert) => {
    setSelectedAlert(alert);
    setMapModalVisible(true);
  };

  const renderItem = ({ item }) => {
    const date = new Date(item.created_at);
    const formattedDate = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;

    return (
      <View style={styles.alertCard}>
        <View style={styles.headerRow}>
          <Text style={styles.childName}>{item.child_name || 'Unknown Child'}</Text>
          <Text style={styles.timestamp}>{formattedDate}</Text>
        </View>

        <TouchableOpacity
          style={styles.locationContainer}
          onPress={() => handleViewMap(item)}
        >
          <Ionicons name="location" size={20} color="#FF3B30" />
          <Text style={styles.locationText}>
            Lat: {item.latitude.toFixed(6)}, Lng: {item.longitude.toFixed(6)}
          </Text>
          <Ionicons name="map-outline" size={20} color="#007AFF" style={styles.mapIcon} />
        </TouchableOpacity>

        {!item.acknowledged && (
          <TouchableOpacity
            style={styles.acknowledgeButton}
            onPress={() => handleAcknowledge(item.id)}
          >
            <Text style={styles.buttonText}>Acknowledge Alert</Text>
          </TouchableOpacity>
        )}

        {item.acknowledged && (
          <View style={styles.acknowledgedContainer}>
            <Ionicons name="checkmark-circle" size={18} color="#34C759" />
            <Text style={styles.acknowledgedText}>Acknowledged</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>SOS Alerts</Text>
        <TouchableOpacity onPress={fetchAlerts} style={styles.refreshButton}>
          <Ionicons name="refresh" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {loading ? (
        <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
      ) : alerts.length > 0 ? (
        <FlatList
          data={alerts}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          contentContainerStyle={styles.listContainer}
        />
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="alert-circle-outline" size={64} color="#8E8E93" />
          <Text style={styles.emptyText}>No SOS alerts found</Text>
        </View>
      )}

      {/* Map Modal */}
      <Modal
        visible={mapModalVisible}
        animationType="slide"
        transparent={false}
        onRequestClose={() => setMapModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {selectedAlert?.child_name || 'Child'}'s Location
            </Text>
            <TouchableOpacity
              onPress={() => setMapModalVisible(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.mapContainer}>
            {selectedAlert && (
              Platform.OS === 'android' ? (
                <NativeOpenStreetMapView
                  latitude={selectedAlert.latitude}
                  longitude={selectedAlert.longitude}
                  zoom={16}
                  style={styles.map}
                  markers={[
                    {
                      latitude: selectedAlert.latitude,
                      longitude: selectedAlert.longitude,
                      title: selectedAlert.child_name || 'Child'
                    }
                  ]}
                />
              ) : (
                <WebViewOpenStreetMap
                  latitude={selectedAlert.latitude}
                  longitude={selectedAlert.longitude}
                  zoom={16}
                  style={styles.map}
                  markers={[
                    {
                      latitude: selectedAlert.latitude,
                      longitude: selectedAlert.longitude,
                      title: selectedAlert.child_name || 'Child'
                    }
                  ]}
                />
              )
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  refreshButton: {
    padding: 8,
  },
  listContainer: {
    padding: 16,
  },
  alertCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  childName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  timestamp: {
    fontSize: 14,
    color: '#8E8E93',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  locationText: {
    marginLeft: 8,
    fontSize: 15,
    color: '#333',
    flex: 1,
  },
  mapIcon: {
    marginLeft: 8,
  },
  acknowledgeButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  acknowledgedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
  },
  acknowledgedText: {
    color: '#34C759',
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    color: '#8E8E93',
    marginTop: 16,
    textAlign: 'center',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 8,
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
});

export default SOSAlertScreen;