// Fallback implementation for Expo Go compatibility
const EventFrequency = {
  INTERVAL_DAILY: 'INTERVAL_DAILY',
  INTERVAL_WEEKLY: 'INTERVAL_WEEKLY',
  INTERVAL_MONTHLY: 'INTERVAL_MONTHLY',
  INTERVAL_YEARLY: 'INTERVAL_YEARLY',
};

const checkForPermission = async (): Promise<boolean> => {
  console.warn('[AppUsageService] Usage stats module not available in Expo Go');
  return false;
};

const queryUsageStats = async (frequency: any, start: number, end: number): Promise<any[]> => {
  console.warn('[AppUsageService] Usage stats query not available in Expo Go');
  return [];
};

const showUsageAccessSettings = async (packageName: string): Promise<void> => {
  console.warn('[AppUsageService] Usage stats settings not available in Expo Go');
};

export interface AppUsageData {
  packageName: string;
  appName?: string;
  totalTimeInForeground: number;
  lastTimeUsed: number;
  firstTimeStamp: number;
  lastTimeStamp: number;
}

export interface AppUsageStats {
  date: string;
  totalScreenTime: number;
  apps: AppUsageData[];
}

class AppUsageService {
  private isInitialized = false;

  constructor() {
    console.log('[AppUsageService] 🏗️ Initializing App Usage Service...');
  }

  /**
   * Controlla se l'app ha il permesso per accedere alle statistiche di utilizzo
   */
  async checkPermission(): Promise<boolean> {
    try {
      console.log('[AppUsageService] 🔍 Checking usage stats permission...');
      const hasPermission = await checkForPermission();
      console.log('[AppUsageService] ✅ Permission status:', hasPermission);
      return hasPermission;
    } catch (error) {
      console.error('[AppUsageService] ❌ Error checking permission:', error);
      return false;
    }
  }

  /**
   * Apre le impostazioni per concedere il permesso di accesso alle statistiche
   */
  async requestPermission(): Promise<void> {
    try {
      console.log('[AppUsageService] 🚀 Opening usage access settings...');
      await showUsageAccessSettings('');
    } catch (error) {
      console.error('[AppUsageService] ❌ Error opening settings:', error);
      throw error;
    }
  }

  /**
   * Ottiene le statistiche di utilizzo delle app per un periodo specificato
   */
  async getUsageStats(
    startDate: Date,
    endDate: Date,
    frequency: EventFrequency = EventFrequency.INTERVAL_DAILY
  ): Promise<AppUsageData[]> {
    try {
      const hasPermission = await this.checkPermission();
      if (!hasPermission) {
        throw new Error('Usage stats permission not granted');
      }

      const startMilliseconds = startDate.getTime();
      const endMilliseconds = endDate.getTime();

      console.log('[AppUsageService] 📊 Querying usage stats:', {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
        frequency
      });

      const result = await queryUsageStats(
        frequency,
        startMilliseconds,
        endMilliseconds
      );

      console.log('[AppUsageService] ✅ Raw usage stats:', result);

      // Processa i dati ricevuti
      const processedData = this.processUsageData(result);
      console.log('[AppUsageService] 📦 Processed usage data:', processedData.length, 'apps');

      return processedData;
    } catch (error) {
      console.error('[AppUsageService] ❌ Error getting usage stats:', error);
      throw error;
    }
  }

  /**
   * Ottiene le statistiche di utilizzo per oggi
   */
  async getTodayUsageStats(): Promise<AppUsageData[]> {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

    return this.getUsageStats(startOfDay, endOfDay);
  }

  /**
   * Ottiene le statistiche di utilizzo per ieri
   */
  async getYesterdayUsageStats(): Promise<AppUsageData[]> {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 0, 0, 0);
    const endOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);

    return this.getUsageStats(startOfDay, endOfDay);
  }

  /**
   * Ottiene le statistiche di utilizzo per una settimana
   */
  async getWeeklyUsageStats(): Promise<AppUsageStats[]> {
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const dailyStats: AppUsageStats[] = [];

    for (let i = 0; i < 7; i++) {
      const date = new Date(weekAgo.getTime() + i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);
      const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);

      try {
        const apps = await this.getUsageStats(startOfDay, endOfDay);
        const totalScreenTime = apps.reduce((total, app) => total + app.totalTimeInForeground, 0);

        dailyStats.push({
          date: date.toISOString().split('T')[0],
          totalScreenTime,
          apps
        });
      } catch (error) {
        console.error(`[AppUsageService] ❌ Error getting stats for ${date.toISOString()}:`, error);
        dailyStats.push({
          date: date.toISOString().split('T')[0],
          totalScreenTime: 0,
          apps: []
        });
      }
    }

    return dailyStats;
  }

  /**
   * Ottiene le app più utilizzate
   */
  async getTopUsedApps(limit: number = 10): Promise<AppUsageData[]> {
    const todayStats = await this.getTodayUsageStats();
    
    return todayStats
      .filter(app => app.totalTimeInForeground > 0)
      .sort((a, b) => b.totalTimeInForeground - a.totalTimeInForeground)
      .slice(0, limit);
  }

  /**
   * Processa i dati grezzi ricevuti dal modulo nativo
   */
  private processUsageData(rawData: any[]): AppUsageData[] {
    if (!Array.isArray(rawData)) {
      console.warn('[AppUsageService] ⚠️ Raw data is not an array:', rawData);
      return [];
    }

    return rawData
      .map((item: any) => {
        try {
          return {
            packageName: item.packageName || item.package_name || 'unknown',
            appName: item.appName || item.app_name || this.getAppNameFromPackage(item.packageName),
            totalTimeInForeground: parseInt(item.totalTimeInForeground || item.total_time_in_foreground || '0'),
            lastTimeUsed: parseInt(item.lastTimeUsed || item.last_time_used || '0'),
            firstTimeStamp: parseInt(item.firstTimeStamp || item.first_time_stamp || '0'),
            lastTimeStamp: parseInt(item.lastTimeStamp || item.last_time_stamp || '0'),
          };
        } catch (error) {
          console.error('[AppUsageService] ❌ Error processing item:', item, error);
          return null;
        }
      })
      .filter((item): item is AppUsageData => item !== null)
      .filter(app => app.totalTimeInForeground > 0); // Filtra app con tempo di utilizzo > 0
  }

  /**
   * Estrae il nome dell'app dal package name
   */
  private getAppNameFromPackage(packageName: string): string {
    if (!packageName) return 'Unknown App';
    
    // Rimuove il dominio e prende l'ultima parte
    const parts = packageName.split('.');
    const appName = parts[parts.length - 1];
    
    // Capitalizza la prima lettera
    return appName.charAt(0).toUpperCase() + appName.slice(1);
  }

  /**
   * Formatta il tempo in millisecondi in una stringa leggibile
   */
  formatTime(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Controlla se un'app è considerata "problematica" o da monitorare
   */
  isAppOfConcern(packageName: string): boolean {
    const concernApps = [
      'com.instagram.android',
      'com.snapchat.android',
      'com.zhiliaoapp.musically', // TikTok
      'com.facebook.katana',
      'com.twitter.android',
      'com.discord',
      'com.whatsapp',
      'com.google.android.youtube',
      'com.netflix.mediaclient',
      'com.spotify.music'
    ];

    return concernApps.includes(packageName);
  }
}

// Export singleton instance
export const appUsageService = new AppUsageService();
export default appUsageService;
