import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Stack } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import SOSButton from '../../components/SOSButton';
import { getChildSOSAlerts } from '../../utils/supabase';
import { formatDistanceToNow } from 'date-fns';
import { StatusBar } from 'expo-status-bar';
import { useTranslations } from '../../contexts/TranslationContext';

export default function SOSScreen() {
  const { user } = useAuth();
  const { t } = useTranslations();
  const [recentAlerts, setRecentAlerts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRecentAlerts();
  }, []);

  const fetchRecentAlerts = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const alerts = await getChildSOSAlerts(user.id);
      setRecentAlerts(alerts.slice(0, 5)); // Show only the 5 most recent alerts
    } catch (error) {
      console.error('Error fetching recent alerts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAlertSent = () => {
    // Refresh the list of alerts after sending a new one
    fetchRecentAlerts();
  };

  const renderRecentAlert = (alert: any) => {
    const timestamp = new Date(alert.timestamp);
    const timeAgo = formatDistanceToNow(timestamp, { addSuffix: true });

    return (
      <View key={alert.id} style={styles.alertItem}>
        <View style={styles.alertTimeContainer}>
          <Ionicons name="time-outline" size={18} color="#777" />
          <Text style={styles.alertTime}>{timeAgo}</Text>
        </View>

        <View style={styles.alertStatus}>
          <Ionicons
            name={alert.acknowledged ? "checkmark-circle" : "alert-circle"}
            size={18}
            color={alert.acknowledged ? "#34C759" : "#FF3B30"}
          />
          <Text style={[
            styles.statusText,
            {color: alert.acknowledged ? "#34C759" : "#FF3B30"}
          ]}>
            {alert.acknowledged ? (t.sos.acknowledged || "Acknowledged") : (t.sos.pending || "Pending")}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <>
      <StatusBar style="dark" />
      <Stack.Screen options={{ title: t.sos.emergencySOS || 'Emergency SOS', headerShown: true }} />

      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <View style={styles.sosSection}>
          <Text style={styles.instruction}>
            {t.sos.instructions || "Press the SOS button in case of emergency. Your location will be sent to your parent/guardian."}
          </Text>

          {user && <SOSButton childId={user.id} onAlertSent={handleAlertSent} />}
        </View>

        <View style={styles.historySection}>
          <Text style={styles.sectionTitle}>{t.sos.recentAlerts || "Recent SOS Alerts"}</Text>

          {loading ? (
            <ActivityIndicator size="small" color="#007AFF" style={styles.loader} />
          ) : recentAlerts.length > 0 ? (
            recentAlerts.map(alert => renderRecentAlert(alert))
          ) : (
            <Text style={styles.emptyText}>{t.sos.noAlerts || "No recent alerts"}</Text>
          )}
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  contentContainer: {
    padding: 16,
  },
  sosSection: {
    alignItems: 'center',
    marginBottom: 24,
    backgroundColor: '#FFF',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  instruction: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
    lineHeight: 22,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  historySection: {
    backgroundColor: '#FFF',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  alertItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EFEFEF',
  },
  alertTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  alertTime: {
    fontSize: 14,
    color: '#777',
    marginLeft: 6,
  },
  alertStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  loader: {
    padding: 20,
  },
  emptyText: {
    textAlign: 'center',
    color: '#999',
    padding: 20,
    fontSize: 16,
  },
});